import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import basicSsl from '@vitejs/plugin-basic-ssl'


// https://vitejs.dev/config/
export default defineConfig({
  base: '/ngs-weixin',

  build: {
    outDir: 'ngs-weixin', //指定打包输出路径
  },
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          // 在这里添加自定义的Less配置
        },
      },
    },
  },
  plugins: [
    vue(),
    vueJsx(),
    basicSsl(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: "0.0.0.0",
    https: true, //开启https配置
    proxy: {
      // Using the proxy instance
      '/ngs-api': {
        target: 'http://127.0.0.1:7901/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/ngs-api/, ''),
        configure: (proxy, options) => {

          // proxy will be an instance of 'http-proxy'
        },
      },
      '/item-image': {
        target: 'http://127.0.0.1:7901/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\//, '/'),
        configure: (proxy, options) => {

          // proxy will be an instance of 'http-proxy'
        },
      },
    }
  }
})
