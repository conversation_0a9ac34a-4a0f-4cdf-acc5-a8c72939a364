{"version": 3, "sources": ["../../node_modules/vuetify/src/framework.ts"], "sourcesContent": ["// Composables\nimport { createDate, DateAdapterSymbol, DateOptionsSymbol } from '@/composables/date/date'\nimport { createDefaults, DefaultsSymbol } from '@/composables/defaults'\nimport { createDisplay, DisplaySymbol } from '@/composables/display'\nimport { createGoTo, GoToSymbol } from '@/composables/goto'\nimport { createIcons, IconSymbol } from '@/composables/icons'\nimport { createLocale, LocaleSymbol } from '@/composables/locale'\nimport { createTheme, ThemeSymbol } from '@/composables/theme'\n\n// Utilities\nimport { nextTick, reactive } from 'vue'\nimport { defineComponent, getUid, IN_BROWSER, mergeDeep } from '@/util'\n\n// Types\nimport type { App, ComponentPublicInstance, InjectionKey } from 'vue'\nimport type { DateOptions } from '@/composables/date'\nimport type { DefaultsOptions } from '@/composables/defaults'\nimport type { DisplayOptions, SSROptions } from '@/composables/display'\nimport type { GoToOptions } from '@/composables/goto'\nimport type { IconOptions } from '@/composables/icons'\nimport type { LocaleOptions, RtlOptions } from '@/composables/locale'\nimport type { ThemeOptions } from '@/composables/theme'\nexport * from './composables'\nexport type { DateOptions, DateInstance, DateModule } from '@/composables/date'\n\nexport interface VuetifyOptions {\n  aliases?: Record<string, any>\n  blueprint?: Blueprint\n  components?: Record<string, any>\n  date?: DateOptions\n  directives?: Record<string, any>\n  defaults?: DefaultsOptions\n  display?: DisplayOptions\n  goTo?: GoToOptions\n  theme?: ThemeOptions\n  icons?: IconOptions\n  locale?: LocaleOptions & RtlOptions\n  ssr?: SSROptions\n}\n\nexport interface Blueprint extends Omit<VuetifyOptions, 'blueprint'> {}\n\nexport function createVuetify (vuetify: VuetifyOptions = {}) {\n  const { blueprint, ...rest } = vuetify\n  const options: VuetifyOptions = mergeDeep(blueprint, rest)\n  const {\n    aliases = {},\n    components = {},\n    directives = {},\n  } = options\n\n  const defaults = createDefaults(options.defaults)\n  const display = createDisplay(options.display, options.ssr)\n  const theme = createTheme(options.theme)\n  const icons = createIcons(options.icons)\n  const locale = createLocale(options.locale)\n  const date = createDate(options.date, locale)\n  const goTo = createGoTo(options.goTo, locale)\n\n  const install = (app: App) => {\n    for (const key in directives) {\n      app.directive(key, directives[key])\n    }\n\n    for (const key in components) {\n      app.component(key, components[key])\n    }\n\n    for (const key in aliases) {\n      app.component(key, defineComponent({\n        ...aliases[key],\n        name: key,\n        aliasName: aliases[key].name,\n      }))\n    }\n\n    theme.install(app)\n\n    app.provide(DefaultsSymbol, defaults)\n    app.provide(DisplaySymbol, display)\n    app.provide(ThemeSymbol, theme)\n    app.provide(IconSymbol, icons)\n    app.provide(LocaleSymbol, locale)\n    app.provide(DateOptionsSymbol, date.options)\n    app.provide(DateAdapterSymbol, date.instance)\n    app.provide(GoToSymbol, goTo)\n\n    if (IN_BROWSER && options.ssr) {\n      if (app.$nuxt) {\n        app.$nuxt.hook('app:suspense:resolve', () => {\n          display.update()\n        })\n      } else {\n        const { mount } = app\n        app.mount = (...args) => {\n          const vm = mount(...args)\n          nextTick(() => display.update())\n          app.mount = mount\n          return vm\n        }\n      }\n    }\n\n    getUid.reset()\n\n    if (typeof __VUE_OPTIONS_API__ !== 'boolean' || __VUE_OPTIONS_API__) {\n      app.mixin({\n        computed: {\n          $vuetify () {\n            return reactive({\n              defaults: inject.call(this, DefaultsSymbol),\n              display: inject.call(this, DisplaySymbol),\n              theme: inject.call(this, ThemeSymbol),\n              icons: inject.call(this, IconSymbol),\n              locale: inject.call(this, LocaleSymbol),\n              date: inject.call(this, DateAdapterSymbol),\n            })\n          },\n        },\n      })\n    }\n  }\n\n  return {\n    install,\n    defaults,\n    display,\n    theme,\n    icons,\n    locale,\n    date,\n    goTo,\n  }\n}\n\nexport const version = __VUETIFY_VERSION__\ncreateVuetify.version = version\n\n// Vue's inject() can only be used in setup\nfunction inject (this: ComponentPublicInstance, key: InjectionKey<any> | string) {\n  const vm = this.$\n\n  const provides = vm.parent?.provides ?? vm.vnode.appContext?.provides\n\n  if (provides && (key as any) in provides) {\n    return provides[(key as string)]\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CO,SAASA,gBAA6C;AAAA,MAA9BC,UAAuBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AACxD,QAAM;IAAEG;IAAW,GAAGC;EAAK,IAAIL;AAC/B,QAAMM,UAA0BC,UAAUH,WAAWC,IAAI;AACzD,QAAM;IACJG,UAAU,CAAC;IACXC,aAAa,CAAC;IACdC,aAAa,CAAC;EAChB,IAAIJ;AAEJ,QAAMK,WAAWC,eAAeN,QAAQK,QAAQ;AAChD,QAAME,UAAUC,cAAcR,QAAQO,SAASP,QAAQS,GAAG;AAC1D,QAAMC,QAAQC,YAAYX,QAAQU,KAAK;AACvC,QAAME,QAAQC,YAAYb,QAAQY,KAAK;AACvC,QAAME,SAASC,aAAaf,QAAQc,MAAM;AAC1C,QAAME,OAAOC,WAAWjB,QAAQgB,MAAMF,MAAM;AAC5C,QAAMI,OAAOC,WAAWnB,QAAQkB,MAAMJ,MAAM;AAE5C,QAAMM,UAAWC,SAAa;AAC5B,eAAWC,OAAOlB,YAAY;AAC5BiB,UAAIE,UAAUD,KAAKlB,WAAWkB,GAAG,CAAC;IACpC;AAEA,eAAWA,OAAOnB,YAAY;AAC5BkB,UAAIG,UAAUF,KAAKnB,WAAWmB,GAAG,CAAC;IACpC;AAEA,eAAWA,OAAOpB,SAAS;AACzBmB,UAAIG,UAAUF,KAAKG,gBAAgB;QACjC,GAAGvB,QAAQoB,GAAG;QACdI,MAAMJ;QACNK,WAAWzB,QAAQoB,GAAG,EAAEI;MAC1B,CAAC,CAAC;IACJ;AAEAhB,UAAMU,QAAQC,GAAG;AAEjBA,QAAIO,QAAQC,gBAAgBxB,QAAQ;AACpCgB,QAAIO,QAAQE,eAAevB,OAAO;AAClCc,QAAIO,QAAQG,aAAarB,KAAK;AAC9BW,QAAIO,QAAQI,YAAYpB,KAAK;AAC7BS,QAAIO,QAAQK,cAAcnB,MAAM;AAChCO,QAAIO,QAAQM,mBAAmBlB,KAAKhB,OAAO;AAC3CqB,QAAIO,QAAQO,mBAAmBnB,KAAKoB,QAAQ;AAC5Cf,QAAIO,QAAQS,YAAYnB,IAAI;AAE5B,QAAIoB,cAActC,QAAQS,KAAK;AAC7B,UAAIY,IAAIkB,OAAO;AACblB,YAAIkB,MAAMC,KAAK,wBAAwB,MAAM;AAC3CjC,kBAAQkC,OAAO;QACjB,CAAC;MACH,OAAO;AACL,cAAM;UAAEC;QAAM,IAAIrB;AAClBA,YAAIqB,QAAQ,WAAa;AACvB,gBAAMC,KAAKD,MAAM,GAAA/C,SAAO;AACxBiD,mBAAS,MAAMrC,QAAQkC,OAAO,CAAC;AAC/BpB,cAAIqB,QAAQA;AACZ,iBAAOC;QACT;MACF;IACF;AAEAE,WAAOC,MAAM;AAEb,QAAI,OAAOC,wBAAwB,aAAaA,qBAAqB;AACnE1B,UAAI2B,MAAM;QACRC,UAAU;UACRC,WAAY;AACV,mBAAOC,SAAS;cACd9C,UAAU+C,OAAOC,KAAK,MAAMxB,cAAc;cAC1CtB,SAAS6C,OAAOC,KAAK,MAAMvB,aAAa;cACxCpB,OAAO0C,OAAOC,KAAK,MAAMtB,WAAW;cACpCnB,OAAOwC,OAAOC,KAAK,MAAMrB,UAAU;cACnClB,QAAQsC,OAAOC,KAAK,MAAMpB,YAAY;cACtCjB,MAAMoC,OAAOC,KAAK,MAAMlB,iBAAiB;YAC3C,CAAC;UACH;QACF;MACF,CAAC;IACH;EACF;AAEA,SAAO;IACLf;IACAf;IACAE;IACAG;IACAE;IACAE;IACAE;IACAE;EACF;AACF;AAEO,IAAMoC,UAAO;AACpB7D,cAAc6D,UAAUA;AAGxB,SAASF,OAAuC9B,KAAiC;AA3IjF;AA4IE,QAAMqB,KAAK,KAAKY;AAEhB,QAAMC,aAAWb,QAAGc,WAAHd,mBAAWa,eAAYb,QAAGe,MAAMC,eAAThB,mBAAqBa;AAE7D,MAAIA,YAAalC,OAAekC,UAAU;AACxC,WAAOA,SAAUlC,GAAG;EACtB;AACF;", "names": ["createVuetify", "vuetify", "arguments", "length", "undefined", "blueprint", "rest", "options", "mergeDeep", "aliases", "components", "directives", "defaults", "createDefaults", "display", "createDisplay", "ssr", "theme", "createTheme", "icons", "createIcons", "locale", "createLocale", "date", "createDate", "goTo", "createGoTo", "install", "app", "key", "directive", "component", "defineComponent", "name", "<PERSON><PERSON><PERSON>", "provide", "DefaultsSymbol", "DisplaySymbol", "ThemeSymbol", "IconSymbol", "LocaleSymbol", "DateOptionsSymbol", "DateAdapterSymbol", "instance", "GoToSymbol", "IN_BROWSER", "$nuxt", "hook", "update", "mount", "vm", "nextTick", "getUid", "reset", "__VUE_OPTIONS_API__", "mixin", "computed", "$vuetify", "reactive", "inject", "call", "version", "$", "provides", "parent", "vnode", "appContext"]}