{"version": 3, "sources": ["../../node_modules/vuetify/src/composables/date/adapters/vuetify.ts", "../../node_modules/vuetify/src/composables/date/date.ts", "../../node_modules/vuetify/src/composables/goto.ts", "../../node_modules/vuetify/src/iconsets/mdi.ts", "../../node_modules/vuetify/src/composables/icons.tsx", "../../node_modules/vuetify/src/composables/resizeObserver.ts", "../../node_modules/vuetify/src/composables/layout.ts"], "sourcesContent": ["// Utilities\nimport { createRange, padStart } from '@/util'\n\n// Types\nimport type { DateAdapter } from '../DateAdapter'\n\ntype CustomDateFormat = Intl.DateTimeFormatOptions | ((date: Date, formatString: string, locale: string) => string)\n\nconst firstDay: Record<string, number> = {\n  '001': 1,\n  AD: 1,\n  AE: 6,\n  AF: 6,\n  AG: 0,\n  AI: 1,\n  AL: 1,\n  AM: 1,\n  AN: 1,\n  AR: 1,\n  AS: 0,\n  AT: 1,\n  AU: 1,\n  AX: 1,\n  AZ: 1,\n  BA: 1,\n  BD: 0,\n  BE: 1,\n  BG: 1,\n  BH: 6,\n  BM: 1,\n  BN: 1,\n  BR: 0,\n  BS: 0,\n  BT: 0,\n  BW: 0,\n  BY: 1,\n  BZ: 0,\n  CA: 0,\n  CH: 1,\n  CL: 1,\n  CM: 1,\n  CN: 1,\n  CO: 0,\n  CR: 1,\n  CY: 1,\n  CZ: 1,\n  DE: 1,\n  DJ: 6,\n  DK: 1,\n  DM: 0,\n  DO: 0,\n  DZ: 6,\n  EC: 1,\n  EE: 1,\n  EG: 6,\n  ES: 1,\n  ET: 0,\n  FI: 1,\n  FJ: 1,\n  FO: 1,\n  FR: 1,\n  GB: 1,\n  'GB-alt-variant': 0,\n  GE: 1,\n  GF: 1,\n  GP: 1,\n  GR: 1,\n  GT: 0,\n  GU: 0,\n  HK: 0,\n  HN: 0,\n  HR: 1,\n  HU: 1,\n  ID: 0,\n  IE: 1,\n  IL: 0,\n  IN: 0,\n  IQ: 6,\n  IR: 6,\n  IS: 1,\n  IT: 1,\n  JM: 0,\n  JO: 6,\n  JP: 0,\n  KE: 0,\n  KG: 1,\n  KH: 0,\n  KR: 0,\n  KW: 6,\n  KZ: 1,\n  LA: 0,\n  LB: 1,\n  LI: 1,\n  LK: 1,\n  LT: 1,\n  LU: 1,\n  LV: 1,\n  LY: 6,\n  MC: 1,\n  MD: 1,\n  ME: 1,\n  MH: 0,\n  MK: 1,\n  MM: 0,\n  MN: 1,\n  MO: 0,\n  MQ: 1,\n  MT: 0,\n  MV: 5,\n  MX: 0,\n  MY: 1,\n  MZ: 0,\n  NI: 0,\n  NL: 1,\n  NO: 1,\n  NP: 0,\n  NZ: 1,\n  OM: 6,\n  PA: 0,\n  PE: 0,\n  PH: 0,\n  PK: 0,\n  PL: 1,\n  PR: 0,\n  PT: 0,\n  PY: 0,\n  QA: 6,\n  RE: 1,\n  RO: 1,\n  RS: 1,\n  RU: 1,\n  SA: 0,\n  SD: 6,\n  SE: 1,\n  SG: 0,\n  SI: 1,\n  SK: 1,\n  SM: 1,\n  SV: 0,\n  SY: 6,\n  TH: 0,\n  TJ: 1,\n  TM: 1,\n  TR: 1,\n  TT: 0,\n  TW: 0,\n  UA: 1,\n  UM: 0,\n  US: 0,\n  UY: 1,\n  UZ: 1,\n  VA: 1,\n  VE: 0,\n  VI: 0,\n  VN: 1,\n  WS: 0,\n  XK: 1,\n  YE: 0,\n  ZA: 0,\n  ZW: 0,\n}\n\nfunction getWeekArray (date: Date, locale: string, firstDayOfWeek?: number) {\n  const weeks = []\n  let currentWeek = []\n  const firstDayOfMonth = startOfMonth(date)\n  const lastDayOfMonth = endOfMonth(date)\n  const first = firstDayOfWeek ?? firstDay[locale.slice(-2).toUpperCase()] ?? 0\n  const firstDayWeekIndex = (firstDayOfMonth.getDay() - first + 7) % 7\n  const lastDayWeekIndex = (lastDayOfMonth.getDay() - first + 7) % 7\n\n  for (let i = 0; i < firstDayWeekIndex; i++) {\n    const adjacentDay = new Date(firstDayOfMonth)\n    adjacentDay.setDate(adjacentDay.getDate() - (firstDayWeekIndex - i))\n    currentWeek.push(adjacentDay)\n  }\n\n  for (let i = 1; i <= lastDayOfMonth.getDate(); i++) {\n    const day = new Date(date.getFullYear(), date.getMonth(), i)\n\n    // Add the day to the current week\n    currentWeek.push(day)\n\n    // If the current week has 7 days, add it to the weeks array and start a new week\n    if (currentWeek.length === 7) {\n      weeks.push(currentWeek)\n      currentWeek = []\n    }\n  }\n\n  for (let i = 1; i < 7 - lastDayWeekIndex; i++) {\n    const adjacentDay = new Date(lastDayOfMonth)\n    adjacentDay.setDate(adjacentDay.getDate() + i)\n    currentWeek.push(adjacentDay)\n  }\n\n  if (currentWeek.length > 0) {\n    weeks.push(currentWeek)\n  }\n\n  return weeks\n}\n\nfunction startOfWeek (date: Date, locale: string, firstDayOfWeek?: number) {\n  const day = firstDayOfWeek ?? firstDay[locale.slice(-2).toUpperCase()] ?? 0\n\n  const d = new Date(date)\n  while (d.getDay() !== day) {\n    d.setDate(d.getDate() - 1)\n  }\n  return d\n}\n\nfunction endOfWeek (date: Date, locale: string) {\n  const d = new Date(date)\n  const lastDay = ((firstDay[locale.slice(-2).toUpperCase()] ?? 0) + 6) % 7\n  while (d.getDay() !== lastDay) {\n    d.setDate(d.getDate() + 1)\n  }\n  return d\n}\n\nfunction startOfMonth (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth(), 1)\n}\n\nfunction endOfMonth (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth() + 1, 0)\n}\n\nfunction parseLocalDate (value: string): Date {\n  const parts = value.split('-').map(Number)\n\n  // new Date() uses local time zone when passing individual date component values\n  return new Date(parts[0], parts[1] - 1, parts[2])\n}\n\nconst _YYYMMDD = /^([12]\\d{3}-([1-9]|0[1-9]|1[0-2])-([1-9]|0[1-9]|[12]\\d|3[01]))$/\n\nfunction date (value?: any): Date | null {\n  if (value == null) return new Date()\n\n  if (value instanceof Date) return value\n\n  if (typeof value === 'string') {\n    let parsed\n\n    if (_YYYMMDD.test(value)) {\n      return parseLocalDate(value)\n    } else {\n      parsed = Date.parse(value)\n    }\n\n    if (!isNaN(parsed)) return new Date(parsed)\n  }\n\n  return null\n}\n\nconst sundayJanuarySecond2000 = new Date(2000, 0, 2)\n\nfunction getWeekdays (locale: string, firstDayOfWeek?: number) {\n  const daysFromSunday = firstDayOfWeek ?? firstDay[locale.slice(-2).toUpperCase()] ?? 0\n\n  return createRange(7).map(i => {\n    const weekday = new Date(sundayJanuarySecond2000)\n    weekday.setDate(sundayJanuarySecond2000.getDate() + daysFromSunday + i)\n    return new Intl.DateTimeFormat(locale, { weekday: 'narrow' }).format(weekday)\n  })\n}\n\nfunction format (\n  value: Date,\n  formatString: string,\n  locale: string,\n  formats?: Record<string, CustomDateFormat>\n): string {\n  const newDate = date(value) ?? new Date()\n  const customFormat = formats?.[formatString]\n\n  if (typeof customFormat === 'function') {\n    return customFormat(newDate, formatString, locale)\n  }\n\n  let options: Intl.DateTimeFormatOptions = {}\n  switch (formatString) {\n    case 'fullDate':\n      options = { year: 'numeric', month: 'long', day: 'numeric' }\n      break\n    case 'fullDateWithWeekday':\n      options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }\n      break\n    case 'normalDate':\n      const day = newDate.getDate()\n      const month = new Intl.DateTimeFormat(locale, { month: 'long' }).format(newDate)\n      return `${day} ${month}`\n    case 'normalDateWithWeekday':\n      options = { weekday: 'short', day: 'numeric', month: 'short' }\n      break\n    case 'shortDate':\n      options = { month: 'short', day: 'numeric' }\n      break\n    case 'year':\n      options = { year: 'numeric' }\n      break\n    case 'month':\n      options = { month: 'long' }\n      break\n    case 'monthShort':\n      options = { month: 'short' }\n      break\n    case 'monthAndYear':\n      options = { month: 'long', year: 'numeric' }\n      break\n    case 'monthAndDate':\n      options = { month: 'long', day: 'numeric' }\n      break\n    case 'weekday':\n      options = { weekday: 'long' }\n      break\n    case 'weekdayShort':\n      options = { weekday: 'short' }\n      break\n    case 'dayOfMonth':\n      return new Intl.NumberFormat(locale).format(newDate.getDate())\n    case 'hours12h':\n      options = { hour: 'numeric', hour12: true }\n      break\n    case 'hours24h':\n      options = { hour: 'numeric', hour12: false }\n      break\n    case 'minutes':\n      options = { minute: 'numeric' }\n      break\n    case 'seconds':\n      options = { second: 'numeric' }\n      break\n    case 'fullTime':\n      options = { hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: true }\n      break\n    case 'fullTime12h':\n      options = { hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: true }\n      break\n    case 'fullTime24h':\n      options = { hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: false }\n      break\n    case 'fullDateTime':\n      options = { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: true }\n      break\n    case 'fullDateTime12h':\n      options = { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: true }\n      break\n    case 'fullDateTime24h':\n      options = { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: false }\n      break\n    case 'keyboardDate':\n      options = { year: 'numeric', month: '2-digit', day: '2-digit' }\n      break\n    case 'keyboardDateTime':\n      options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: false }\n      break\n    case 'keyboardDateTime12h':\n      options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: true }\n      break\n    case 'keyboardDateTime24h':\n      options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: false }\n      break\n    default:\n      options = customFormat ?? { timeZone: 'UTC', timeZoneName: 'short' }\n  }\n\n  return new Intl.DateTimeFormat(locale, options).format(newDate)\n}\n\nfunction toISO (adapter: DateAdapter<any>, value: Date) {\n  const date = adapter.toJsDate(value)\n  const year = date.getFullYear()\n  const month = padStart(String(date.getMonth() + 1), 2, '0')\n  const day = padStart(String(date.getDate()), 2, '0')\n\n  return `${year}-${month}-${day}`\n}\n\nfunction parseISO (value: string) {\n  const [year, month, day] = value.split('-').map(Number)\n\n  return new Date(year, month - 1, day)\n}\n\nfunction addMinutes (date: Date, amount: number) {\n  const d = new Date(date)\n  d.setMinutes(d.getMinutes() + amount)\n  return d\n}\n\nfunction addHours (date: Date, amount: number) {\n  const d = new Date(date)\n  d.setHours(d.getHours() + amount)\n  return d\n}\n\nfunction addDays (date: Date, amount: number) {\n  const d = new Date(date)\n  d.setDate(d.getDate() + amount)\n  return d\n}\n\nfunction addWeeks (date: Date, amount: number) {\n  const d = new Date(date)\n  d.setDate(d.getDate() + (amount * 7))\n  return d\n}\n\nfunction addMonths (date: Date, amount: number) {\n  const d = new Date(date)\n  d.setDate(1)\n  d.setMonth(d.getMonth() + amount)\n  return d\n}\n\nfunction getYear (date: Date) {\n  return date.getFullYear()\n}\n\nfunction getMonth (date: Date) {\n  return date.getMonth()\n}\n\nfunction getDate (date: Date) {\n  return date.getDate()\n}\n\nfunction getNextMonth (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth() + 1, 1)\n}\n\nfunction getPreviousMonth (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth() - 1, 1)\n}\n\nfunction getHours (date: Date) {\n  return date.getHours()\n}\n\nfunction getMinutes (date: Date) {\n  return date.getMinutes()\n}\n\nfunction startOfYear (date: Date) {\n  return new Date(date.getFullYear(), 0, 1)\n}\nfunction endOfYear (date: Date) {\n  return new Date(date.getFullYear(), 11, 31)\n}\n\nfunction isWithinRange (date: Date, range: [Date, Date]) {\n  return isAfter(date, range[0]) && isBefore(date, range[1])\n}\n\nfunction isValid (date: any) {\n  const d = new Date(date)\n\n  return d instanceof Date && !isNaN(d.getTime())\n}\n\nfunction isAfter (date: Date, comparing: Date) {\n  return date.getTime() > comparing.getTime()\n}\n\nfunction isAfterDay (date: Date, comparing: Date): boolean {\n  return isAfter(startOfDay(date), startOfDay(comparing))\n}\n\nfunction isBefore (date: Date, comparing: Date) {\n  return date.getTime() < comparing.getTime()\n}\n\nfunction isEqual (date: Date, comparing: Date) {\n  return date.getTime() === comparing.getTime()\n}\n\nfunction isSameDay (date: Date, comparing: Date) {\n  return date.getDate() === comparing.getDate() &&\n    date.getMonth() === comparing.getMonth() &&\n    date.getFullYear() === comparing.getFullYear()\n}\n\nfunction isSameMonth (date: Date, comparing: Date) {\n  return date.getMonth() === comparing.getMonth() &&\n    date.getFullYear() === comparing.getFullYear()\n}\n\nfunction isSameYear (date: Date, comparing: Date) {\n  return date.getFullYear() === comparing.getFullYear()\n}\n\nfunction getDiff (date: Date, comparing: Date | string, unit?: string) {\n  const d = new Date(date)\n  const c = new Date(comparing)\n\n  switch (unit) {\n    case 'years':\n      return d.getFullYear() - c.getFullYear()\n    case 'quarters':\n      return Math.floor((d.getMonth() - c.getMonth() + (d.getFullYear() - c.getFullYear()) * 12) / 4)\n    case 'months':\n      return d.getMonth() - c.getMonth() + (d.getFullYear() - c.getFullYear()) * 12\n    case 'weeks':\n      return Math.floor((d.getTime() - c.getTime()) / (1000 * 60 * 60 * 24 * 7))\n    case 'days':\n      return Math.floor((d.getTime() - c.getTime()) / (1000 * 60 * 60 * 24))\n    case 'hours':\n      return Math.floor((d.getTime() - c.getTime()) / (1000 * 60 * 60))\n    case 'minutes':\n      return Math.floor((d.getTime() - c.getTime()) / (1000 * 60))\n    case 'seconds':\n      return Math.floor((d.getTime() - c.getTime()) / 1000)\n    default: {\n      return d.getTime() - c.getTime()\n    }\n  }\n}\n\nfunction setHours (date: Date, count: number) {\n  const d = new Date(date)\n  d.setHours(count)\n  return d\n}\n\nfunction setMinutes (date: Date, count: number) {\n  const d = new Date(date)\n  d.setMinutes(count)\n  return d\n}\n\nfunction setMonth (date: Date, count: number) {\n  const d = new Date(date)\n  d.setMonth(count)\n  return d\n}\n\nfunction setDate (date: Date, day: number) {\n  const d = new Date(date)\n  d.setDate(day)\n  return d\n}\n\nfunction setYear (date: Date, year: number) {\n  const d = new Date(date)\n  d.setFullYear(year)\n  return d\n}\n\nfunction startOfDay (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0)\n}\n\nfunction endOfDay (date: Date) {\n  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999)\n}\n\nexport class VuetifyDateAdapter implements DateAdapter<Date> {\n  locale: string\n  formats?: Record<string, CustomDateFormat>\n\n  constructor (options: { locale: string, formats?: Record<string, CustomDateFormat> }) {\n    this.locale = options.locale\n    this.formats = options.formats\n  }\n\n  date (value?: any) {\n    return date(value)\n  }\n\n  toJsDate (date: Date) {\n    return date\n  }\n\n  toISO (date: Date): string {\n    return toISO(this, date)\n  }\n\n  parseISO (date: string) {\n    return parseISO(date)\n  }\n\n  addMinutes (date: Date, amount: number) {\n    return addMinutes(date, amount)\n  }\n\n  addHours (date: Date, amount: number) {\n    return addHours(date, amount)\n  }\n\n  addDays (date: Date, amount: number) {\n    return addDays(date, amount)\n  }\n\n  addWeeks (date: Date, amount: number) {\n    return addWeeks(date, amount)\n  }\n\n  addMonths (date: Date, amount: number) {\n    return addMonths(date, amount)\n  }\n\n  getWeekArray (date: Date, firstDayOfWeek?: number | string) {\n    return getWeekArray(date, this.locale, firstDayOfWeek ? Number(firstDayOfWeek) : undefined)\n  }\n\n  startOfWeek (date: Date, firstDayOfWeek?: number | string): Date {\n    return startOfWeek(date, this.locale, firstDayOfWeek ? Number(firstDayOfWeek) : undefined)\n  }\n\n  endOfWeek (date: Date): Date {\n    return endOfWeek(date, this.locale)\n  }\n\n  startOfMonth (date: Date) {\n    return startOfMonth(date)\n  }\n\n  endOfMonth (date: Date) {\n    return endOfMonth(date)\n  }\n\n  format (date: Date, formatString: string) {\n    return format(date, formatString, this.locale, this.formats)\n  }\n\n  isEqual (date: Date, comparing: Date) {\n    return isEqual(date, comparing)\n  }\n\n  isValid (date: any) {\n    return isValid(date)\n  }\n\n  isWithinRange (date: Date, range: [Date, Date]) {\n    return isWithinRange(date, range)\n  }\n\n  isAfter (date: Date, comparing: Date) {\n    return isAfter(date, comparing)\n  }\n\n  isAfterDay (date: Date, comparing: Date) {\n    return isAfterDay(date, comparing)\n  }\n\n  isBefore (date: Date, comparing: Date) {\n    return !isAfter(date, comparing) && !isEqual(date, comparing)\n  }\n\n  isSameDay (date: Date, comparing: Date) {\n    return isSameDay(date, comparing)\n  }\n\n  isSameMonth (date: Date, comparing: Date) {\n    return isSameMonth(date, comparing)\n  }\n\n  isSameYear (date: Date, comparing: Date) {\n    return isSameYear(date, comparing)\n  }\n\n  setMinutes (date: Date, count: number) {\n    return setMinutes(date, count)\n  }\n\n  setHours (date: Date, count: number) {\n    return setHours(date, count)\n  }\n\n  setMonth (date: Date, count: number) {\n    return setMonth(date, count)\n  }\n\n  setDate (date: Date, day: number): Date {\n    return setDate(date, day)\n  }\n\n  setYear (date: Date, year: number) {\n    return setYear(date, year)\n  }\n\n  getDiff (date: Date, comparing: Date | string, unit?: string) {\n    return getDiff(date, comparing, unit)\n  }\n\n  getWeekdays (firstDayOfWeek?: number | string) {\n    return getWeekdays(this.locale, firstDayOfWeek ? Number(firstDayOfWeek) : undefined)\n  }\n\n  getYear (date: Date) {\n    return getYear(date)\n  }\n\n  getMonth (date: Date) {\n    return getMonth(date)\n  }\n\n  getDate (date: Date) {\n    return getDate(date)\n  }\n\n  getNextMonth (date: Date) {\n    return getNextMonth(date)\n  }\n\n  getPreviousMonth (date: Date) {\n    return getPreviousMonth(date)\n  }\n\n  getHours (date: Date) {\n    return getHours(date)\n  }\n\n  getMinutes (date: Date) {\n    return getMinutes(date)\n  }\n\n  startOfDay (date: Date) {\n    return startOfDay(date)\n  }\n\n  endOfDay (date: Date) {\n    return endOfDay(date)\n  }\n\n  startOfYear (date: Date) {\n    return startOfYear(date)\n  }\n\n  endOfYear (date: Date) {\n    return endOfYear(date)\n  }\n}\n", "// Composables\nimport { useLocale } from '@/composables/locale'\n\n// Utilities\nimport { inject, reactive, watch } from 'vue'\nimport { mergeDeep } from '@/util'\n\n// Types\nimport type { InjectionKey } from 'vue'\nimport type { DateAdapter } from './DateAdapter'\nimport type { LocaleInstance } from '@/composables/locale'\n\n// Adapters\nimport { VuetifyDateAdapter } from './adapters/vuetify'\n\nexport interface DateInstance extends DateModule.InternalAdapter {\n  locale?: any\n}\n\n/** Supports module augmentation to specify date adapter types */\nexport namespace DateModule {\n  interface Adapter {}\n\n  export type InternalAdapter = {} extends Adapter ? DateAdapter : Adapter\n}\n\nexport type InternalDateOptions = {\n  adapter: (new (options: { locale: any, formats?: any }) => DateInstance) | DateInstance\n  formats?: Record<string, any>\n  locale: Record<string, any>\n}\n\nexport type DateOptions = Partial<InternalDateOptions>\n\nexport const DateOptionsSymbol: InjectionKey<InternalDateOptions> = Symbol.for('vuetify:date-options')\nexport const DateAdapterSymbol: InjectionKey<DateInstance> = Symbol.for('vuetify:date-adapter')\n\nexport function createDate (options: DateOptions | undefined, locale: LocaleInstance) {\n  const _options = mergeDeep({\n    adapter: VuetifyDateAdapter,\n    locale: {\n      af: 'af-ZA',\n      // ar: '', # not the same value for all variants\n      bg: 'bg-BG',\n      ca: 'ca-ES',\n      ckb: '',\n      cs: 'cs-CZ',\n      de: 'de-DE',\n      el: 'el-GR',\n      en: 'en-US',\n      // es: '', # not the same value for all variants\n      et: 'et-EE',\n      fa: 'fa-IR',\n      fi: 'fi-FI',\n      // fr: '', #not the same value for all variants\n      hr: 'hr-HR',\n      hu: 'hu-HU',\n      he: 'he-IL',\n      id: 'id-ID',\n      it: 'it-IT',\n      ja: 'ja-JP',\n      ko: 'ko-KR',\n      lv: 'lv-LV',\n      lt: 'lt-LT',\n      nl: 'nl-NL',\n      no: 'no-NO',\n      pl: 'pl-PL',\n      pt: 'pt-PT',\n      ro: 'ro-RO',\n      ru: 'ru-RU',\n      sk: 'sk-SK',\n      sl: 'sl-SI',\n      srCyrl: 'sr-SP',\n      srLatn: 'sr-SP',\n      sv: 'sv-SE',\n      th: 'th-TH',\n      tr: 'tr-TR',\n      az: 'az-AZ',\n      uk: 'uk-UA',\n      vi: 'vi-VN',\n      zhHans: 'zh-CN',\n      zhHant: 'zh-TW',\n    },\n  }, options) as InternalDateOptions\n\n  return {\n    options: _options,\n    instance: createInstance(_options, locale),\n  }\n}\n\nfunction createInstance (options: InternalDateOptions, locale: LocaleInstance) {\n  const instance = reactive(\n    typeof options.adapter === 'function'\n      // eslint-disable-next-line new-cap\n      ? new options.adapter({\n        locale: options.locale[locale.current.value] ?? locale.current.value,\n        formats: options.formats,\n      })\n      : options.adapter\n  )\n\n  watch(locale.current, value => {\n    instance.locale = options.locale[value] ?? value ?? instance.locale\n  })\n\n  return instance\n}\n\nexport function useDate (): DateInstance {\n  const options = inject(DateOptionsSymbol)\n\n  if (!options) throw new Error('[Vuetify] Could not find injected date options')\n\n  const locale = useLocale()\n\n  return createInstance(options, locale)\n}\n\n// https://stackoverflow.com/questions/274861/how-do-i-calculate-the-week-number-given-a-date/275024#275024\nexport function getWeek (adapter: DateAdapter<any>, value: any) {\n  const date = adapter.toJsDate(value)\n  let year = date.getFullYear()\n  let d1w1 = new Date(year, 0, 1)\n\n  if (date < d1w1) {\n    year = year - 1\n    d1w1 = new Date(year, 0, 1)\n  } else {\n    const tv = new Date(year + 1, 0, 1)\n    if (date >= tv) {\n      year = year + 1\n      d1w1 = tv\n    }\n  }\n\n  const diffTime = Math.abs(date.getTime() - d1w1.getTime())\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n\n  return Math.floor(diffDays / 7) + 1\n}\n", "// Utilities\nimport { computed, inject } from 'vue'\nimport { useRtl } from './locale'\nimport { clamp, consoleWarn, mergeDeep, refElement } from '@/util'\n\n// Types\nimport type { ComponentPublicInstance, InjectionKey, Ref } from 'vue'\nimport type { LocaleInstance, RtlInstance } from './locale'\n\nexport interface GoToInstance {\n  rtl: Ref<boolean>\n  options: InternalGoToOptions\n}\n\nexport interface InternalGoToOptions {\n  container: ComponentPublicInstance | HTMLElement | string\n  duration: number\n  layout: boolean\n  offset: number\n  easing: string | ((t: number) => number)\n  patterns: Record<string, (t: number) => number>\n}\n\nexport type GoToOptions = Partial<InternalGoToOptions>\n\nexport const GoToSymbol: InjectionKey<GoToInstance> = Symbol.for('vuetify:goto')\n\nfunction genDefaults () {\n  return {\n    container: undefined,\n    duration: 300,\n    layout: false,\n    offset: 0,\n    easing: 'easeInOutCubic',\n    patterns: {\n      linear: (t: number) => t,\n      easeInQuad: (t: number) => t ** 2,\n      easeOutQuad: (t: number) => t * (2 - t),\n      easeInOutQuad: (t: number) => (t < 0.5 ? 2 * t ** 2 : -1 + (4 - 2 * t) * t),\n      easeInCubic: (t: number) => t ** 3,\n      easeOutCubic: (t: number) => --t ** 3 + 1,\n      easeInOutCubic: (t: number) => t < 0.5 ? 4 * t ** 3 : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,\n      easeInQuart: (t: number) => t ** 4,\n      easeOutQuart: (t: number) => 1 - --t ** 4,\n      easeInOutQuart: (t: number) => (t < 0.5 ? 8 * t ** 4 : 1 - 8 * --t ** 4),\n      easeInQuint: (t: number) => t ** 5,\n      easeOutQuint: (t: number) => 1 + --t ** 5,\n      easeInOutQuint: (t: number) => t < 0.5 ? 16 * t ** 5 : 1 + 16 * --t ** 5,\n    },\n  }\n}\n\nfunction getContainer (el?: ComponentPublicInstance | HTMLElement | string) {\n  return getTarget(el) ?? (document.scrollingElement || document.body) as HTMLElement\n}\n\nfunction getTarget (el: ComponentPublicInstance | HTMLElement | string | undefined) {\n  return (typeof el === 'string') ? document.querySelector<HTMLElement>(el) : refElement(el)\n}\n\nfunction getOffset (target: any, horizontal?: boolean, rtl?: boolean): number {\n  if (typeof target === 'number') return horizontal && rtl ? -target : target\n\n  let el = getTarget(target)\n  let totalOffset = 0\n  while (el) {\n    totalOffset += horizontal ? el.offsetLeft : el.offsetTop\n    el = el.offsetParent as HTMLElement\n  }\n\n  return totalOffset\n}\n\nexport function createGoTo (\n  options: GoToOptions| undefined,\n  locale: LocaleInstance & RtlInstance\n): GoToInstance {\n  return {\n    rtl: locale.isRtl,\n    options: mergeDeep(genDefaults(), options) as InternalGoToOptions,\n  }\n}\n\nexport async function scrollTo (\n  _target: ComponentPublicInstance | HTMLElement | number | string,\n  _options: GoToOptions,\n  horizontal?: boolean,\n  goTo?: GoToInstance,\n) {\n  const property = horizontal ? 'scrollLeft' : 'scrollTop'\n  const options = mergeDeep(goTo?.options ?? genDefaults(), _options)\n  const rtl = goTo?.rtl.value\n  const target = (typeof _target === 'number' ? _target : getTarget(_target)) ?? 0\n  const container = options.container === 'parent' && target instanceof HTMLElement\n    ? target.parentElement!\n    : getContainer(options.container)\n  const ease = typeof options.easing === 'function' ? options.easing : options.patterns[options.easing]\n\n  if (!ease) throw new TypeError(`Easing function \"${options.easing}\" not found.`)\n\n  let targetLocation: number\n  if (typeof target === 'number') {\n    targetLocation = getOffset(target, horizontal, rtl)\n  } else {\n    targetLocation = getOffset(target, horizontal, rtl) - getOffset(container, horizontal, rtl)\n\n    if (options.layout) {\n      const styles = window.getComputedStyle(target)\n      const layoutOffset = styles.getPropertyValue('--v-layout-top')\n\n      if (layoutOffset) targetLocation -= parseInt(layoutOffset, 10)\n    }\n  }\n\n  targetLocation += options.offset\n  targetLocation = clampTarget(container, targetLocation, !!rtl, !!horizontal)\n\n  const startLocation = container[property] ?? 0\n\n  if (targetLocation === startLocation) return Promise.resolve(targetLocation)\n\n  const startTime = performance.now()\n\n  return new Promise(resolve => requestAnimationFrame(function step (currentTime: number) {\n    const timeElapsed = currentTime - startTime\n    const progress = timeElapsed / options.duration\n    const location = Math.floor(\n      startLocation +\n      (targetLocation - startLocation) *\n      ease(clamp(progress, 0, 1))\n    )\n\n    container[property] = location\n\n    // Allow for some jitter if target time has elapsed\n    if (progress >= 1 && Math.abs(location - container[property]) < 10) {\n      return resolve(targetLocation)\n    } else if (progress > 2) {\n      // The target might not be reachable\n      consoleWarn('Scroll target is not reachable')\n      return resolve(container[property])\n    }\n\n    requestAnimationFrame(step)\n  }))\n}\n\nexport function useGoTo (_options: GoToOptions = {}) {\n  const goToInstance = inject(GoToSymbol)\n  const { isRtl } = useRtl()\n\n  if (!goToInstance) throw new Error('[Vuetify] Could not find injected goto instance')\n\n  const goTo = {\n    ...goToInstance,\n    // can be set via VLocaleProvider\n    rtl: computed(() => goToInstance.rtl.value || isRtl.value),\n  }\n\n  async function go (\n    target: ComponentPublicInstance | HTMLElement | string | number,\n    options?: Partial<GoToOptions>,\n  ) {\n    return scrollTo(target, mergeDeep(_options, options), false, goTo)\n  }\n\n  go.horizontal = async (\n    target: ComponentPublicInstance | HTMLElement | string | number,\n    options?: Partial<GoToOptions>,\n  ) => {\n    return scrollTo(target, mergeDeep(_options, options), true, goTo)\n  }\n\n  return go\n}\n\n/**\n * Clamp target value to achieve a smooth scroll animation\n * when the value goes outside the scroll container size\n */\nfunction clampTarget (\n  container: HTMLElement,\n  value: number,\n  rtl: boolean,\n  horizontal: boolean,\n) {\n  const { scrollWidth, scrollHeight } = container\n  const [containerWidth, containerHeight] = container === document.scrollingElement\n    ? [window.innerWidth, window.innerHeight]\n    : [container.offsetWidth, container.offsetHeight]\n\n  let min: number\n  let max: number\n\n  if (horizontal) {\n    if (rtl) {\n      min = -(scrollWidth - containerWidth)\n      max = 0\n    } else {\n      min = 0\n      max = scrollWidth - containerWidth\n    }\n  } else {\n    min = 0\n    max = scrollHeight + -containerHeight\n  }\n\n  return Math.max(Math.min(value, max), min)\n}\n", "// Composables\nimport { VClassIcon } from '@/composables/icons'\n\n// Utilities\nimport { h } from 'vue'\n\n// Types\nimport type { IconAliases, IconSet } from '@/composables/icons'\n\nconst aliases: IconAliases = {\n  collapse: 'mdi-chevron-up',\n  complete: 'mdi-check',\n  cancel: 'mdi-close-circle',\n  close: 'mdi-close',\n  delete: 'mdi-close-circle', // delete (e.g. v-chip close)\n  clear: 'mdi-close-circle',\n  success: 'mdi-check-circle',\n  info: 'mdi-information',\n  warning: 'mdi-alert-circle',\n  error: 'mdi-close-circle',\n  prev: 'mdi-chevron-left',\n  next: 'mdi-chevron-right',\n  checkboxOn: 'mdi-checkbox-marked',\n  checkboxOff: 'mdi-checkbox-blank-outline',\n  checkboxIndeterminate: 'mdi-minus-box',\n  delimiter: 'mdi-circle', // for carousel\n  sortAsc: 'mdi-arrow-up',\n  sortDesc: 'mdi-arrow-down',\n  expand: 'mdi-chevron-down',\n  menu: 'mdi-menu',\n  subgroup: 'mdi-menu-down',\n  dropdown: 'mdi-menu-down',\n  radioOn: 'mdi-radiobox-marked',\n  radioOff: 'mdi-radiobox-blank',\n  edit: 'mdi-pencil',\n  ratingEmpty: 'mdi-star-outline',\n  ratingFull: 'mdi-star',\n  ratingHalf: 'mdi-star-half-full',\n  loading: 'mdi-cached',\n  first: 'mdi-page-first',\n  last: 'mdi-page-last',\n  unfold: 'mdi-unfold-more-horizontal',\n  file: 'mdi-paperclip',\n  plus: 'mdi-plus',\n  minus: 'mdi-minus',\n  calendar: 'mdi-calendar',\n  treeviewCollapse: 'mdi-menu-down',\n  treeviewExpand: 'mdi-menu-right',\n  eyeDropper: 'mdi-eyedropper',\n}\n\nconst mdi: IconSet = {\n  // Not using mergeProps here, functional components merge props by default (?)\n  component: (props: any) => h(VClassIcon, { ...props, class: 'mdi' }),\n}\n\nexport { aliases, mdi }\n", "// Icons\nimport { aliases, mdi } from '@/iconsets/mdi'\n\n// Utilities\nimport { computed, inject, unref } from 'vue'\nimport { consoleWarn, defineComponent, genericComponent, mergeDeep, propsFactory } from '@/util'\n\n// Types\nimport type { ComponentPublicInstance, FunctionalComponent, InjectionKey, PropType, Ref } from 'vue'\n\nexport type JSXComponent<Props = any> =\n  | { new (): ComponentPublicInstance<Props> }\n  | FunctionalComponent<Props>\n\nexport type IconValue =\n  | string\n  | (string | [path: string, opacity: number])[]\n  | JSXComponent\nexport const IconValue = [String, Function, Object, Array] as PropType<IconValue>\n\nexport interface IconAliases {\n  [name: string]: IconValue\n  complete: IconValue\n  cancel: IconValue\n  close: IconValue\n  delete: IconValue\n  clear: IconValue\n  success: IconValue\n  info: IconValue\n  warning: IconValue\n  error: IconValue\n  prev: IconValue\n  next: IconValue\n  checkboxOn: IconValue\n  checkboxOff: IconValue\n  checkboxIndeterminate: IconValue\n  delimiter: IconValue\n  sortAsc: IconValue\n  sortDesc: IconValue\n  expand: IconValue\n  menu: IconValue\n  subgroup: IconValue\n  dropdown: IconValue\n  radioOn: IconValue\n  radioOff: IconValue\n  edit: IconValue\n  ratingEmpty: IconValue\n  ratingFull: IconValue\n  ratingHalf: IconValue\n  loading: IconValue\n  first: IconValue\n  last: IconValue\n  unfold: IconValue\n  file: IconValue\n  plus: IconValue\n  minus: IconValue\n  calendar: IconValue\n}\n\nexport interface IconProps {\n  tag: string\n  icon?: IconValue\n  disabled?: Boolean\n}\n\ntype IconComponent = JSXComponent<IconProps>\n\nexport interface IconSet {\n  component: IconComponent\n}\n\nexport type InternalIconOptions = {\n  defaultSet: string\n  aliases: Partial<IconAliases>\n  sets: Record<string, IconSet>\n}\n\nexport type IconOptions = Partial<InternalIconOptions>\n\ntype IconInstance = {\n  component: IconComponent\n  icon?: IconValue\n}\n\nexport const IconSymbol: InjectionKey<InternalIconOptions> = Symbol.for('vuetify:icons')\n\nexport const makeIconProps = propsFactory({\n  icon: {\n    type: IconValue,\n  },\n  // Could not remove this and use makeTagProps, types complained because it is not required\n  tag: {\n    type: String,\n    required: true,\n  },\n}, 'icon')\n\nexport const VComponentIcon = genericComponent()({\n  name: 'VComponentIcon',\n\n  props: makeIconProps(),\n\n  setup (props, { slots }) {\n    return () => {\n      const Icon = props.icon as JSXComponent\n      return (\n        <props.tag>\n          { props.icon ? <Icon /> : slots.default?.() }\n        </props.tag>\n      )\n    }\n  },\n})\nexport type VComponentIcon = InstanceType<typeof VComponentIcon>\n\nexport const VSvgIcon = defineComponent({\n  name: 'VSvgIcon',\n\n  inheritAttrs: false,\n\n  props: makeIconProps(),\n\n  setup (props, { attrs }) {\n    return () => {\n      return (\n        <props.tag { ...attrs } style={ null }>\n          <svg\n            class=\"v-icon__svg\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            viewBox=\"0 0 24 24\"\n            role=\"img\"\n            aria-hidden=\"true\"\n          >\n            { Array.isArray(props.icon)\n              ? props.icon.map(path => (\n                Array.isArray(path)\n                  ? <path d={ path[0] as string } fill-opacity={ path[1] }></path>\n                  : <path d={ path as string }></path>\n              ))\n              : <path d={ props.icon as string }></path>\n            }\n          </svg>\n        </props.tag>\n      )\n    }\n  },\n})\nexport type VSvgIcon = InstanceType<typeof VSvgIcon>\n\nexport const VLigatureIcon = defineComponent({\n  name: 'VLigatureIcon',\n\n  props: makeIconProps(),\n\n  setup (props) {\n    return () => {\n      return <props.tag>{ props.icon }</props.tag>\n    }\n  },\n})\nexport type VLigatureIcon = InstanceType<typeof VLigatureIcon>\n\nexport const VClassIcon = defineComponent({\n  name: 'VClassIcon',\n\n  props: makeIconProps(),\n\n  setup (props) {\n    return () => {\n      return <props.tag class={ props.icon }></props.tag>\n    }\n  },\n})\nexport type VClassIcon = InstanceType<typeof VClassIcon>\n\nfunction genDefaults (): Record<string, IconSet> {\n  return {\n    svg: {\n      component: VSvgIcon,\n    },\n    class: {\n      component: VClassIcon,\n    },\n  }\n}\n\n// Composables\nexport function createIcons (options?: IconOptions) {\n  const sets = genDefaults()\n  const defaultSet = options?.defaultSet ?? 'mdi'\n\n  if (defaultSet === 'mdi' && !sets.mdi) {\n    sets.mdi = mdi\n  }\n\n  return mergeDeep({\n    defaultSet,\n    sets,\n    aliases: {\n      ...aliases,\n      /* eslint-disable max-len */\n      vuetify: [\n        'M8.2241 14.2009L12 21L22 3H14.4459L8.2241 14.2009Z',\n        ['M7.26303 12.4733L7.00113 12L2 3H12.5261C12.5261 3 12.5261 3 12.5261 3L7.26303 12.4733Z', 0.6],\n      ],\n      'vuetify-outline': 'svg:M7.26 12.47 12.53 3H2L7.26 12.47ZM14.45 3 8.22 14.2 12 21 22 3H14.45ZM18.6 5 12 16.88 10.51 14.2 15.62 5ZM7.26 8.35 5.4 5H9.13L7.26 8.35Z',\n      'vuetify-play': [\n        'm6.376 13.184-4.11-7.192C1.505 4.66 2.467 3 4.003 3h8.532l-.953 1.576-.006.01-.396.677c-.429.732-.214 1.507.194 2.015.404.503 1.092.878 1.869.806a3.72 3.72 0 0 1 1.005.022c.276.053.434.143.523.237.138.146.38.635-.25 2.09-.893 1.63-1.553 1.722-1.847 1.677-.213-.033-.468-.158-.756-.406a4.95 4.95 0 0 1-.8-.927c-.39-.564-1.04-.84-1.66-.846-.625-.006-1.316.27-1.693.921l-.478.826-.911 1.506Z',\n        ['M9.093 11.552c.046-.079.144-.15.32-.148a.53.53 0 0 1 .43.207c.285.414.636.847 1.046 1.2.405.35.914.662 1.516.754 1.334.205 2.502-.698 3.48-2.495l.014-.028.013-.03c.687-1.574.774-2.852-.005-3.675-.37-.391-.861-.586-1.333-.676a5.243 5.243 0 0 0-1.447-.044c-.173.016-.393-.073-.54-.257-.145-.18-.127-.316-.082-.392l.393-.672L14.287 3h5.71c1.536 0 2.499 1.659 1.737 2.992l-7.997 13.996c-.768 1.344-2.706 1.344-3.473 0l-3.037-5.314 1.377-2.278.004-.006.004-.007.481-.831Z', 0.6],\n      ],\n      /* eslint-enable max-len */\n    },\n  }, options) as InternalIconOptions\n}\n\nexport const useIcon = (props: Ref<IconValue | undefined>) => {\n  const icons = inject(IconSymbol)\n\n  if (!icons) throw new Error('Missing Vuetify Icons provide!')\n\n  const iconData = computed<IconInstance>(() => {\n    const iconAlias = unref(props)\n\n    if (!iconAlias) return { component: VComponentIcon }\n\n    let icon: IconValue | undefined = iconAlias\n\n    if (typeof icon === 'string') {\n      icon = icon.trim()\n\n      if (icon.startsWith('$')) {\n        icon = icons.aliases?.[icon.slice(1)]\n      }\n    }\n\n    if (!icon) consoleWarn(`Could not find aliased icon \"${iconAlias}\"`)\n\n    if (Array.isArray(icon)) {\n      return {\n        component: VSvgIcon,\n        icon,\n      }\n    } else if (typeof icon !== 'string') {\n      return {\n        component: VComponentIcon,\n        icon,\n      }\n    }\n\n    const iconSetName = Object.keys(icons.sets).find(\n      setName => typeof icon === 'string' && icon.startsWith(`${setName}:`)\n    )\n\n    const iconName = iconSetName ? icon.slice(iconSetName.length + 1) : icon\n    const iconSet = icons.sets[iconSetName ?? icons.defaultSet]\n\n    return {\n      component: iconSet.component,\n      icon: iconName,\n    }\n  })\n\n  return { iconData }\n}\n", "// Utilities\nimport { onBeforeUnmount, readonly, ref, watch } from 'vue'\nimport { templateRef } from '@/util'\nimport { IN_BROWSER } from '@/util/globals'\n\n// Types\nimport type { DeepReadonly, Ref } from 'vue'\nimport type { TemplateRef } from '@/util'\n\ninterface ResizeState {\n  resizeRef: TemplateRef\n  contentRect: DeepReadonly<Ref<DOMRectReadOnly | undefined>>\n}\n\nexport function useResizeObserver (callback?: ResizeObserverCallback, box: 'content' | 'border' = 'content'): ResizeState {\n  const resizeRef = templateRef()\n  const contentRect = ref<DOMRectReadOnly>()\n\n  if (IN_BROWSER) {\n    const observer = new ResizeObserver((entries: ResizeObserverEntry[]) => {\n      callback?.(entries, observer)\n\n      if (!entries.length) return\n\n      if (box === 'content') {\n        contentRect.value = entries[0].contentRect\n      } else {\n        contentRect.value = entries[0].target.getBoundingClientRect()\n      }\n    })\n\n    onBeforeUnmount(() => {\n      observer.disconnect()\n    })\n\n    watch(() => resizeRef.el, (newValue, oldValue) => {\n      if (oldValue) {\n        observer.unobserve(oldValue)\n        contentRect.value = undefined\n      }\n\n      if (newValue) observer.observe(newValue)\n    }, {\n      flush: 'post',\n    })\n  }\n\n  return {\n    resizeRef,\n    contentRect: readonly(contentRect),\n  }\n}\n", "// Composables\nimport { useResizeObserver } from '@/composables/resizeObserver'\n\n// Utilities\nimport {\n  computed,\n  inject,\n  onActivated,\n  onBeforeUnmount,\n  onDeactivated,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  shallowRef,\n} from 'vue'\nimport { convertToUnit, findChildrenWithProvide, getCurrentInstance, getUid, propsFactory } from '@/util'\n\n// Types\nimport type { ComponentInternalInstance, CSSProperties, InjectionKey, Prop, Ref } from 'vue'\n\nexport type Position = 'top' | 'left' | 'right' | 'bottom'\n\ninterface Layer {\n  top: number\n  bottom: number\n  left: number\n  right: number\n}\n\ninterface LayoutItem extends Layer {\n  id: string\n  size: number\n  position: Position\n}\n\ninterface LayoutProvide {\n  register: (\n    vm: ComponentInternalInstance,\n    options: {\n      id: string\n      order: Ref<number>\n      position: Ref<Position>\n      layoutSize: Ref<number | string>\n      elementSize: Ref<number | string | undefined>\n      active: Ref<boolean>\n      disableTransitions?: Ref<boolean>\n      absolute: Ref<boolean | undefined>\n    }\n  ) => {\n    layoutItemStyles: Ref<CSSProperties>\n    layoutItemScrimStyles: Ref<CSSProperties>\n    zIndex: Ref<number>\n  }\n  unregister: (id: string) => void\n  mainRect: Ref<Layer>\n  mainStyles: Ref<CSSProperties>\n  getLayoutItem: (id: string) => LayoutItem | undefined\n  items: Ref<LayoutItem[]>\n  layoutRect: Ref<DOMRectReadOnly | undefined>\n  rootZIndex: Ref<number>\n}\n\nexport const VuetifyLayoutKey: InjectionKey<LayoutProvide> = Symbol.for('vuetify:layout')\nexport const VuetifyLayoutItemKey: InjectionKey<{ id: string }> = Symbol.for('vuetify:layout-item')\n\nconst ROOT_ZINDEX = 1000\n\nexport const makeLayoutProps = propsFactory({\n  overlaps: {\n    type: Array,\n    default: () => ([]),\n  } as Prop<string[]>,\n  fullHeight: Boolean,\n}, 'layout')\n\n// Composables\nexport const makeLayoutItemProps = propsFactory({\n  name: {\n    type: String,\n  },\n  order: {\n    type: [Number, String],\n    default: 0,\n  },\n  absolute: Boolean,\n}, 'layout-item')\n\nexport function useLayout () {\n  const layout = inject(VuetifyLayoutKey)\n\n  if (!layout) throw new Error('[Vuetify] Could not find injected layout')\n\n  return {\n    getLayoutItem: layout.getLayoutItem,\n    mainRect: layout.mainRect,\n    mainStyles: layout.mainStyles,\n  }\n}\n\nexport function useLayoutItem (options: {\n  id: string | undefined\n  order: Ref<number>\n  position: Ref<Position>\n  layoutSize: Ref<number | string>\n  elementSize: Ref<number | string | undefined>\n  active: Ref<boolean>\n  disableTransitions?: Ref<boolean>\n  absolute: Ref<boolean | undefined>\n}) {\n  const layout = inject(VuetifyLayoutKey)\n\n  if (!layout) throw new Error('[Vuetify] Could not find injected layout')\n\n  const id = options.id ?? `layout-item-${getUid()}`\n\n  const vm = getCurrentInstance('useLayoutItem')\n\n  provide(VuetifyLayoutItemKey, { id })\n\n  const isKeptAlive = shallowRef(false)\n  onDeactivated(() => isKeptAlive.value = true)\n  onActivated(() => isKeptAlive.value = false)\n\n  const {\n    layoutItemStyles,\n    layoutItemScrimStyles,\n  } = layout.register(vm, {\n    ...options,\n    active: computed(() => isKeptAlive.value ? false : options.active.value),\n    id,\n  })\n\n  onBeforeUnmount(() => layout.unregister(id))\n\n  return { layoutItemStyles, layoutRect: layout.layoutRect, layoutItemScrimStyles }\n}\n\nconst generateLayers = (\n  layout: string[],\n  positions: Map<string, Ref<Position>>,\n  layoutSizes: Map<string, Ref<number | string>>,\n  activeItems: Map<string, Ref<boolean>>,\n): { id: string, layer: Layer }[] => {\n  let previousLayer: Layer = { top: 0, left: 0, right: 0, bottom: 0 }\n  const layers = [{ id: '', layer: { ...previousLayer } }]\n  for (const id of layout) {\n    const position = positions.get(id)\n    const amount = layoutSizes.get(id)\n    const active = activeItems.get(id)\n    if (!position || !amount || !active) continue\n\n    const layer = {\n      ...previousLayer,\n      [position.value]: parseInt(previousLayer[position.value], 10) + (active.value ? parseInt(amount.value, 10) : 0),\n    }\n\n    layers.push({\n      id,\n      layer,\n    })\n\n    previousLayer = layer\n  }\n\n  return layers\n}\n\nexport function createLayout (props: { overlaps?: string[], fullHeight?: boolean }) {\n  const parentLayout = inject(VuetifyLayoutKey, null)\n  const rootZIndex = computed(() => parentLayout ? parentLayout.rootZIndex.value - 100 : ROOT_ZINDEX)\n  const registered = ref<string[]>([])\n  const positions = reactive(new Map<string, Ref<Position>>())\n  const layoutSizes = reactive(new Map<string, Ref<number | string>>())\n  const priorities = reactive(new Map<string, Ref<number>>())\n  const activeItems = reactive(new Map<string, Ref<boolean>>())\n  const disabledTransitions = reactive(new Map<string, Ref<boolean>>())\n  const { resizeRef, contentRect: layoutRect } = useResizeObserver()\n\n  const computedOverlaps = computed(() => {\n    const map = new Map<string, { position: Position, amount: number }>()\n    const overlaps = props.overlaps ?? []\n    for (const overlap of overlaps.filter(item => item.includes(':'))) {\n      const [top, bottom] = overlap.split(':')\n      if (!registered.value.includes(top) || !registered.value.includes(bottom)) continue\n\n      const topPosition = positions.get(top)\n      const bottomPosition = positions.get(bottom)\n      const topAmount = layoutSizes.get(top)\n      const bottomAmount = layoutSizes.get(bottom)\n\n      if (!topPosition || !bottomPosition || !topAmount || !bottomAmount) continue\n\n      map.set(bottom, { position: topPosition.value, amount: parseInt(topAmount.value, 10) })\n      map.set(top, { position: bottomPosition.value, amount: -parseInt(bottomAmount.value, 10) })\n    }\n\n    return map\n  })\n\n  const layers = computed(() => {\n    const uniquePriorities = [...new Set([...priorities.values()].map(p => p.value))].sort((a, b) => a - b)\n    const layout = []\n    for (const p of uniquePriorities) {\n      const items = registered.value.filter(id => priorities.get(id)?.value === p)\n      layout.push(...items)\n    }\n    return generateLayers(layout, positions, layoutSizes, activeItems)\n  })\n\n  const transitionsEnabled = computed(() => {\n    return !Array.from(disabledTransitions.values()).some(ref => ref.value)\n  })\n\n  const mainRect = computed(() => {\n    return layers.value[layers.value.length - 1].layer\n  })\n\n  const mainStyles = computed<CSSProperties>(() => {\n    return {\n      '--v-layout-left': convertToUnit(mainRect.value.left),\n      '--v-layout-right': convertToUnit(mainRect.value.right),\n      '--v-layout-top': convertToUnit(mainRect.value.top),\n      '--v-layout-bottom': convertToUnit(mainRect.value.bottom),\n      ...(transitionsEnabled.value ? undefined : { transition: 'none' }),\n    }\n  })\n\n  const items = computed(() => {\n    return layers.value.slice(1).map(({ id }, index) => {\n      const { layer } = layers.value[index]\n      const size = layoutSizes.get(id)\n      const position = positions.get(id)\n\n      return {\n        id,\n        ...layer,\n        size: Number(size!.value),\n        position: position!.value,\n      }\n    })\n  })\n\n  const getLayoutItem = (id: string) => {\n    return items.value.find(item => item.id === id)\n  }\n\n  const rootVm = getCurrentInstance('createLayout')\n\n  const isMounted = shallowRef(false)\n  onMounted(() => {\n    isMounted.value = true\n  })\n\n  provide(VuetifyLayoutKey, {\n    register: (\n      vm: ComponentInternalInstance,\n      {\n        id,\n        order,\n        position,\n        layoutSize,\n        elementSize,\n        active,\n        disableTransitions,\n        absolute,\n      }\n    ) => {\n      priorities.set(id, order)\n      positions.set(id, position)\n      layoutSizes.set(id, layoutSize)\n      activeItems.set(id, active)\n      disableTransitions && disabledTransitions.set(id, disableTransitions)\n\n      const instances = findChildrenWithProvide(VuetifyLayoutItemKey, rootVm?.vnode)\n      const instanceIndex = instances.indexOf(vm)\n\n      if (instanceIndex > -1) registered.value.splice(instanceIndex, 0, id)\n      else registered.value.push(id)\n\n      const index = computed(() => items.value.findIndex(i => i.id === id))\n      const zIndex = computed(() => rootZIndex.value + (layers.value.length * 2) - (index.value * 2))\n\n      const layoutItemStyles = computed<CSSProperties>(() => {\n        const isHorizontal = position.value === 'left' || position.value === 'right'\n        const isOppositeHorizontal = position.value === 'right'\n        const isOppositeVertical = position.value === 'bottom'\n        const size = elementSize.value ?? layoutSize.value\n        const unit = size === 0 ? '%' : 'px'\n\n        const styles = {\n          [position.value]: 0,\n          zIndex: zIndex.value,\n          transform: `translate${isHorizontal ? 'X' : 'Y'}(${(active.value ? 0 : -(size === 0 ? 100 : size)) * (isOppositeHorizontal || isOppositeVertical ? -1 : 1)}${unit})`,\n          position: absolute.value || rootZIndex.value !== ROOT_ZINDEX ? 'absolute' : 'fixed',\n          ...(transitionsEnabled.value ? undefined : { transition: 'none' }),\n        } as const\n\n        if (!isMounted.value) return styles\n\n        const item = items.value[index.value]\n\n        if (!item) throw new Error(`[Vuetify] Could not find layout item \"${id}\"`)\n\n        const overlap = computedOverlaps.value.get(id)\n        if (overlap) {\n          item[overlap.position] += overlap.amount\n        }\n\n        return {\n          ...styles,\n          height:\n            isHorizontal ? `calc(100% - ${item.top}px - ${item.bottom}px)`\n            : elementSize.value ? `${elementSize.value}px`\n            : undefined,\n          left: isOppositeHorizontal ? undefined : `${item.left}px`,\n          right: isOppositeHorizontal ? `${item.right}px` : undefined,\n          top: position.value !== 'bottom' ? `${item.top}px` : undefined,\n          bottom: position.value !== 'top' ? `${item.bottom}px` : undefined,\n          width:\n            !isHorizontal ? `calc(100% - ${item.left}px - ${item.right}px)`\n            : elementSize.value ? `${elementSize.value}px`\n            : undefined,\n        }\n      })\n\n      const layoutItemScrimStyles = computed<CSSProperties>(() => ({\n        zIndex: zIndex.value - 1,\n      }))\n\n      return { layoutItemStyles, layoutItemScrimStyles, zIndex }\n    },\n    unregister: (id: string) => {\n      priorities.delete(id)\n      positions.delete(id)\n      layoutSizes.delete(id)\n      activeItems.delete(id)\n      disabledTransitions.delete(id)\n      registered.value = registered.value.filter(v => v !== id)\n    },\n    mainRect,\n    mainStyles,\n    getLayoutItem,\n    items,\n    layoutRect,\n    rootZIndex,\n  })\n\n  const layoutClasses = computed(() => [\n    'v-layout',\n    { 'v-layout--full-height': props.fullHeight },\n  ])\n\n  const layoutStyles = computed(() => ({\n    zIndex: parentLayout ? rootZIndex.value : undefined,\n    position: parentLayout ? 'relative' as const : undefined,\n    overflow: parentLayout ? 'hidden' : undefined,\n  }))\n\n  return {\n    layoutClasses,\n    layoutStyles,\n    getLayoutItem,\n    items,\n    layoutRect,\n    layoutRef: resizeRef,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAMA,WAAmC;EACvC,OAAO;EACPC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJ,kBAAkB;EAClBC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;EACJC,IAAI;AACN;AAEA,SAASC,aAAcC,OAAYC,QAAgBC,gBAAyB;AAC1E,QAAMC,QAAQ,CAAA;AACd,MAAIC,cAAc,CAAA;AAClB,QAAMC,kBAAkBC,aAAaN,KAAI;AACzC,QAAMO,iBAAiBC,WAAWR,KAAI;AACtC,QAAMS,QAAQP,kBAAkBzJ,SAASwJ,OAAOS,MAAM,EAAE,EAAEC,YAAY,CAAC,KAAK;AAC5E,QAAMC,qBAAqBP,gBAAgBQ,OAAO,IAAIJ,QAAQ,KAAK;AACnE,QAAMK,oBAAoBP,eAAeM,OAAO,IAAIJ,QAAQ,KAAK;AAEjE,WAASM,IAAI,GAAGA,IAAIH,mBAAmBG,KAAK;AAC1C,UAAMC,cAAc,IAAIC,KAAKZ,eAAe;AAC5CW,gBAAYE,QAAQF,YAAYG,QAAQ,KAAKP,oBAAoBG,EAAE;AACnEX,gBAAYgB,KAAKJ,WAAW;EAC9B;AAEA,WAASD,IAAI,GAAGA,KAAKR,eAAeY,QAAQ,GAAGJ,KAAK;AAClD,UAAMM,MAAM,IAAIJ,KAAKjB,MAAKsB,YAAY,GAAGtB,MAAKuB,SAAS,GAAGR,CAAC;AAG3DX,gBAAYgB,KAAKC,GAAG;AAGpB,QAAIjB,YAAYoB,WAAW,GAAG;AAC5BrB,YAAMiB,KAAKhB,WAAW;AACtBA,oBAAc,CAAA;IAChB;EACF;AAEA,WAASW,IAAI,GAAGA,IAAI,IAAID,kBAAkBC,KAAK;AAC7C,UAAMC,cAAc,IAAIC,KAAKV,cAAc;AAC3CS,gBAAYE,QAAQF,YAAYG,QAAQ,IAAIJ,CAAC;AAC7CX,gBAAYgB,KAAKJ,WAAW;EAC9B;AAEA,MAAIZ,YAAYoB,SAAS,GAAG;AAC1BrB,UAAMiB,KAAKhB,WAAW;EACxB;AAEA,SAAOD;AACT;AAEA,SAASsB,YAAazB,OAAYC,QAAgBC,gBAAyB;AACzE,QAAMmB,MAAMnB,kBAAkBzJ,SAASwJ,OAAOS,MAAM,EAAE,EAAEC,YAAY,CAAC,KAAK;AAE1E,QAAMe,IAAI,IAAIT,KAAKjB,KAAI;AACvB,SAAO0B,EAAEb,OAAO,MAAMQ,KAAK;AACzBK,MAAER,QAAQQ,EAAEP,QAAQ,IAAI,CAAC;EAC3B;AACA,SAAOO;AACT;AAEA,SAASC,UAAW3B,OAAYC,QAAgB;AAC9C,QAAMyB,IAAI,IAAIT,KAAKjB,KAAI;AACvB,QAAM4B,YAAYnL,SAASwJ,OAAOS,MAAM,EAAE,EAAEC,YAAY,CAAC,KAAK,KAAK,KAAK;AACxE,SAAOe,EAAEb,OAAO,MAAMe,SAAS;AAC7BF,MAAER,QAAQQ,EAAEP,QAAQ,IAAI,CAAC;EAC3B;AACA,SAAOO;AACT;AAEA,SAASpB,aAAcN,OAAY;AACjC,SAAO,IAAIiB,KAAKjB,MAAKsB,YAAY,GAAGtB,MAAKuB,SAAS,GAAG,CAAC;AACxD;AAEA,SAASf,WAAYR,OAAY;AAC/B,SAAO,IAAIiB,KAAKjB,MAAKsB,YAAY,GAAGtB,MAAKuB,SAAS,IAAI,GAAG,CAAC;AAC5D;AAEA,SAASM,eAAgBC,OAAqB;AAC5C,QAAMC,QAAQD,MAAME,MAAM,GAAG,EAAEC,IAAIC,MAAM;AAGzC,SAAO,IAAIjB,KAAKc,MAAM,CAAC,GAAGA,MAAM,CAAC,IAAI,GAAGA,MAAM,CAAC,CAAC;AAClD;AAEA,IAAMI,WAAW;AAEjB,SAASnC,KAAM8B,OAA0B;AACvC,MAAIA,SAAS,KAAM,QAAO,oBAAIb,KAAK;AAEnC,MAAIa,iBAAiBb,KAAM,QAAOa;AAElC,MAAI,OAAOA,UAAU,UAAU;AAC7B,QAAIM;AAEJ,QAAID,SAASE,KAAKP,KAAK,GAAG;AACxB,aAAOD,eAAeC,KAAK;IAC7B,OAAO;AACLM,eAASnB,KAAKqB,MAAMR,KAAK;IAC3B;AAEA,QAAI,CAACS,MAAMH,MAAM,EAAG,QAAO,IAAInB,KAAKmB,MAAM;EAC5C;AAEA,SAAO;AACT;AAEA,IAAMI,0BAA0B,IAAIvB,KAAK,KAAM,GAAG,CAAC;AAEnD,SAASwB,YAAaxC,QAAgBC,gBAAyB;AAC7D,QAAMwC,iBAAiBxC,kBAAkBzJ,SAASwJ,OAAOS,MAAM,EAAE,EAAEC,YAAY,CAAC,KAAK;AAErF,SAAOgC,YAAY,CAAC,EAAEV,IAAIlB,OAAK;AAC7B,UAAM6B,UAAU,IAAI3B,KAAKuB,uBAAuB;AAChDI,YAAQ1B,QAAQsB,wBAAwBrB,QAAQ,IAAIuB,iBAAiB3B,CAAC;AACtE,WAAO,IAAI8B,KAAKC,eAAe7C,QAAQ;MAAE2C,SAAS;IAAS,CAAC,EAAEG,OAAOH,OAAO;EAC9E,CAAC;AACH;AAEA,SAASG,OACPjB,OACAkB,cACA/C,QACAgD,SACQ;AACR,QAAMC,UAAUlD,KAAK8B,KAAK,KAAK,oBAAIb,KAAK;AACxC,QAAMkC,eAAeF,mCAAUD;AAE/B,MAAI,OAAOG,iBAAiB,YAAY;AACtC,WAAOA,aAAaD,SAASF,cAAc/C,MAAM;EACnD;AAEA,MAAImD,UAAsC,CAAC;AAC3C,UAAQJ,cAAY;IAClB,KAAK;AACHI,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAQjC,KAAK;MAAU;AAC3D;IACF,KAAK;AACH+B,gBAAU;QAAER,SAAS;QAAQS,MAAM;QAAWC,OAAO;QAAQjC,KAAK;MAAU;AAC5E;IACF,KAAK;AACH,YAAMA,MAAM6B,QAAQ/B,QAAQ;AAC5B,YAAMmC,QAAQ,IAAIT,KAAKC,eAAe7C,QAAQ;QAAEqD,OAAO;MAAO,CAAC,EAAEP,OAAOG,OAAO;AAC/E,aAAO,GAAG7B,GAAG,IAAIiC,KAAK;IACxB,KAAK;AACHF,gBAAU;QAAER,SAAS;QAASvB,KAAK;QAAWiC,OAAO;MAAQ;AAC7D;IACF,KAAK;AACHF,gBAAU;QAAEE,OAAO;QAASjC,KAAK;MAAU;AAC3C;IACF,KAAK;AACH+B,gBAAU;QAAEC,MAAM;MAAU;AAC5B;IACF,KAAK;AACHD,gBAAU;QAAEE,OAAO;MAAO;AAC1B;IACF,KAAK;AACHF,gBAAU;QAAEE,OAAO;MAAQ;AAC3B;IACF,KAAK;AACHF,gBAAU;QAAEE,OAAO;QAAQD,MAAM;MAAU;AAC3C;IACF,KAAK;AACHD,gBAAU;QAAEE,OAAO;QAAQjC,KAAK;MAAU;AAC1C;IACF,KAAK;AACH+B,gBAAU;QAAER,SAAS;MAAO;AAC5B;IACF,KAAK;AACHQ,gBAAU;QAAER,SAAS;MAAQ;AAC7B;IACF,KAAK;AACH,aAAO,IAAIC,KAAKU,aAAatD,MAAM,EAAE8C,OAAOG,QAAQ/B,QAAQ,CAAC;IAC/D,KAAK;AACHiC,gBAAU;QAAEI,MAAM;QAAWC,QAAQ;MAAK;AAC1C;IACF,KAAK;AACHL,gBAAU;QAAEI,MAAM;QAAWC,QAAQ;MAAM;AAC3C;IACF,KAAK;AACHL,gBAAU;QAAEM,QAAQ;MAAU;AAC9B;IACF,KAAK;AACHN,gBAAU;QAAEO,QAAQ;MAAU;AAC9B;IACF,KAAK;AACHP,gBAAU;QAAEI,MAAM;QAAWE,QAAQ;QAAWC,QAAQ;QAAWF,QAAQ;MAAK;AAChF;IACF,KAAK;AACHL,gBAAU;QAAEI,MAAM;QAAWE,QAAQ;QAAWC,QAAQ;QAAWF,QAAQ;MAAK;AAChF;IACF,KAAK;AACHL,gBAAU;QAAEI,MAAM;QAAWE,QAAQ;QAAWC,QAAQ;QAAWF,QAAQ;MAAM;AACjF;IACF,KAAK;AACHL,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAQjC,KAAK;QAAWmC,MAAM;QAAWE,QAAQ;QAAWC,QAAQ;QAAWF,QAAQ;MAAK;AAChI;IACF,KAAK;AACHL,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAQjC,KAAK;QAAWmC,MAAM;QAAWE,QAAQ;QAAWC,QAAQ;QAAWF,QAAQ;MAAK;AAChI;IACF,KAAK;AACHL,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAQjC,KAAK;QAAWmC,MAAM;QAAWE,QAAQ;QAAWC,QAAQ;QAAWF,QAAQ;MAAM;AACjI;IACF,KAAK;AACHL,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAWjC,KAAK;MAAU;AAC9D;IACF,KAAK;AACH+B,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAWjC,KAAK;QAAWmC,MAAM;QAAWE,QAAQ;QAAWC,QAAQ;QAAWF,QAAQ;MAAM;AACpI;IACF,KAAK;AACHL,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAWjC,KAAK;QAAWmC,MAAM;QAAWE,QAAQ;QAAWC,QAAQ;QAAWF,QAAQ;MAAK;AACnI;IACF,KAAK;AACHL,gBAAU;QAAEC,MAAM;QAAWC,OAAO;QAAWjC,KAAK;QAAWmC,MAAM;QAAWE,QAAQ;QAAWC,QAAQ;QAAWF,QAAQ;MAAM;AACpI;IACF;AACEL,gBAAUD,gBAAgB;QAAES,UAAU;QAAOC,cAAc;MAAQ;EACvE;AAEA,SAAO,IAAIhB,KAAKC,eAAe7C,QAAQmD,OAAO,EAAEL,OAAOG,OAAO;AAChE;AAEA,SAASY,MAAOC,SAA2BjC,OAAa;AACtD,QAAM9B,QAAO+D,QAAQC,SAASlC,KAAK;AACnC,QAAMuB,OAAOrD,MAAKsB,YAAY;AAC9B,QAAMgC,QAAQW,SAASC,OAAOlE,MAAKuB,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG;AAC1D,QAAMF,MAAM4C,SAASC,OAAOlE,MAAKmB,QAAQ,CAAC,GAAG,GAAG,GAAG;AAEnD,SAAO,GAAGkC,IAAI,IAAIC,KAAK,IAAIjC,GAAG;AAChC;AAEA,SAAS8C,SAAUrC,OAAe;AAChC,QAAM,CAACuB,MAAMC,OAAOjC,GAAG,IAAIS,MAAME,MAAM,GAAG,EAAEC,IAAIC,MAAM;AAEtD,SAAO,IAAIjB,KAAKoC,MAAMC,QAAQ,GAAGjC,GAAG;AACtC;AAEA,SAAS+C,WAAYpE,OAAYqE,QAAgB;AAC/C,QAAM3C,IAAI,IAAIT,KAAKjB,KAAI;AACvB0B,IAAE4C,WAAW5C,EAAE6C,WAAW,IAAIF,MAAM;AACpC,SAAO3C;AACT;AAEA,SAAS8C,SAAUxE,OAAYqE,QAAgB;AAC7C,QAAM3C,IAAI,IAAIT,KAAKjB,KAAI;AACvB0B,IAAE+C,SAAS/C,EAAEgD,SAAS,IAAIL,MAAM;AAChC,SAAO3C;AACT;AAEA,SAASiD,QAAS3E,OAAYqE,QAAgB;AAC5C,QAAM3C,IAAI,IAAIT,KAAKjB,KAAI;AACvB0B,IAAER,QAAQQ,EAAEP,QAAQ,IAAIkD,MAAM;AAC9B,SAAO3C;AACT;AAEA,SAASkD,SAAU5E,OAAYqE,QAAgB;AAC7C,QAAM3C,IAAI,IAAIT,KAAKjB,KAAI;AACvB0B,IAAER,QAAQQ,EAAEP,QAAQ,IAAKkD,SAAS,CAAE;AACpC,SAAO3C;AACT;AAEA,SAASmD,UAAW7E,OAAYqE,QAAgB;AAC9C,QAAM3C,IAAI,IAAIT,KAAKjB,KAAI;AACvB0B,IAAER,QAAQ,CAAC;AACXQ,IAAEoD,SAASpD,EAAEH,SAAS,IAAI8C,MAAM;AAChC,SAAO3C;AACT;AAEA,SAASqD,QAAS/E,OAAY;AAC5B,SAAOA,MAAKsB,YAAY;AAC1B;AAEA,SAASC,SAAUvB,OAAY;AAC7B,SAAOA,MAAKuB,SAAS;AACvB;AAEA,SAASJ,QAASnB,OAAY;AAC5B,SAAOA,MAAKmB,QAAQ;AACtB;AAEA,SAAS6D,aAAchF,OAAY;AACjC,SAAO,IAAIiB,KAAKjB,MAAKsB,YAAY,GAAGtB,MAAKuB,SAAS,IAAI,GAAG,CAAC;AAC5D;AAEA,SAAS0D,iBAAkBjF,OAAY;AACrC,SAAO,IAAIiB,KAAKjB,MAAKsB,YAAY,GAAGtB,MAAKuB,SAAS,IAAI,GAAG,CAAC;AAC5D;AAEA,SAASmD,SAAU1E,OAAY;AAC7B,SAAOA,MAAK0E,SAAS;AACvB;AAEA,SAASH,WAAYvE,OAAY;AAC/B,SAAOA,MAAKuE,WAAW;AACzB;AAEA,SAASW,YAAalF,OAAY;AAChC,SAAO,IAAIiB,KAAKjB,MAAKsB,YAAY,GAAG,GAAG,CAAC;AAC1C;AACA,SAAS6D,UAAWnF,OAAY;AAC9B,SAAO,IAAIiB,KAAKjB,MAAKsB,YAAY,GAAG,IAAI,EAAE;AAC5C;AAEA,SAAS8D,cAAepF,OAAYqF,OAAqB;AACvD,SAAOC,QAAQtF,OAAMqF,MAAM,CAAC,CAAC,KAAKE,SAASvF,OAAMqF,MAAM,CAAC,CAAC;AAC3D;AAEA,SAASG,QAASxF,OAAW;AAC3B,QAAM0B,IAAI,IAAIT,KAAKjB,KAAI;AAEvB,SAAO0B,aAAaT,QAAQ,CAACsB,MAAMb,EAAE+D,QAAQ,CAAC;AAChD;AAEA,SAASH,QAAStF,OAAY0F,WAAiB;AAC7C,SAAO1F,MAAKyF,QAAQ,IAAIC,UAAUD,QAAQ;AAC5C;AAEA,SAASE,WAAY3F,OAAY0F,WAA0B;AACzD,SAAOJ,QAAQM,WAAW5F,KAAI,GAAG4F,WAAWF,SAAS,CAAC;AACxD;AAEA,SAASH,SAAUvF,OAAY0F,WAAiB;AAC9C,SAAO1F,MAAKyF,QAAQ,IAAIC,UAAUD,QAAQ;AAC5C;AAEA,SAASI,QAAS7F,OAAY0F,WAAiB;AAC7C,SAAO1F,MAAKyF,QAAQ,MAAMC,UAAUD,QAAQ;AAC9C;AAEA,SAASK,UAAW9F,OAAY0F,WAAiB;AAC/C,SAAO1F,MAAKmB,QAAQ,MAAMuE,UAAUvE,QAAQ,KAC1CnB,MAAKuB,SAAS,MAAMmE,UAAUnE,SAAS,KACvCvB,MAAKsB,YAAY,MAAMoE,UAAUpE,YAAY;AACjD;AAEA,SAASyE,YAAa/F,OAAY0F,WAAiB;AACjD,SAAO1F,MAAKuB,SAAS,MAAMmE,UAAUnE,SAAS,KAC5CvB,MAAKsB,YAAY,MAAMoE,UAAUpE,YAAY;AACjD;AAEA,SAAS0E,WAAYhG,OAAY0F,WAAiB;AAChD,SAAO1F,MAAKsB,YAAY,MAAMoE,UAAUpE,YAAY;AACtD;AAEA,SAAS2E,QAASjG,OAAY0F,WAA0BQ,MAAe;AACrE,QAAMxE,IAAI,IAAIT,KAAKjB,KAAI;AACvB,QAAMmG,IAAI,IAAIlF,KAAKyE,SAAS;AAE5B,UAAQQ,MAAI;IACV,KAAK;AACH,aAAOxE,EAAEJ,YAAY,IAAI6E,EAAE7E,YAAY;IACzC,KAAK;AACH,aAAO8E,KAAKC,OAAO3E,EAAEH,SAAS,IAAI4E,EAAE5E,SAAS,KAAKG,EAAEJ,YAAY,IAAI6E,EAAE7E,YAAY,KAAK,MAAM,CAAC;IAChG,KAAK;AACH,aAAOI,EAAEH,SAAS,IAAI4E,EAAE5E,SAAS,KAAKG,EAAEJ,YAAY,IAAI6E,EAAE7E,YAAY,KAAK;IAC7E,KAAK;AACH,aAAO8E,KAAKC,OAAO3E,EAAE+D,QAAQ,IAAIU,EAAEV,QAAQ,MAAM,MAAO,KAAK,KAAK,KAAK,EAAE;IAC3E,KAAK;AACH,aAAOW,KAAKC,OAAO3E,EAAE+D,QAAQ,IAAIU,EAAEV,QAAQ,MAAM,MAAO,KAAK,KAAK,GAAG;IACvE,KAAK;AACH,aAAOW,KAAKC,OAAO3E,EAAE+D,QAAQ,IAAIU,EAAEV,QAAQ,MAAM,MAAO,KAAK,GAAG;IAClE,KAAK;AACH,aAAOW,KAAKC,OAAO3E,EAAE+D,QAAQ,IAAIU,EAAEV,QAAQ,MAAM,MAAO,GAAG;IAC7D,KAAK;AACH,aAAOW,KAAKC,OAAO3E,EAAE+D,QAAQ,IAAIU,EAAEV,QAAQ,KAAK,GAAI;IACtD,SAAS;AACP,aAAO/D,EAAE+D,QAAQ,IAAIU,EAAEV,QAAQ;IACjC;EACF;AACF;AAEA,SAAShB,SAAUzE,OAAYsG,OAAe;AAC5C,QAAM5E,IAAI,IAAIT,KAAKjB,KAAI;AACvB0B,IAAE+C,SAAS6B,KAAK;AAChB,SAAO5E;AACT;AAEA,SAAS4C,WAAYtE,OAAYsG,OAAe;AAC9C,QAAM5E,IAAI,IAAIT,KAAKjB,KAAI;AACvB0B,IAAE4C,WAAWgC,KAAK;AAClB,SAAO5E;AACT;AAEA,SAASoD,SAAU9E,OAAYsG,OAAe;AAC5C,QAAM5E,IAAI,IAAIT,KAAKjB,KAAI;AACvB0B,IAAEoD,SAASwB,KAAK;AAChB,SAAO5E;AACT;AAEA,SAASR,QAASlB,OAAYqB,KAAa;AACzC,QAAMK,IAAI,IAAIT,KAAKjB,KAAI;AACvB0B,IAAER,QAAQG,GAAG;AACb,SAAOK;AACT;AAEA,SAAS6E,QAASvG,OAAYqD,MAAc;AAC1C,QAAM3B,IAAI,IAAIT,KAAKjB,KAAI;AACvB0B,IAAE8E,YAAYnD,IAAI;AAClB,SAAO3B;AACT;AAEA,SAASkE,WAAY5F,OAAY;AAC/B,SAAO,IAAIiB,KAAKjB,MAAKsB,YAAY,GAAGtB,MAAKuB,SAAS,GAAGvB,MAAKmB,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;AACjF;AAEA,SAASsF,SAAUzG,OAAY;AAC7B,SAAO,IAAIiB,KAAKjB,MAAKsB,YAAY,GAAGtB,MAAKuB,SAAS,GAAGvB,MAAKmB,QAAQ,GAAG,IAAI,IAAI,IAAI,GAAG;AACtF;AAEO,IAAMuF,qBAAN,MAAsD;EAI3DC,YAAavD,SAAyE;AACpF,SAAKnD,SAASmD,QAAQnD;AACtB,SAAKgD,UAAUG,QAAQH;EACzB;EAEAjD,KAAM8B,OAAa;AACjB,WAAO9B,KAAK8B,KAAK;EACnB;EAEAkC,SAAUhE,OAAY;AACpB,WAAOA;EACT;EAEA8D,MAAO9D,OAAoB;AACzB,WAAO8D,MAAM,MAAM9D,KAAI;EACzB;EAEAmE,SAAUnE,OAAc;AACtB,WAAOmE,SAASnE,KAAI;EACtB;EAEAoE,WAAYpE,OAAYqE,QAAgB;AACtC,WAAOD,WAAWpE,OAAMqE,MAAM;EAChC;EAEAG,SAAUxE,OAAYqE,QAAgB;AACpC,WAAOG,SAASxE,OAAMqE,MAAM;EAC9B;EAEAM,QAAS3E,OAAYqE,QAAgB;AACnC,WAAOM,QAAQ3E,OAAMqE,MAAM;EAC7B;EAEAO,SAAU5E,OAAYqE,QAAgB;AACpC,WAAOO,SAAS5E,OAAMqE,MAAM;EAC9B;EAEAQ,UAAW7E,OAAYqE,QAAgB;AACrC,WAAOQ,UAAU7E,OAAMqE,MAAM;EAC/B;EAEAtE,aAAcC,OAAYE,gBAAkC;AAC1D,WAAOH,aAAaC,OAAM,KAAKC,QAAQC,iBAAiBgC,OAAOhC,cAAc,IAAI0G,MAAS;EAC5F;EAEAnF,YAAazB,OAAYE,gBAAwC;AAC/D,WAAOuB,YAAYzB,OAAM,KAAKC,QAAQC,iBAAiBgC,OAAOhC,cAAc,IAAI0G,MAAS;EAC3F;EAEAjF,UAAW3B,OAAkB;AAC3B,WAAO2B,UAAU3B,OAAM,KAAKC,MAAM;EACpC;EAEAK,aAAcN,OAAY;AACxB,WAAOM,aAAaN,KAAI;EAC1B;EAEAQ,WAAYR,OAAY;AACtB,WAAOQ,WAAWR,KAAI;EACxB;EAEA+C,OAAQ/C,OAAYgD,cAAsB;AACxC,WAAOD,OAAO/C,OAAMgD,cAAc,KAAK/C,QAAQ,KAAKgD,OAAO;EAC7D;EAEA4C,QAAS7F,OAAY0F,WAAiB;AACpC,WAAOG,QAAQ7F,OAAM0F,SAAS;EAChC;EAEAF,QAASxF,OAAW;AAClB,WAAOwF,QAAQxF,KAAI;EACrB;EAEAoF,cAAepF,OAAYqF,OAAqB;AAC9C,WAAOD,cAAcpF,OAAMqF,KAAK;EAClC;EAEAC,QAAStF,OAAY0F,WAAiB;AACpC,WAAOJ,QAAQtF,OAAM0F,SAAS;EAChC;EAEAC,WAAY3F,OAAY0F,WAAiB;AACvC,WAAOC,WAAW3F,OAAM0F,SAAS;EACnC;EAEAH,SAAUvF,OAAY0F,WAAiB;AACrC,WAAO,CAACJ,QAAQtF,OAAM0F,SAAS,KAAK,CAACG,QAAQ7F,OAAM0F,SAAS;EAC9D;EAEAI,UAAW9F,OAAY0F,WAAiB;AACtC,WAAOI,UAAU9F,OAAM0F,SAAS;EAClC;EAEAK,YAAa/F,OAAY0F,WAAiB;AACxC,WAAOK,YAAY/F,OAAM0F,SAAS;EACpC;EAEAM,WAAYhG,OAAY0F,WAAiB;AACvC,WAAOM,WAAWhG,OAAM0F,SAAS;EACnC;EAEApB,WAAYtE,OAAYsG,OAAe;AACrC,WAAOhC,WAAWtE,OAAMsG,KAAK;EAC/B;EAEA7B,SAAUzE,OAAYsG,OAAe;AACnC,WAAO7B,SAASzE,OAAMsG,KAAK;EAC7B;EAEAxB,SAAU9E,OAAYsG,OAAe;AACnC,WAAOxB,SAAS9E,OAAMsG,KAAK;EAC7B;EAEApF,QAASlB,OAAYqB,KAAmB;AACtC,WAAOH,QAAQlB,OAAMqB,GAAG;EAC1B;EAEAkF,QAASvG,OAAYqD,MAAc;AACjC,WAAOkD,QAAQvG,OAAMqD,IAAI;EAC3B;EAEA4C,QAASjG,OAAY0F,WAA0BQ,MAAe;AAC5D,WAAOD,QAAQjG,OAAM0F,WAAWQ,IAAI;EACtC;EAEAzD,YAAavC,gBAAkC;AAC7C,WAAOuC,YAAY,KAAKxC,QAAQC,iBAAiBgC,OAAOhC,cAAc,IAAI0G,MAAS;EACrF;EAEA7B,QAAS/E,OAAY;AACnB,WAAO+E,QAAQ/E,KAAI;EACrB;EAEAuB,SAAUvB,OAAY;AACpB,WAAOuB,SAASvB,KAAI;EACtB;EAEAmB,QAASnB,OAAY;AACnB,WAAOmB,QAAQnB,KAAI;EACrB;EAEAgF,aAAchF,OAAY;AACxB,WAAOgF,aAAahF,KAAI;EAC1B;EAEAiF,iBAAkBjF,OAAY;AAC5B,WAAOiF,iBAAiBjF,KAAI;EAC9B;EAEA0E,SAAU1E,OAAY;AACpB,WAAO0E,SAAS1E,KAAI;EACtB;EAEAuE,WAAYvE,OAAY;AACtB,WAAOuE,WAAWvE,KAAI;EACxB;EAEA4F,WAAY5F,OAAY;AACtB,WAAO4F,WAAW5F,KAAI;EACxB;EAEAyG,SAAUzG,OAAY;AACpB,WAAOyG,SAASzG,KAAI;EACtB;EAEAkF,YAAalF,OAAY;AACvB,WAAOkF,YAAYlF,KAAI;EACzB;EAEAmF,UAAWnF,OAAY;AACrB,WAAOmF,UAAUnF,KAAI;EACvB;AACF;;;AC/rBO,IAAM6G,oBAAuDC,OAAOC,IAAI,sBAAsB;AAC9F,IAAMC,oBAAgDF,OAAOC,IAAI,sBAAsB;AAEvF,SAASE,WAAYC,SAAkCC,QAAwB;AACpF,QAAMC,WAAWC,UAAU;IACzBC,SAASC;IACTJ,QAAQ;MACNK,IAAI;;MAEJC,IAAI;MACJC,IAAI;MACJC,KAAK;MACLC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;;MAEJC,IAAI;MACJC,IAAI;MACJC,IAAI;;MAEJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,QAAQ;MACRC,QAAQ;MACRC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,IAAI;MACJC,QAAQ;MACRC,QAAQ;IACV;EACF,GAAG3C,OAAO;AAEV,SAAO;IACLA,SAASE;IACT0C,UAAUC,eAAe3C,UAAUD,MAAM;EAC3C;AACF;AAEA,SAAS4C,eAAgB7C,SAA8BC,QAAwB;AAC7E,QAAM2C,WAAWE,SACf,OAAO9C,QAAQI,YAAY,aAEvB,IAAIJ,QAAQI,QAAQ;IACpBH,QAAQD,QAAQC,OAAOA,OAAO8C,QAAQC,KAAK,KAAK/C,OAAO8C,QAAQC;IAC/DC,SAASjD,QAAQiD;EACnB,CAAC,IACCjD,QAAQI,OACd;AAEA8C,QAAMjD,OAAO8C,SAASC,WAAS;AAC7BJ,aAAS3C,SAASD,QAAQC,OAAO+C,KAAK,KAAKA,SAASJ,SAAS3C;EAC/D,CAAC;AAED,SAAO2C;AACT;AAEO,SAASO,UAAyB;AACvC,QAAMnD,UAAUoD,OAAOzD,iBAAiB;AAExC,MAAI,CAACK,QAAS,OAAM,IAAIqD,MAAM,gDAAgD;AAE9E,QAAMpD,SAASqD,UAAU;AAEzB,SAAOT,eAAe7C,SAASC,MAAM;AACvC;AAGO,SAASsD,QAASnD,SAA2B4C,OAAY;AAC9D,QAAMQ,QAAOpD,QAAQqD,SAAST,KAAK;AACnC,MAAIU,OAAOF,MAAKG,YAAY;AAC5B,MAAIC,OAAO,IAAIC,KAAKH,MAAM,GAAG,CAAC;AAE9B,MAAIF,QAAOI,MAAM;AACfF,WAAOA,OAAO;AACdE,WAAO,IAAIC,KAAKH,MAAM,GAAG,CAAC;EAC5B,OAAO;AACL,UAAMI,KAAK,IAAID,KAAKH,OAAO,GAAG,GAAG,CAAC;AAClC,QAAIF,SAAQM,IAAI;AACdJ,aAAOA,OAAO;AACdE,aAAOE;IACT;EACF;AAEA,QAAMC,WAAWC,KAAKC,IAAIT,MAAKU,QAAQ,IAAIN,KAAKM,QAAQ,CAAC;AACzD,QAAMC,WAAWH,KAAKI,KAAKL,YAAY,MAAO,KAAK,KAAK,GAAG;AAE3D,SAAOC,KAAKK,MAAMF,WAAW,CAAC,IAAI;AACpC;;;ACnHO,IAAMG,aAAyCC,OAAOC,IAAI,cAAc;AAE/E,SAASC,cAAe;AACtB,SAAO;IACLC,WAAWC;IACXC,UAAU;IACVC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,UAAU;MACRC,QAASC,OAAcA;MACvBC,YAAaD,OAAcA,KAAK;MAChCE,aAAcF,OAAcA,KAAK,IAAIA;MACrCG,eAAgBH,OAAeA,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAI,IAAIA,KAAKA;MACzEI,aAAcJ,OAAcA,KAAK;MACjCK,cAAeL,OAAc,EAAEA,KAAK,IAAI;MACxCM,gBAAiBN,OAAcA,IAAI,MAAM,IAAIA,KAAK,KAAKA,IAAI,MAAM,IAAIA,IAAI,MAAM,IAAIA,IAAI,KAAK;MAC5FO,aAAcP,OAAcA,KAAK;MACjCQ,cAAeR,OAAc,IAAI,EAAEA,KAAK;MACxCS,gBAAiBT,OAAeA,IAAI,MAAM,IAAIA,KAAK,IAAI,IAAI,IAAI,EAAEA,KAAK;MACtEU,aAAcV,OAAcA,KAAK;MACjCW,cAAeX,OAAc,IAAI,EAAEA,KAAK;MACxCY,gBAAiBZ,OAAcA,IAAI,MAAM,KAAKA,KAAK,IAAI,IAAI,KAAK,EAAEA,KAAK;IACzE;EACF;AACF;AAEA,SAASa,aAAcC,IAAqD;AAC1E,SAAOC,UAAUD,EAAE,MAAME,SAASC,oBAAoBD,SAASE;AACjE;AAEA,SAASH,UAAWD,IAAgE;AAClF,SAAQ,OAAOA,OAAO,WAAYE,SAASG,cAA2BL,EAAE,IAAIM,WAAWN,EAAE;AAC3F;AAEA,SAASO,UAAWC,QAAaC,YAAsBC,KAAuB;AAC5E,MAAI,OAAOF,WAAW,SAAU,QAAOC,cAAcC,MAAM,CAACF,SAASA;AAErE,MAAIR,KAAKC,UAAUO,MAAM;AACzB,MAAIG,cAAc;AAClB,SAAOX,IAAI;AACTW,mBAAeF,aAAaT,GAAGY,aAAaZ,GAAGa;AAC/Cb,SAAKA,GAAGc;EACV;AAEA,SAAOH;AACT;AAEO,SAASI,WACdC,SACAC,QACc;AACd,SAAO;IACLP,KAAKO,OAAOC;IACZF,SAASG,UAAU1C,YAAY,GAAGuC,OAAO;EAC3C;AACF;AAEA,eAAsBI,SACpBC,SACAC,UACAb,YACAc,MACA;AACA,QAAMC,WAAWf,aAAa,eAAe;AAC7C,QAAMO,UAAUG,WAAUI,6BAAMP,YAAWvC,YAAY,GAAG6C,QAAQ;AAClE,QAAMZ,MAAMa,6BAAMb,IAAIe;AACtB,QAAMjB,UAAU,OAAOa,YAAY,WAAWA,UAAUpB,UAAUoB,OAAO,MAAM;AAC/E,QAAM3C,YAAYsC,QAAQtC,cAAc,YAAY8B,kBAAkBkB,cAClElB,OAAOmB,gBACP5B,aAAaiB,QAAQtC,SAAS;AAClC,QAAMkD,OAAO,OAAOZ,QAAQjC,WAAW,aAAaiC,QAAQjC,SAASiC,QAAQhC,SAASgC,QAAQjC,MAAM;AAEpG,MAAI,CAAC6C,KAAM,OAAM,IAAIC,UAAU,oBAAoBb,QAAQjC,MAAM,cAAc;AAE/E,MAAI+C;AACJ,MAAI,OAAOtB,WAAW,UAAU;AAC9BsB,qBAAiBvB,UAAUC,QAAQC,YAAYC,GAAG;EACpD,OAAO;AACLoB,qBAAiBvB,UAAUC,QAAQC,YAAYC,GAAG,IAAIH,UAAU7B,WAAW+B,YAAYC,GAAG;AAE1F,QAAIM,QAAQnC,QAAQ;AAClB,YAAMkD,SAASC,OAAOC,iBAAiBzB,MAAM;AAC7C,YAAM0B,eAAeH,OAAOI,iBAAiB,gBAAgB;AAE7D,UAAID,aAAcJ,mBAAkBM,SAASF,cAAc,EAAE;IAC/D;EACF;AAEAJ,oBAAkBd,QAAQlC;AAC1BgD,mBAAiBO,YAAY3D,WAAWoD,gBAAgB,CAAC,CAACpB,KAAK,CAAC,CAACD,UAAU;AAE3E,QAAM6B,gBAAgB5D,UAAU8C,QAAQ,KAAK;AAE7C,MAAIM,mBAAmBQ,cAAe,QAAOC,QAAQC,QAAQV,cAAc;AAE3E,QAAMW,YAAYC,YAAYC,IAAI;AAElC,SAAO,IAAIJ,QAAQC,aAAWI,sBAAsB,SAASC,KAAMC,aAAqB;AACtF,UAAMC,cAAcD,cAAcL;AAClC,UAAMO,WAAWD,cAAc/B,QAAQpC;AACvC,UAAMqE,WAAWC,KAAKC,MACpBb,iBACCR,iBAAiBQ,iBAClBV,KAAKwB,MAAMJ,UAAU,GAAG,CAAC,CAAC,CAC5B;AAEAtE,cAAU8C,QAAQ,IAAIyB;AAGtB,QAAID,YAAY,KAAKE,KAAKG,IAAIJ,WAAWvE,UAAU8C,QAAQ,CAAC,IAAI,IAAI;AAClE,aAAOgB,QAAQV,cAAc;IAC/B,WAAWkB,WAAW,GAAG;AAEvBM,kBAAY,gCAAgC;AAC5C,aAAOd,QAAQ9D,UAAU8C,QAAQ,CAAC;IACpC;AAEAoB,0BAAsBC,IAAI;EAC5B,CAAC,CAAC;AACJ;AAEO,SAASU,UAAqC;AAAA,MAA5BjC,WAAqBkC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAA7E,SAAA6E,UAAA,CAAA,IAAG,CAAC;AAChD,QAAME,eAAeC,OAAOrF,UAAU;AACtC,QAAM;IAAE4C;EAAM,IAAI0C,OAAO;AAEzB,MAAI,CAACF,aAAc,OAAM,IAAIG,MAAM,iDAAiD;AAEpF,QAAMtC,OAAO;IACX,GAAGmC;;IAEHhD,KAAKoD,SAAS,MAAMJ,aAAahD,IAAIe,SAASP,MAAMO,KAAK;EAC3D;AAEA,iBAAesC,GACbvD,QACAQ,SACA;AACA,WAAOI,SAASZ,QAAQW,UAAUG,UAAUN,OAAO,GAAG,OAAOO,IAAI;EACnE;AAEAwC,KAAGtD,aAAa,OACdD,QACAQ,YACG;AACH,WAAOI,SAASZ,QAAQW,UAAUG,UAAUN,OAAO,GAAG,MAAMO,IAAI;EAClE;AAEA,SAAOwC;AACT;AAMA,SAAS1B,YACP3D,WACA+C,OACAf,KACAD,YACA;AACA,QAAM;IAAEuD;IAAaC;EAAa,IAAIvF;AACtC,QAAM,CAACwF,gBAAgBC,eAAe,IAAIzF,cAAcwB,SAASC,mBAC7D,CAAC6B,OAAOoC,YAAYpC,OAAOqC,WAAW,IACtC,CAAC3F,UAAU4F,aAAa5F,UAAU6F,YAAY;AAElD,MAAIC;AACJ,MAAIC;AAEJ,MAAIhE,YAAY;AACd,QAAIC,KAAK;AACP8D,YAAM,EAAER,cAAcE;AACtBO,YAAM;IACR,OAAO;AACLD,YAAM;AACNC,YAAMT,cAAcE;IACtB;EACF,OAAO;AACLM,UAAM;AACNC,UAAMR,eAAe,CAACE;EACxB;AAEA,SAAOjB,KAAKuB,IAAIvB,KAAKsB,IAAI/C,OAAOgD,GAAG,GAAGD,GAAG;AAC3C;;;ACvMA,IAAME,UAAuB;EAC3BC,UAAU;EACVC,UAAU;EACVC,QAAQ;EACRC,OAAO;EACPC,QAAQ;;EACRC,OAAO;EACPC,SAAS;EACTC,MAAM;EACNC,SAAS;EACTC,OAAO;EACPC,MAAM;EACNC,MAAM;EACNC,YAAY;EACZC,aAAa;EACbC,uBAAuB;EACvBC,WAAW;;EACXC,SAAS;EACTC,UAAU;EACVC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,UAAU;EACVC,SAAS;EACTC,UAAU;EACVC,MAAM;EACNC,aAAa;EACbC,YAAY;EACZC,YAAY;EACZC,SAAS;EACTC,OAAO;EACPC,MAAM;EACNC,QAAQ;EACRC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,UAAU;EACVC,kBAAkB;EAClBC,gBAAgB;EAChBC,YAAY;AACd;AAEA,IAAMC,MAAe;;EAEnBC,WAAYC,WAAeC,EAAEC,YAAY;IAAE,GAAGF;IAAOG,OAAO;EAAM,CAAC;AACrE;;;ACpCO,IAAMC,YAAY,CAACC,QAAQC,UAAUC,QAAQC,KAAK;AAkElD,IAAMC,aAAgDC,OAAOC,IAAI,eAAe;AAEhF,IAAMC,gBAAgBC,aAAa;EACxCC,MAAM;IACJC,MAAMX;EACR;;EAEAY,KAAK;IACHD,MAAMV;IACNY,UAAU;EACZ;AACF,GAAG,MAAM;AAEF,IAAMC,iBAAiBC,iBAAiB,EAAE;EAC/CC,MAAM;EAENC,OAAOT,cAAc;EAErBU,MAAOD,OAAKE,MAAa;AAAA,QAAX;MAAEC;IAAM,IAACD;AACrB,WAAO,MAAM;AACX,YAAME,OAAOJ,MAAMP;AACnB,aAAAY,YAAAL,MAAAL,KAAA,MAAA;QAAAW,SAAAA,MAAA;;AAAA,kBAEMN,MAAMP,OAAIY,YAAAD,MAAA,MAAA,IAAA,KAAcD,WAAMG,YAANH,8BAAiB;;MAAA,CAAA;IAGjD;EACF;AACF,CAAC;AAGM,IAAMI,WAAWC,gBAAgB;EACtCT,MAAM;EAENU,cAAc;EAEdT,OAAOT,cAAc;EAErBU,MAAOD,OAAKU,OAAa;AAAA,QAAX;MAAEC;IAAM,IAACD;AACrB,WAAO,MAAM;AACX,aAAAL,YAAAL,MAAAL,KAAAiB,WACkBD,OAAK;QAAA,SAAW;MAAI,CAAA,GAAA;QAAAL,SAAAA,MAAA,CAAAD,YAAA,OAAA;UAAA,SAAA;UAAA,SAAA;UAAA,WAAA;UAAA,QAAA;UAAA,eAAA;QAAA,GAAA,CAQ9BlB,MAAM0B,QAAQb,MAAMP,IAAI,IACtBO,MAAMP,KAAKqB,IAAIC,UACf5B,MAAM0B,QAAQE,IAAI,IAACV,YAAA,QAAA;UAAA,KACLU,KAAK,CAAC;UAAC,gBAA4BA,KAAK,CAAC;QAAC,GAAA,IAAA,IAAAV,YAAA,QAAA;UAAA,KAC1CU;QAAI,GAAA,IAAA,CACnB,IAACV,YAAA,QAAA;UAAA,KACUL,MAAMP;QAAI,GAAA,IAAA,CAAoB,CAAA,CAAA;MAAA,CAAA;IAKpD;EACF;AACF,CAAC;AAGM,IAAMuB,gBAAgBR,gBAAgB;EAC3CT,MAAM;EAENC,OAAOT,cAAc;EAErBU,MAAOD,OAAO;AACZ,WAAO,MAAM;AACX,aAAAK,YAAAL,MAAAL,KAAA,MAAA;QAAAW,SAAAA,MAAA,CAAoBN,MAAMP,IAAI;MAAA,CAAA;IAChC;EACF;AACF,CAAC;AAGM,IAAMwB,aAAaT,gBAAgB;EACxCT,MAAM;EAENC,OAAOT,cAAc;EAErBU,MAAOD,OAAO;AACZ,WAAO,MAAM;AACX,aAAAK,YAAAL,MAAAL,KAAA;QAAA,SAA0BK,MAAMP;MAAI,GAAA,IAAA;IACtC;EACF;AACF,CAAC;AAGD,SAASyB,eAAwC;AAC/C,SAAO;IACLC,KAAK;MACHC,WAAWb;IACb;IACAc,OAAO;MACLD,WAAWH;IACb;EACF;AACF;AAGO,SAASK,YAAaC,SAAuB;AAClD,QAAMC,OAAON,aAAY;AACzB,QAAMO,cAAaF,mCAASE,eAAc;AAE1C,MAAIA,eAAe,SAAS,CAACD,KAAKE,KAAK;AACrCF,SAAKE,MAAMA;EACb;AAEA,SAAOC,UAAU;IACfF;IACAD;IACAI,SAAS;MACP,GAAGA;;MAEHC,SAAS,CACP,sDACA,CAAC,0FAA0F,GAAG,CAAC;MAEjG,mBAAmB;MACnB,gBAAgB,CACd,wYACA,CAAC,sdAAsd,GAAG,CAAC;;IAG/d;EACF,GAAGN,OAAO;AACZ;AAEO,IAAMO,UAAW9B,WAAsC;AAC5D,QAAM+B,QAAQC,OAAO5C,UAAU;AAE/B,MAAI,CAAC2C,MAAO,OAAM,IAAIE,MAAM,gCAAgC;AAE5D,QAAMC,WAAWC,SAAuB,MAAM;;AAC5C,UAAMC,YAAYC,MAAMrC,KAAK;AAE7B,QAAI,CAACoC,UAAW,QAAO;MAAEhB,WAAWvB;IAAe;AAEnD,QAAIJ,OAA8B2C;AAElC,QAAI,OAAO3C,SAAS,UAAU;AAC5BA,aAAOA,KAAK6C,KAAK;AAEjB,UAAI7C,KAAK8C,WAAW,GAAG,GAAG;AACxB9C,gBAAOsC,WAAMH,YAANG,mBAAgBtC,KAAK+C,MAAM,CAAC;MACrC;IACF;AAEA,QAAI,CAAC/C,KAAMgD,aAAY,gCAAgCL,SAAS,GAAG;AAEnE,QAAIjD,MAAM0B,QAAQpB,IAAI,GAAG;AACvB,aAAO;QACL2B,WAAWb;QACXd;MACF;IACF,WAAW,OAAOA,SAAS,UAAU;AACnC,aAAO;QACL2B,WAAWvB;QACXJ;MACF;IACF;AAEA,UAAMiD,cAAcxD,OAAOyD,KAAKZ,MAAMP,IAAI,EAAEoB,KAC1CC,aAAW,OAAOpD,SAAS,YAAYA,KAAK8C,WAAW,GAAGM,OAAO,GAAG,CACtE;AAEA,UAAMC,WAAWJ,cAAcjD,KAAK+C,MAAME,YAAYK,SAAS,CAAC,IAAItD;AACpE,UAAMuD,UAAUjB,MAAMP,KAAKkB,eAAeX,MAAMN,UAAU;AAE1D,WAAO;MACLL,WAAW4B,QAAQ5B;MACnB3B,MAAMqD;IACR;EACF,CAAC;AAED,SAAO;IAAEZ;EAAS;AACpB;;;ACzPO,SAASe,kBAAmBC,UAAuF;AAAA,MAApDC,MAAyBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAChG,QAAMG,YAAYC,YAAY;AAC9B,QAAMC,cAAcC,IAAqB;AAEzC,MAAIC,YAAY;AACd,UAAMC,WAAW,IAAIC,eAAgBC,aAAmC;AACtEZ,2CAAWY,SAASF;AAEpB,UAAI,CAACE,QAAQT,OAAQ;AAErB,UAAIF,QAAQ,WAAW;AACrBM,oBAAYM,QAAQD,QAAQ,CAAC,EAAEL;MACjC,OAAO;AACLA,oBAAYM,QAAQD,QAAQ,CAAC,EAAEE,OAAOC,sBAAsB;MAC9D;IACF,CAAC;AAEDC,oBAAgB,MAAM;AACpBN,eAASO,WAAW;IACtB,CAAC;AAEDC,UAAM,MAAMb,UAAUc,IAAI,CAACC,UAAUC,aAAa;AAChD,UAAIA,UAAU;AACZX,iBAASY,UAAUD,QAAQ;AAC3Bd,oBAAYM,QAAQT;MACtB;AAEA,UAAIgB,SAAUV,UAASa,QAAQH,QAAQ;IACzC,GAAG;MACDI,OAAO;IACT,CAAC;EACH;AAEA,SAAO;IACLnB;IACAE,aAAakB,SAASlB,WAAW;EACnC;AACF;;;ACYO,IAAMmB,mBAAgDC,OAAOC,IAAI,gBAAgB;AACjF,IAAMC,uBAAqDF,OAAOC,IAAI,qBAAqB;AAElG,IAAME,cAAc;AAEb,IAAMC,kBAAkBC,aAAa;EAC1CC,UAAU;IACRC,MAAMC;IACNC,SAASA,MAAO,CAAA;EAClB;EACAC,YAAYC;AACd,GAAG,QAAQ;AAGJ,IAAMC,sBAAsBP,aAAa;EAC9CQ,MAAM;IACJN,MAAMO;EACR;EACAC,OAAO;IACLR,MAAM,CAACS,QAAQF,MAAM;IACrBL,SAAS;EACX;EACAQ,UAAUN;AACZ,GAAG,aAAa;AAET,SAASO,YAAa;AAC3B,QAAMC,SAASC,OAAOrB,gBAAgB;AAEtC,MAAI,CAACoB,OAAQ,OAAM,IAAIE,MAAM,0CAA0C;AAEvE,SAAO;IACLC,eAAeH,OAAOG;IACtBC,UAAUJ,OAAOI;IACjBC,YAAYL,OAAOK;EACrB;AACF;AAEO,SAASC,cAAeC,SAS5B;AACD,QAAMP,SAASC,OAAOrB,gBAAgB;AAEtC,MAAI,CAACoB,OAAQ,OAAM,IAAIE,MAAM,0CAA0C;AAEvE,QAAMM,KAAKD,QAAQC,MAAM,eAAeC,OAAO,CAAC;AAEhD,QAAMC,KAAKC,mBAAmB,eAAe;AAE7CC,UAAQ7B,sBAAsB;IAAEyB;EAAG,CAAC;AAEpC,QAAMK,cAAcC,WAAW,KAAK;AACpCC,gBAAc,MAAMF,YAAYG,QAAQ,IAAI;AAC5CC,cAAY,MAAMJ,YAAYG,QAAQ,KAAK;AAE3C,QAAM;IACJE;IACAC;EACF,IAAInB,OAAOoB,SAASV,IAAI;IACtB,GAAGH;IACHc,QAAQC,SAAS,MAAMT,YAAYG,QAAQ,QAAQT,QAAQc,OAAOL,KAAK;IACvER;EACF,CAAC;AAEDe,kBAAgB,MAAMvB,OAAOwB,WAAWhB,EAAE,CAAC;AAE3C,SAAO;IAAEU;IAAkBO,YAAYzB,OAAOyB;IAAYN;EAAsB;AAClF;AAEA,IAAMO,iBAAiBA,CACrB1B,QACA2B,WACAC,aACAC,gBACmC;AACnC,MAAIC,gBAAuB;IAAEC,KAAK;IAAGC,MAAM;IAAGC,OAAO;IAAGC,QAAQ;EAAE;AAClE,QAAMC,SAAS,CAAC;IAAE3B,IAAI;IAAI4B,OAAO;MAAE,GAAGN;IAAc;EAAE,CAAC;AACvD,aAAWtB,MAAMR,QAAQ;AACvB,UAAMqC,WAAWV,UAAUW,IAAI9B,EAAE;AACjC,UAAM+B,SAASX,YAAYU,IAAI9B,EAAE;AACjC,UAAMa,SAASQ,YAAYS,IAAI9B,EAAE;AACjC,QAAI,CAAC6B,YAAY,CAACE,UAAU,CAAClB,OAAQ;AAErC,UAAMe,QAAQ;MACZ,GAAGN;MACH,CAACO,SAASrB,KAAK,GAAGwB,SAASV,cAAcO,SAASrB,KAAK,GAAG,EAAE,KAAKK,OAAOL,QAAQwB,SAASD,OAAOvB,OAAO,EAAE,IAAI;IAC/G;AAEAmB,WAAOM,KAAK;MACVjC;MACA4B;IACF,CAAC;AAEDN,oBAAgBM;EAClB;AAEA,SAAOD;AACT;AAEO,SAASO,aAAcC,OAAsD;AAClF,QAAMC,eAAe3C,OAAOrB,kBAAkB,IAAI;AAClD,QAAMiE,aAAavB,SAAS,MAAMsB,eAAeA,aAAaC,WAAW7B,QAAQ,MAAMhC,WAAW;AAClG,QAAM8D,aAAaC,IAAc,CAAA,CAAE;AACnC,QAAMpB,YAAYqB,SAAS,oBAAIC,IAA2B,CAAC;AAC3D,QAAMrB,cAAcoB,SAAS,oBAAIC,IAAkC,CAAC;AACpE,QAAMC,aAAaF,SAAS,oBAAIC,IAAyB,CAAC;AAC1D,QAAMpB,cAAcmB,SAAS,oBAAIC,IAA0B,CAAC;AAC5D,QAAME,sBAAsBH,SAAS,oBAAIC,IAA0B,CAAC;AACpE,QAAM;IAAEG;IAAWC,aAAa5B;EAAW,IAAI6B,kBAAkB;AAEjE,QAAMC,mBAAmBjC,SAAS,MAAM;AACtC,UAAMkC,MAAM,oBAAIP,IAAoD;AACpE,UAAM9D,WAAWwD,MAAMxD,YAAY,CAAA;AACnC,eAAWsE,WAAWtE,SAASuE,OAAOC,UAAQA,KAAKC,SAAS,GAAG,CAAC,GAAG;AACjE,YAAM,CAAC7B,KAAKG,MAAM,IAAIuB,QAAQI,MAAM,GAAG;AACvC,UAAI,CAACf,WAAW9B,MAAM4C,SAAS7B,GAAG,KAAK,CAACe,WAAW9B,MAAM4C,SAAS1B,MAAM,EAAG;AAE3E,YAAM4B,cAAcnC,UAAUW,IAAIP,GAAG;AACrC,YAAMgC,iBAAiBpC,UAAUW,IAAIJ,MAAM;AAC3C,YAAM8B,YAAYpC,YAAYU,IAAIP,GAAG;AACrC,YAAMkC,eAAerC,YAAYU,IAAIJ,MAAM;AAE3C,UAAI,CAAC4B,eAAe,CAACC,kBAAkB,CAACC,aAAa,CAACC,aAAc;AAEpET,UAAIU,IAAIhC,QAAQ;QAAEG,UAAUyB,YAAY9C;QAAOuB,QAAQC,SAASwB,UAAUhD,OAAO,EAAE;MAAE,CAAC;AACtFwC,UAAIU,IAAInC,KAAK;QAAEM,UAAU0B,eAAe/C;QAAOuB,QAAQ,CAACC,SAASyB,aAAajD,OAAO,EAAE;MAAE,CAAC;IAC5F;AAEA,WAAOwC;EACT,CAAC;AAED,QAAMrB,SAASb,SAAS,MAAM;AAC5B,UAAM6C,mBAAmB,CAAC,GAAG,IAAIC,IAAI,CAAC,GAAGlB,WAAWmB,OAAO,CAAC,EAAEb,IAAIc,OAAKA,EAAEtD,KAAK,CAAC,CAAC,EAAEuD,KAAK,CAACC,GAAGC,MAAMD,IAAIC,CAAC;AACtG,UAAMzE,SAAS,CAAA;AACf,eAAWsE,KAAKH,kBAAkB;AAChC,YAAMO,SAAQ5B,WAAW9B,MAAM0C,OAAOlD,QAAE;AA5M9C;AA4MkD0C,iCAAWZ,IAAI9B,EAAE,MAAjB0C,mBAAoBlC,WAAUsD;OAAC;AAC3EtE,aAAOyC,KAAK,GAAGiC,MAAK;IACtB;AACA,WAAOhD,eAAe1B,QAAQ2B,WAAWC,aAAaC,WAAW;EACnE,CAAC;AAED,QAAM8C,qBAAqBrD,SAAS,MAAM;AACxC,WAAO,CAACjC,MAAMuF,KAAKzB,oBAAoBkB,OAAO,CAAC,EAAEQ,KAAK9B,CAAAA,SAAOA,KAAI/B,KAAK;EACxE,CAAC;AAED,QAAMZ,WAAWkB,SAAS,MAAM;AAC9B,WAAOa,OAAOnB,MAAMmB,OAAOnB,MAAM8D,SAAS,CAAC,EAAE1C;EAC/C,CAAC;AAED,QAAM/B,aAAaiB,SAAwB,MAAM;AAC/C,WAAO;MACL,mBAAmByD,cAAc3E,SAASY,MAAMgB,IAAI;MACpD,oBAAoB+C,cAAc3E,SAASY,MAAMiB,KAAK;MACtD,kBAAkB8C,cAAc3E,SAASY,MAAMe,GAAG;MAClD,qBAAqBgD,cAAc3E,SAASY,MAAMkB,MAAM;MACxD,GAAIyC,mBAAmB3D,QAAQgE,SAAY;QAAEC,YAAY;MAAO;IAClE;EACF,CAAC;AAED,QAAMP,QAAQpD,SAAS,MAAM;AAC3B,WAAOa,OAAOnB,MAAMkE,MAAM,CAAC,EAAE1B,IAAI,CAAA2B,MAASC,UAAU;AAAA,UAAlB;QAAE5E;MAAG,IAAC2E;AACtC,YAAM;QAAE/C;MAAM,IAAID,OAAOnB,MAAMoE,KAAK;AACpC,YAAMC,OAAOzD,YAAYU,IAAI9B,EAAE;AAC/B,YAAM6B,WAAWV,UAAUW,IAAI9B,EAAE;AAEjC,aAAO;QACLA;QACA,GAAG4B;QACHiD,MAAMxF,OAAOwF,KAAMrE,KAAK;QACxBqB,UAAUA,SAAUrB;MACtB;IACF,CAAC;EACH,CAAC;AAED,QAAMb,gBAAiBK,QAAe;AACpC,WAAOkE,MAAM1D,MAAMsE,KAAK3B,UAAQA,KAAKnD,OAAOA,EAAE;EAChD;AAEA,QAAM+E,SAAS5E,mBAAmB,cAAc;AAEhD,QAAM6E,YAAY1E,WAAW,KAAK;AAClC2E,YAAU,MAAM;AACdD,cAAUxE,QAAQ;EACpB,CAAC;AAEDJ,UAAQhC,kBAAkB;IACxBwC,UAAUA,CACRV,IAA6BgF,UAW1B;AAAA,UAVH;QACElF;QACAZ;QACAyC;QACAsD;QACAC;QACAvE;QACAwE;QACA/F;MACF,IAAC4F;AAEDxC,iBAAWgB,IAAI1D,IAAIZ,KAAK;AACxB+B,gBAAUuC,IAAI1D,IAAI6B,QAAQ;AAC1BT,kBAAYsC,IAAI1D,IAAImF,UAAU;AAC9B9D,kBAAYqC,IAAI1D,IAAIa,MAAM;AAC1BwE,4BAAsB1C,oBAAoBe,IAAI1D,IAAIqF,kBAAkB;AAEpE,YAAMC,YAAYC,wBAAwBhH,sBAAsBwG,iCAAQS,KAAK;AAC7E,YAAMC,gBAAgBH,UAAUI,QAAQxF,EAAE;AAE1C,UAAIuF,gBAAgB,GAAInD,YAAW9B,MAAMmF,OAAOF,eAAe,GAAGzF,EAAE;UAC/DsC,YAAW9B,MAAMyB,KAAKjC,EAAE;AAE7B,YAAM4E,QAAQ9D,SAAS,MAAMoD,MAAM1D,MAAMoF,UAAUC,OAAKA,EAAE7F,OAAOA,EAAE,CAAC;AACpE,YAAM8F,SAAShF,SAAS,MAAMuB,WAAW7B,QAASmB,OAAOnB,MAAM8D,SAAS,IAAMM,MAAMpE,QAAQ,CAAE;AAE9F,YAAME,mBAAmBI,SAAwB,MAAM;AACrD,cAAMiF,eAAelE,SAASrB,UAAU,UAAUqB,SAASrB,UAAU;AACrE,cAAMwF,uBAAuBnE,SAASrB,UAAU;AAChD,cAAMyF,qBAAqBpE,SAASrB,UAAU;AAC9C,cAAMqE,OAAOO,YAAY5E,SAAS2E,WAAW3E;AAC7C,cAAM0F,OAAOrB,SAAS,IAAI,MAAM;AAEhC,cAAMsB,SAAS;UACb,CAACtE,SAASrB,KAAK,GAAG;UAClBsF,QAAQA,OAAOtF;UACf4F,WAAW,YAAYL,eAAe,MAAM,GAAG,KAAKlF,OAAOL,QAAQ,IAAI,EAAEqE,SAAS,IAAI,MAAMA,UAAUmB,wBAAwBC,qBAAqB,KAAK,EAAE,GAAGC,IAAI;UACjKrE,UAAUvC,SAASkB,SAAS6B,WAAW7B,UAAUhC,cAAc,aAAa;UAC5E,GAAI2F,mBAAmB3D,QAAQgE,SAAY;YAAEC,YAAY;UAAO;QAClE;AAEA,YAAI,CAACO,UAAUxE,MAAO,QAAO2F;AAE7B,cAAMhD,OAAOe,MAAM1D,MAAMoE,MAAMpE,KAAK;AAEpC,YAAI,CAAC2C,KAAM,OAAM,IAAIzD,MAAM,yCAAyCM,EAAE,GAAG;AAEzE,cAAMiD,UAAUF,iBAAiBvC,MAAMsB,IAAI9B,EAAE;AAC7C,YAAIiD,SAAS;AACXE,eAAKF,QAAQpB,QAAQ,KAAKoB,QAAQlB;QACpC;AAEA,eAAO;UACL,GAAGoE;UACHE,QACEN,eAAe,eAAe5C,KAAK5B,GAAG,QAAQ4B,KAAKzB,MAAM,QACvD0D,YAAY5E,QAAQ,GAAG4E,YAAY5E,KAAK,OACxCgE;UACJhD,MAAMwE,uBAAuBxB,SAAY,GAAGrB,KAAK3B,IAAI;UACrDC,OAAOuE,uBAAuB,GAAG7C,KAAK1B,KAAK,OAAO+C;UAClDjD,KAAKM,SAASrB,UAAU,WAAW,GAAG2C,KAAK5B,GAAG,OAAOiD;UACrD9C,QAAQG,SAASrB,UAAU,QAAQ,GAAG2C,KAAKzB,MAAM,OAAO8C;UACxD8B,OACE,CAACP,eAAe,eAAe5C,KAAK3B,IAAI,QAAQ2B,KAAK1B,KAAK,QACxD2D,YAAY5E,QAAQ,GAAG4E,YAAY5E,KAAK,OACxCgE;QACN;MACF,CAAC;AAED,YAAM7D,wBAAwBG,SAAwB,OAAO;QAC3DgF,QAAQA,OAAOtF,QAAQ;MACzB,EAAE;AAEF,aAAO;QAAEE;QAAkBC;QAAuBmF;MAAO;IAC3D;IACA9E,YAAahB,QAAe;AAC1B0C,iBAAW6D,OAAOvG,EAAE;AACpBmB,gBAAUoF,OAAOvG,EAAE;AACnBoB,kBAAYmF,OAAOvG,EAAE;AACrBqB,kBAAYkF,OAAOvG,EAAE;AACrB2C,0BAAoB4D,OAAOvG,EAAE;AAC7BsC,iBAAW9B,QAAQ8B,WAAW9B,MAAM0C,OAAOsD,OAAKA,MAAMxG,EAAE;IAC1D;IACAJ;IACAC;IACAF;IACAuE;IACAjD;IACAoB;EACF,CAAC;AAED,QAAMoE,gBAAgB3F,SAAS,MAAM,CACnC,YACA;IAAE,yBAAyBqB,MAAMpD;EAAW,CAAC,CAC9C;AAED,QAAM2H,eAAe5F,SAAS,OAAO;IACnCgF,QAAQ1D,eAAeC,WAAW7B,QAAQgE;IAC1C3C,UAAUO,eAAe,aAAsBoC;IAC/CmC,UAAUvE,eAAe,WAAWoC;EACtC,EAAE;AAEF,SAAO;IACLiC;IACAC;IACA/G;IACAuE;IACAjD;IACA2F,WAAWhE;EACb;AACF;", "names": ["firstDay", "AD", "AE", "AF", "AG", "AI", "AL", "AM", "AN", "AR", "AS", "AT", "AU", "AX", "AZ", "BA", "BD", "BE", "BG", "BH", "BM", "BN", "BR", "BS", "BT", "BW", "BY", "BZ", "CA", "CH", "CL", "CM", "CN", "CO", "CR", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE", "EG", "ES", "ET", "FI", "FJ", "FO", "FR", "GB", "GE", "GF", "GP", "GR", "GT", "GU", "HK", "HN", "HR", "HU", "ID", "IE", "IL", "IN", "IQ", "IR", "IS", "IT", "JM", "JO", "JP", "KE", "KG", "KH", "KR", "KW", "KZ", "LA", "LB", "LI", "LK", "LT", "LU", "LV", "LY", "MC", "MD", "ME", "MH", "MK", "MM", "MN", "MO", "MQ", "MT", "MV", "MX", "MY", "MZ", "NI", "NL", "NO", "NP", "NZ", "OM", "PA", "PE", "PH", "PK", "PL", "PR", "PT", "PY", "QA", "RE", "RO", "RS", "RU", "SA", "SD", "SE", "SG", "SI", "SK", "SM", "SV", "SY", "TH", "TJ", "TM", "TR", "TT", "TW", "UA", "UM", "US", "UY", "UZ", "VA", "VE", "VI", "VN", "WS", "XK", "YE", "ZA", "ZW", "getWeekArray", "date", "locale", "firstDayOfWeek", "weeks", "currentWeek", "firstDayOfMonth", "startOfMonth", "lastDayOfMonth", "endOfMonth", "first", "slice", "toUpperCase", "firstDayWeekIndex", "getDay", "lastDayWeekIndex", "i", "adjacentDay", "Date", "setDate", "getDate", "push", "day", "getFullYear", "getMonth", "length", "startOfWeek", "d", "endOfWeek", "lastDay", "parseLocalDate", "value", "parts", "split", "map", "Number", "_YYYMMDD", "parsed", "test", "parse", "isNaN", "sundayJanuarySecond2000", "getWeekdays", "days<PERSON><PERSON><PERSON><PERSON><PERSON>", "createRange", "weekday", "Intl", "DateTimeFormat", "format", "formatString", "formats", "newDate", "customFormat", "options", "year", "month", "NumberFormat", "hour", "hour12", "minute", "second", "timeZone", "timeZoneName", "toISO", "adapter", "toJsDate", "padStart", "String", "parseISO", "addMinutes", "amount", "setMinutes", "getMinutes", "addHours", "setHours", "getHours", "addDays", "addWeeks", "addMonths", "setMonth", "getYear", "getNextMonth", "getPrevious<PERSON><PERSON>h", "startOfYear", "endOfYear", "is<PERSON>ithinRange", "range", "isAfter", "isBefore", "<PERSON><PERSON><PERSON><PERSON>", "getTime", "comparing", "isAfterDay", "startOfDay", "isEqual", "isSameDay", "isSameMonth", "isSameYear", "getDiff", "unit", "c", "Math", "floor", "count", "setYear", "setFullYear", "endOfDay", "VuetifyDateAdapter", "constructor", "undefined", "DateOptionsSymbol", "Symbol", "for", "DateAdapterSymbol", "createDate", "options", "locale", "_options", "mergeDeep", "adapter", "VuetifyDateAdapter", "af", "bg", "ca", "ckb", "cs", "de", "el", "en", "et", "fa", "fi", "hr", "hu", "he", "id", "it", "ja", "ko", "lv", "lt", "nl", "no", "pl", "pt", "ro", "ru", "sk", "sl", "srCyrl", "srLatn", "sv", "th", "tr", "az", "uk", "vi", "zhHans", "zhHant", "instance", "createInstance", "reactive", "current", "value", "formats", "watch", "useDate", "inject", "Error", "useLocale", "getWeek", "date", "toJsDate", "year", "getFullYear", "d1w1", "Date", "tv", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "floor", "GoToSymbol", "Symbol", "for", "gen<PERSON><PERSON><PERSON><PERSON>", "container", "undefined", "duration", "layout", "offset", "easing", "patterns", "linear", "t", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "getContainer", "el", "get<PERSON><PERSON><PERSON>", "document", "scrollingElement", "body", "querySelector", "refElement", "getOffset", "target", "horizontal", "rtl", "totalOffset", "offsetLeft", "offsetTop", "offsetParent", "createGoTo", "options", "locale", "isRtl", "mergeDeep", "scrollTo", "_target", "_options", "goTo", "property", "value", "HTMLElement", "parentElement", "ease", "TypeError", "targetLocation", "styles", "window", "getComputedStyle", "layoutOffset", "getPropertyValue", "parseInt", "clampTarget", "startLocation", "Promise", "resolve", "startTime", "performance", "now", "requestAnimationFrame", "step", "currentTime", "timeElapsed", "progress", "location", "Math", "floor", "clamp", "abs", "console<PERSON>arn", "useGoTo", "arguments", "length", "goToInstance", "inject", "useRtl", "Error", "computed", "go", "scrollWidth", "scrollHeight", "containerWidth", "containerHeight", "innerWidth", "innerHeight", "offsetWidth", "offsetHeight", "min", "max", "aliases", "collapse", "complete", "cancel", "close", "delete", "clear", "success", "info", "warning", "error", "prev", "next", "checkboxOn", "checkboxOff", "checkboxIndeterminate", "delimiter", "sortAsc", "sortDesc", "expand", "menu", "subgroup", "dropdown", "radioOn", "radioOff", "edit", "ratingEmpty", "ratingFull", "ratingHalf", "loading", "first", "last", "unfold", "file", "plus", "minus", "calendar", "treeviewCollapse", "treeviewExpand", "eyeDropper", "mdi", "component", "props", "h", "VClassIcon", "class", "IconValue", "String", "Function", "Object", "Array", "IconSymbol", "Symbol", "for", "makeIconProps", "propsFactory", "icon", "type", "tag", "required", "VComponentIcon", "genericComponent", "name", "props", "setup", "_ref", "slots", "Icon", "_createVNode", "default", "VSvgIcon", "defineComponent", "inheritAttrs", "_ref2", "attrs", "_mergeProps", "isArray", "map", "path", "VLigatureIcon", "VClassIcon", "gen<PERSON><PERSON><PERSON><PERSON>", "svg", "component", "class", "createIcons", "options", "sets", "defaultSet", "mdi", "mergeDeep", "aliases", "vuetify", "useIcon", "icons", "inject", "Error", "iconData", "computed", "iconAlias", "unref", "trim", "startsWith", "slice", "console<PERSON>arn", "iconSetName", "keys", "find", "setName", "iconName", "length", "iconSet", "useResizeObserver", "callback", "box", "arguments", "length", "undefined", "resizeRef", "templateRef", "contentRect", "ref", "IN_BROWSER", "observer", "ResizeObserver", "entries", "value", "target", "getBoundingClientRect", "onBeforeUnmount", "disconnect", "watch", "el", "newValue", "oldValue", "unobserve", "observe", "flush", "readonly", "VuetifyLayoutKey", "Symbol", "for", "VuetifyLayoutItemKey", "ROOT_ZINDEX", "makeLayoutProps", "propsFactory", "overlaps", "type", "Array", "default", "fullHeight", "Boolean", "makeLayoutItemProps", "name", "String", "order", "Number", "absolute", "useLayout", "layout", "inject", "Error", "getLayoutItem", "mainRect", "mainStyles", "useLayoutItem", "options", "id", "getUid", "vm", "getCurrentInstance", "provide", "isKeptAlive", "shallowRef", "onDeactivated", "value", "onActivated", "layoutItemStyles", "layoutItemScrimStyles", "register", "active", "computed", "onBeforeUnmount", "unregister", "layoutRect", "generateLayers", "positions", "layoutSizes", "activeItems", "<PERSON><PERSON><PERSON><PERSON>", "top", "left", "right", "bottom", "layers", "layer", "position", "get", "amount", "parseInt", "push", "createLayout", "props", "parentLayout", "rootZIndex", "registered", "ref", "reactive", "Map", "priorities", "disabledTransitions", "resizeRef", "contentRect", "useResizeObserver", "computedOverlaps", "map", "overlap", "filter", "item", "includes", "split", "topPosition", "bottomPosition", "topAmount", "bottomAmount", "set", "uniquePriorities", "Set", "values", "p", "sort", "a", "b", "items", "transitionsEnabled", "from", "some", "length", "convertToUnit", "undefined", "transition", "slice", "_ref", "index", "size", "find", "rootVm", "isMounted", "onMounted", "_ref2", "layoutSize", "elementSize", "disableTransitions", "instances", "findChildrenWithProvide", "vnode", "instanceIndex", "indexOf", "splice", "findIndex", "i", "zIndex", "isHorizontal", "isOppositeHorizontal", "isOppositeVertical", "unit", "styles", "transform", "height", "width", "delete", "v", "layoutClasses", "layoutStyles", "overflow", "layoutRef"]}