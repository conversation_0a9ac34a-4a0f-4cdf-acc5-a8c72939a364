{"name": "nearest_gov_services", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@mdi/font": "^7.4.47", "@vitejs/plugin-basic-ssl": "^2.0.0", "@zxing/library": "^0.21.3", "axios": "^1.6.5", "image-conversion": "^2.1.1", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "jsencrypt": "^3.3.2", "less": "^4.2.0", "less-loader": "^12.2.0", "pdfjs-dist": "^2.16.105", "vant": "^4.9.3", "vconsole": "^3.15.1", "vue": "^3.4.35", "vue-router": "^4.2.5", "vue3-qr-reader": "^1.0.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.6", "@types/node": "^18.19.3", "@vitejs/plugin-vue": "^4.5.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^23.0.1", "npm-run-all2": "^6.1.1", "prettier": "^3.0.3", "typescript": "~5.3.0", "vite": "^5.4.4", "vite-plugin-vuetify": "^2.0.3", "vitest": "^1.0.4", "vue-tsc": "^1.8.25", "vuetify": "^3.6.14"}}