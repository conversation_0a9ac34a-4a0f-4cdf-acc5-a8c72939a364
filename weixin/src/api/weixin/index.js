import {get} from '@/request/index.js'
import {urlData} from "@/config/index.js";
import {encrypt} from "@/utils"
import md5 from 'js-md5';


class WeixinService {
    constructor() {
    }

    //列表
    async getRecomMessageList(pageNum, pageSize) {
        return await get(urlData.weixin.message, {pageNum, pageSize});
    }
    //列表
    async getMessageList(pageNum, pageSize) {
        return await get(urlData.weixin.messageOld, {pageNum, pageSize});
    }
    async getMessage(id) {
        let rel = await get(`${urlData.weixin.messageOld}/${id}`);
        return rel;
    }
    //列表
    async getCenterList(lnglat, centerType, pageNum, pageSize) {
        return await get(urlData.weixin.center, {lnglat, centerType, pageNum, pageSize});
    }
    async getType() {
        let rel = await get(`${urlData.weixin.center}/type`);
        return rel;
    }
    //列表
    async getCenterProjectList(centerId) {
        return await get(`${urlData.weixin.center}/project/${centerId}`);
    }
    // 微信实例
    async getWxConfig() {
        let time = new Date();
        let timestamp = time.getTime();
        let url = window.location.href
        let urlEncode = encodeURIComponent(url);
        let sign = encrypt(`timestamp=${timestamp}`)


        return await get(`${urlData.weixin.wxConfig}?url=${urlEncode}`, {}, {'sign': sign, 'timestamp': timestamp});
    }
    async getCurLocation() {
        return await get(urlData.weixin.ip, {key: '6OJBZ-N2EAJ-FQCFR-D37DO-RJN5V-HAFAV'});
    }

}

export default WeixinService
