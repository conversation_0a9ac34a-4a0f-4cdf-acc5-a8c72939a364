import axios from 'axios';
import router from "@/router";

const service = axios.create({
    timeout: 60000,
    headers: {
        // 'Content-Type': 'application/json; charset=utf-8',
        'Content-Type': 'application/json',
    },
});
// //请求拦截
service.interceptors.request.use(
    (config) => {
        requestMethod(config)
        return config;
    },
    (err) => {
        return Promise.reject(err);
    }
);

//响应拦截
service.interceptors.response.use(
    (res) => {
        if (res.data.code && res.data.code !== 0) {
            // toastErrorMessage(res.data.code, res.data.msg)
        }
        return res.data;

    },
    (err) => {
        let response = err.response;
        const status = response.status;
        // console.log('接口信息', err.code);
    }
);

function post(url, data, type = 'application/json') {
    const config = {
        headers: {
            'Content-Type': type,
        },
    };
    return service.post(url, data, config);
}

function upload(url, formData) {
    return service.post(url, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}

function postV4(url, data, headers) {
    let par = data ? qs.stringify(data) : '';
    return service.post(url, par, {headers: headers || {}})
}

function put(url, data) {
    const config = {
        headers: {
            'Content-Type': 'application/json',
        },
    };
    return service.put(url, data, config);
}

function get(url, data, headers) {
    const config = {
        params: data,
        maxRedirects: 10,
        timeout: 50000,
        headers: {...headers}
    };
    return service.get(url, config);
}

function del(url, data) {
    const config = {
        headers: {
            'Content-Type': 'application/json',
        },
        params: data,
        timeout: 50000,
    };
    return service.delete(url, config);
}



//接口统一处理添加参数
function requestMethod(config) {
    let i = '';
    switch (config.method) {
        case "get":
            i = config.params || {}
            break;
        case "post":
            i = config.data || {}
            break;
        case "put":
            i = config.data || config.params || {}
            break;
        case "delete":
            config.data = config.data ? config.data : config.params ? config.params : {}
            i = config.data || config.params || {}
            break;
    }
}

export {get, del, post, postV4, put, upload, service};
