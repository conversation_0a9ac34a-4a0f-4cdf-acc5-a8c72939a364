import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { FloatingBubble, Field, Swipe, SwipeItem, Icon,Button,TreeSelect, Sidebar, SidebarItem  } from 'vant';
import 'vant/lib/index.css';


// Vuetify
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

// import VConsole from 'vconsole';

// const vConsole = new VConsole();
// 或者使用配置参数来初始化，详情见文档
// vConsole()

const vuetify = createVuetify({
    ssr: true,
    components,
    directives
})


const app = createApp(App)

app.use(FloatingBubble);
app.use(Field);
app.use(Icon);
app.use(Swipe);
app.use(SwipeItem);
app.use( Sidebar);
app.use(SidebarItem);
app.use(Button);
app.use(TreeSelect);
app.use(vuetify)
app.use(router)

app.mount('#app')
