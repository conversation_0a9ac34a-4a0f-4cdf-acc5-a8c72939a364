import { createRouter, createWebHistory } from 'vue-router'
import HomeView2 from '../views/HomeView2.vue'
import HomeView from '../views/HomeView.vue'
console.info("url", import.meta.env.BASE_URL)
const router = createRouter({
  history: createWebHistory("/ngs-weixin"),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/home2',
      name: 'home2',
      component: HomeView2
    },
    {
      path: '/share',
      name: 'share',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/PromotionView.vue')
    }
  ]
})
export default router
