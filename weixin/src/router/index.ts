import { createRouter, createWebHistory } from 'vue-router'
import HomeView2 from '../views/HomeView2.vue'
import HomeView from '../views/HomeView.vue'
console.info("url", import.meta.env.BASE_URL)
const router = createRouter({
  history: createWebHistory("/ngs-weixin"),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/home2',
      name: 'home2',
      component: HomeView2
    },
    {
      path: '/promotion/:qrCode?',
      name: 'promotion',
      component: () => import('../views/PromotionView.vue'),
      props: true
    },
    {
      path: '/promotion-test',
      name: 'promotion-test',
      component: () => import('../views/PromotionTest.vue')
    },
  ]
})
export default router
