<template>
<div class='pdf-viewer' ref='pdfContent'>
  aaaa
 <canvas class='pdf-item' id='pdf-canvas'></canvas>
  aaaa
 <div class='pdf-controller'>
   aaaa
    <van-stepper style='flex-direction: column' disable-input @plus='plus' @minus='minus' v-model="state.currentPage" integer min="1" :max="state.totalPageNum" />

   aaaa333
   </div>
</div>
</template>

<script setup>
import {reactive, nextTick, ref, defineProps, onMounted, onUnmounted, watchEffect} from 'vue'
import * as pdfjs from 'pdfjs-dist'
import _ from 'lodash'
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
    'pdfjs-dist/build/pdf.worker.js',
    import.meta.url
)
import { useRoute } from 'vue-router'
import { closeToast, showLoadingToast } from 'vant'

const props = defineProps({
  pdfUrl: String
})
const url = props.pdfUrl
const emit = defineEmits(['onRendered'])
const state = reactive({
  currentPageNum: 0, // 当前页
  totalPageNum: 0,
  currentPage:1, // 当前页码
  scale: 3, // 缩放倍数
})

let pdfCtx
/**
 * @type {Ref<UnwrapRef<Element>>}
 */
const pdfContent = ref(null)

const resolvePdf = async (url) => {
  await showLoadingToast()
  alert(1)
  try {
    const loadingTask = pdfjs.getDocument(url)
    const pdf = await loadingTask.promise
    pdfCtx = pdf
    state.totalPageNum = pdf.numPages
    state.currentPageNum = 1
    const res = await pdf.getPage(1)
    await nextTick(async () => {
      await renderPdf()
    })
  } catch (e) {
    await closeToast()
    console.error('加载PDF出错', e)
  }
}
const plus=setTimeout(()=>{
  if(state.currentPage<state.totalPageNum){
    state.currentPage++
    renderPdf()
  }
},500)
const minus=setTimeout(()=>{
  if(state.currentPage>1){
    state.currentPage--
    renderPdf()
  }
},500)
const renderPdf = async () => {
  const page = await pdfCtx.getPage(state.currentPage)
  const canvas = document.getElementById('pdf-canvas')
  const ctx = canvas.getContext('2d')
  const viewport = page.getViewport({ scale: state.scale })
  canvas.width = viewport.width
  canvas.height = viewport.height
  // 画布的dom大小, 设置移动端,宽度设置铺满整个屏幕
  const clientWidth = pdfContent.value.clientWidth
  canvas.style.width = clientWidth + 'px'
  // 根据pdf每页的宽高比例设置canvas的高度
  canvas.style.height = clientWidth * (viewport.height / viewport.width) + 'px'
  page.render({ canvasContext: ctx, viewport })
  alert(1)
  if (state.currentPage >= state.totalPageNum) {
    emit('onRendered')
  }
}
const resizeCanvas = async () => {
  // for (let i = 1; i <= state.totalPageNum; i++) {
  const page = await pdfCtx.getPage(state.currentPage)
  const canvas = document.getElementById('pdf-canvas')
  const viewport = page.getViewport({ scale: state.scale })
  const clientWidth = pdfContent.value.clientWidth
  canvas.style.width = clientWidth + 'px'
  // 根据pdf每页的宽高比例设置canvas的高度
  canvas.style.height = clientWidth * (viewport.height / viewport.width) + 'px'
  // }
}
onMounted(() => {
  window.addEventListener('resize', resizeCanvas)
})
onUnmounted(() => {
  window.removeEventListener('resize', resizeCanvas)
})
watchEffect( ()=>{
  if (props.pdfUrl) resolvePdf(props.pdfUrl)
})

</script>

<style scoped>
.pdf-viewer {
  width: 100vw;
  height: 100%;
  overflow: scroll;
  scrollbar-width: none; /* firefox */
  -ms-overflow-style: none; /* IE 10+ */
  position:relative;
  .pdf-controller{
     transform:rotate(90deg);
     position: fixed;
     right: -20px;
     bottom: 50%;
      :deep(input[type="tel"]){
        transform:rotate(-90deg);
        }
  }
}

.pdf-viewer::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
</style>
