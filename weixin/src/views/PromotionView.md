# 小程序推广页面使用说明

## 功能介绍

这是一个小程序推广页面，参考微信小程序推广页面的设计风格，包含以下功能：

- 精美的渐变背景设计
- 清晰的使用步骤说明
- 支持动态传递二维码图片地址
- 响应式设计，适配各种屏幕尺寸
- 图片加载状态和错误处理
- 长按保存二维码功能

## 页面访问方式

### 1. 基础访问
```
/promotion
```
使用默认的二维码图片

### 2. 通过路由参数传递二维码
```
/promotion/https%3A%2F%2Fexample.com%2Fqrcode.png
```
注意：URL需要进行编码

### 3. 通过查询参数传递二维码
```
/promotion?qrCode=https%3A%2F%2Fexample.com%2Fqrcode.png
```

### 4. 编程方式跳转
```javascript
import { useRouter } from 'vue-router'

const router = useRouter()

// 方式1：使用路由参数
router.push({
  name: 'promotion',
  params: {
    qrCode: encodeURIComponent('https://example.com/qrcode.png')
  }
})

// 方式2：使用查询参数
router.push({
  name: 'promotion',
  query: {
    qrCode: encodeURIComponent('https://example.com/qrcode.png')
  }
})
```

## 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| qrCode | String | 否 | 二维码图片地址，支持相对路径和绝对路径 |

## 参数优先级

当同时传递多个参数时，优先级如下：
1. Props（组件属性）
2. Query参数（查询字符串）
3. Route参数（路由参数）
4. 默认值

## 示例

### 示例1：显示本地二维码
```
/promotion?qrCode=%2Fsrc%2Fassets%2Fmy-qrcode.png
```

### 示例2：显示远程二维码
```
/promotion?qrCode=https%3A%2F%2Fapi.example.com%2Fqrcode%2F123.png
```

### 示例3：在其他组件中跳转
```vue
<template>
  <button @click="goToPromotion">查看推广页面</button>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goToPromotion = () => {
  const qrCodeUrl = 'https://example.com/my-miniprogram-qrcode.png'
  router.push({
    name: 'promotion',
    query: {
      qrCode: encodeURIComponent(qrCodeUrl)
    }
  })
}
</script>
```

## 注意事项

1. **URL编码**：传递二维码地址时，必须使用 `encodeURIComponent()` 进行编码
2. **图片格式**：支持常见的图片格式（jpg, png, gif, webp等）
3. **跨域问题**：如果使用外部图片，需要确保服务器支持跨域访问
4. **图片大小**：建议二维码图片尺寸为正方形，最小200x200像素
5. **加载失败**：当图片加载失败时，页面会显示错误提示信息

## 自定义配置

如需修改页面内容，可以编辑 `PromotionView.vue` 文件中的以下部分：

- `appTitle`：页面标题
- `steps`：使用步骤说明
- `bottomText`：底部提示文字
- CSS样式：修改颜色、字体、布局等

## 技术实现

- Vue 3 Composition API
- Vue Router 4
- 响应式设计
- CSS3 动画和渐变
- 图片懒加载和错误处理
