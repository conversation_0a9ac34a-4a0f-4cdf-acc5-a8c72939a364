<script setup>

import { reactive, ref, getCurrentInstance, onMounted, computed, watchEffect, nextTick, onBeforeUnmount, watch } from "vue";
import { weixinService } from '@/api'
import { wgs_gcj_encrypts } from '@/utils'
import {get} from '@/request/index.js'
import { useRouter } from 'vue-router';
import { showSuccessToast, showFailToast } from 'vant';
const router = useRouter();
import location_marker_img from "@/assets/marker/map-marker-radius.svg"
import town_marker_img from "@/assets/marker/SUB_CENTER.svg"
import machine_marker_img from "@/assets/marker/SELF_MACHINE.svg"
import bank_marker_img from "@/assets/marker/BANK.svg"
import water_marker_img from "@/assets/marker/WATER_GAS.svg"
import n1 from "@/assets/number/1.svg"
import n2 from "@/assets/number/2.svg"
import n3 from "@/assets/number/3.svg"
import n4 from "@/assets/number/4.svg"
import n5 from "@/assets/number/5.svg"
import n6 from "@/assets/number/6.svg"
import n7 from "@/assets/number/7.svg"
import n8 from "@/assets/number/8.svg"
import n9 from "@/assets/number/9.svg"
import n10 from "@/assets/number/10.svg"

let urlImg = "/ngs-upload"
onMounted(() => {
  onLoad();
})

const showInfo = ref(false)
const showOtherList = ref(false)
const showSmallInfo = ref(false)
const centerInfo = ref({})
const showNotice = ref(true)
const noticeList = ref([])
// const imagePathPrefix = ref("/upload-file")
const noticeIndex = ref(0)
const centerList = ref([])
const filterCenters = ref([])
const projectList = ref([])
const sortIcons = ref([n1, n2, n3, n4, n5, n6, n7, n8, n9, n10])
let map;
let zoom = 11;
const win_height = window.innerHeight;

const showSearch = ref(false)
const dialogSearchData = ref([])
const searchTxt = ref("")

const showList = ref(false)
/**
 * 中心类型 1：镇街、社区服务中心；2：自助机；3：政银合作点；4：水电气网
 */

const centerType = ref('SUB_CENTER')
const centerTypeList = ref({});
const dialogListData = ref([])
let CENTER_LAT_LNG = {
  lat: 30.18693,
  lng: 120.25989
}

// 位置
const positionData = reactive({
  //定位成功标识
  flag: false,
  latitude: 0,
  longitude: 0,
})

let timer = null;
let timeCount = 0;
let zoomTimer = null;
const zoomend = function (e) {
  let _zoom = e.target.fW;
  console.info("zoom", _zoom)
  getCenterList();
  showCenter(_zoom)
}

const waterTypeList = ref([])
const ltype = ref(0)
const changeLeft = (index) => {
  let _type = waterTypeList.value[index];
  console.info(waterTypeList.value[index])
  if (_type) {
    let arr = [];
    for (let i = 0, len = filterCenters.value.length; i < len; i++) {
      let _c = filterCenters.value[i];
      if (_c.centerName.indexOf(_type) != -1) {
        arr.push(_c)
      }
    }
    console.info({centerList: centerList.value})
    centerList.value = arr;
  }else {
    centerList.value = filterCenters.value;
  }
  showCenter();
}
const onLoad = () => {
  timeCount = 0;
  map = new T.Map('mapDiv');
  // map.centerAndZoom(new T.LngLat(CENTER_LAT_LNG.lng, CENTER_LAT_LNG.lat), zoom);
  map.centerAndZoom(new T.LngLat(120.27144, 30.13191), zoom);
  // map.addEventListener("zoomend", (e) => {
  //   console.info({zoomend: e});
  //   let _zoom = e.target.fW;
  //   getCenterList();
  //   showCenter(_zoom)
  // });
  map.addEventListener("zoomend", (e) => {
    console.info({zoomend: e});
   if (zoomTimer) {
     console.info("clearZoomTimer")
     clearTimeout(zoomTimer)
   }
   console.info("zoomTimer")
   zoomTimer = setTimeout(() => {zoomend(e)}, 3000)
  });

  getMessageList();
  getCenterType();
  getCenterList();
  setCurMarker()
  if (isWechat()) {
    getWxConfigPage()
  }
  doLocation();
  timer = setInterval(() => {
    if (positionData.flag || timeCount > 12) {
      clearInterval(timer);
      console.info("退出定位")
      return;
    }
    timeCount++;
    console.info("重新定位")
    // if (isWechat()) {
      doLocation();
    // }else {
    //   getWebLocation();
    // }
  }, 5000);
  // getCurrentPosition1();
  // getCurrentPosition2();

  // nextTick(() => {
  //   if (positionData.latitude == 0) {
  //   }else{
  //     getCenterList(positionData.latitude + "," + positionData.longitude);
  //   }
  // })
}
// watch(positionData, (newVal, oldVal) => {
//   getCenterList(newVal.latitude + "," + newVal.longitude);
// })

const showCenterList = (_centerType) => {
  centerType.value = _centerType;
  console.info({centerTypeList})
  waterTypeList.value = centerTypeList.value[_centerType].expandData.subs ||[]
  ltype.value = 0;
  showSmallInfo.value = false;
  getCenterList();
}

const showOnline = (_url) => {
  location.href = _url;
}

/**
 * 浏览器定位
 */
const getWebLocation = () => {
  weixinService.getCurLocation().then(res => {
    if (res.success) {
      let _latlng = res.data;
      if (_latlng) {
        positionData.flag = true;
        positionData.latitude = _latlng.lat;
        positionData.longitude = _latlng.lng;
        console.info({positionData})
        setCurMarker()
        getCenterList();
      }
    }else {
      positionData.latitude = CENTER_LAT_LNG.lat;
      positionData.longitude = CENTER_LAT_LNG.lng;
    }
  })
}

function isWechat() {
  return /MicroMessenger/i.test(window.navigator.userAgent);
}
function isIOS() {
  const userAgent = navigator.userAgent;
  return /iPhone|iPad|ipod/i.test(userAgent);
}


function getCurrentPosition1 () {
  console.log('获取定位1');

  var e = new T.Geolocation;
  function a(e) {
    console.log("获取定位坐标：" + e.lnglat.lat + "," + e.lnglat.lng)
    positionData.longitude = e.lnglat.lng
    positionData.latitude = e.lnglat.lat
  }
  e.getCurrentPosition(a)
}
function getCurrentPosition2 () {
  console.log('获取定位2');
  debugger
  var lc = new T.LocalCity();
  lc.location(function (e) {
    console.info(e.lnglat);
  });
}


const getWxConfigPage = (lnglat) => {
  weixinService.getWxConfig().then(res => {
    if (res.success) {
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: res.data.appId, // 必填，公众号的唯一标识
        timestamp: res.data.timestamp, // 必填，生成签名的时间戳
        nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
        signature: res.data.signature,// 必填，签名
        jsApiList: [
          "openLocation", "makePhoneCall", "getLocation"
        ] // 必填，需要使用的JS接口列表
      });
    } else {
      console.error('加载微信API失败，msg：' + res.msg);
    }
  })
}
const getCenterList = () => {
  let lnglat;
  if (positionData.flag) {
    lnglat = {
      lat: positionData.latitude,
      lng: positionData.longitude
    }
  }else {
    lnglat = CENTER_LAT_LNG
  }
  weixinService.getCenterList(lnglat.lat + "," + lnglat.lng, centerType.value, 1, 999).then(res => {
    if (res.success) {
      centerList.value = res.data?.list ||[];
      filterCenters.value = res.data?.list || [];
      filterCenters.value.sort((a, b) => a.centerName.localeCompare(b.centerName));
      changeLeft(ltype.value)
      // let kmArr = res.data?.list.filter(i => i.distance?.indexOf('km') >= 0 )
      // let mArr = res.data?.list.filter(i => i.distance?.indexOf('km') == -1 )
      // let mSort = mArr.sort((a, b) => {
      //   let numA = a.distance.match(/[\d|.]/g).join("")
      //   let numB = b.distance.match(/[\d|.]/g).join("")
      //   return numA - numB
      // });
      // let kmSort = kmArr.sort((a, b) => {
      //   let numA = a.distance.match(/[\d|.]/g).join("")
      //   let numB = b.distance.match(/[\d|.]/g).join("")
      //   return numA - numB
      // });
      // dialogListData.value = [...mSort, ...kmSort];
      dialogListData.value = res.data?.list;
      showCenter();
    } else {
      showFailToast('加载服务中心数据失败');
    }
  })
}
const getMessageList = () => {
  weixinService.getRecomMessageList(0, 5).then(res => {
    if (res.success) {
      noticeList.value = (res.data || []).map(message => message.title);
      setTimeout(noticeChange, 300)
    } else {
      showFailToast('加载消息数据失败');
    }
  })
}
const getCenterType = () => {
  weixinService.getType().then(res => {
    if (res.success) {
      let _data = res.data;
      for (let i = 0; i < _data.length; i++) {
        let _d = _data[i];
        _d.expandData = JSON.parse(_d.expandData)
        centerTypeList.value[_d.cod] = _d;
      }
    } else {
      showFailToast('加载分类数据失败');
    }
  })
}
const toLocation = () => {
  map.panTo({
    lat: positionData.latitude,
    lng: positionData.longitude
  });
}
const doLocation = () => {
  let lo = new T.Geolocation();
  lo.getCurrentPosition(function (e) {
    console.info("获取定位结果", { e })
    if (e.lnglat) {
        console.info("获取定位坐标：" + e.lnglat.lat + "," + e.lnglat.lng)
        positionData.flag = true;
        positionData.latitude = e.lnglat.lat
        positionData.longitude = e.lnglat.lng
        getCenterList();
        setCurMarker();
    }else {
      positionData.latitude = CENTER_LAT_LNG.lat;
      positionData.longitude = CENTER_LAT_LNG.lng;
    }
  });
}
let curMarker = null;
const setCurMarker = () => {
  if (curMarker) {
    map.removeOverLay(curMarker)
  }
  let lnglat;
  if (positionData.flag) {
    lnglat = {
      lat: positionData.latitude,
      lng: positionData.longitude
    }
  }else {
    lnglat = CENTER_LAT_LNG
  }
  // map.panTo(lnglat)
  let icon = new T.Icon({
    iconUrl: location_marker_img,
    iconSize: new T.Point(30, 30),
    iconAnchor: new T.Point(10, 25)
  });
  curMarker = new T.Marker(lnglat, { icon });
  //向地图上添加标注
  map.addOverLay(curMarker);
}

const showCenter = (_zoom) => {
  if (!_zoom) {
    _zoom = zoom;
  }
  map.clearOverLays();
  setCurMarker();
  let _icon = town_marker_img;
  if (centerType.value == 'SELF_MACHINE' ) {
    _icon= machine_marker_img;
  }else if (centerType.value == 'BANK') {
    _icon = bank_marker_img;
  }else if (centerType.value == 'WATER_GAS') {
    _icon = water_marker_img;
  }
  for (let i = 0, len = centerList.value.length; i < len; i++) {
    let _center = centerList.value[i];
    if (_center.latlng) {
      let icon = new T.Icon({
        iconUrl: _icon,
        iconSize: new T.Point(45, 40),
        iconAnchor: new T.Point(10, 25)
      });
      let _latlng = _center.latlng.split(",");
      //向地图上添加自定义标注
      let marker = new T.Marker({ lat: _latlng[0], lng: _latlng[1] }, { icon: icon });
      marker.addEventListener("click", () => {
        clickInfo(_center);
      });// 将标注添加到地图中
      // 添加文字
      let label = new T.Label({
        text: `<b style="background: transparent">${_center.centerName}<b>`,
        position: marker.getLngLat(),
        offset: new T.Point(15, -10)
      });
      if (_zoom > 10) {
        map.addOverLay(label)
      }

      map.addOverLay(marker);
    }
  }
}
const clickInfo = (center) => {
  showSmallInfo.value = false;
  centerInfo.value = center
  setTimeout(() => {
    showSmallInfo.value = true;
  }, 50)
}

const showDetail = (info) => {
  projectList.value = [];
  centerInfo.value = info
  weixinService.getCenterProjectList(info.id).then(res => {
    // debugger
    if (res.success) {
      projectList.value = res.data || [];
    } else {
      showFailToast('加载消息数据失败');
    }
  })
  showInfo.value = true;
}
const toNoticeList = () => {
  router.push("/notice")
}

const noticeChange = () => {
  if (noticeList.value.length > 1) {
    setTimeout(() => {
      showNotice.value = false;
      if (noticeIndex.value === noticeList.value.length - 1) {
        noticeIndex.value = 0;
      } else {
        noticeIndex.value += 1;
      }
      showNotice.value = true;
      noticeChange();
    }, 3000);
  }

}

const listChange = (e) => {
  let val = e.target.value,
    arr = [];
  console.info({val})
  if (val == '') {
    filterCenters.value.forEach(i => {
      arr.push(i)
    })
  } else {
    filterCenters.value.forEach(i => {
      if (i.centerName.includes(val)) {
        arr.push(i)
      }
    })
  }
  dialogListData.value = arr;
}
const searchChange = (e) => {
  let val = e.target.value,
    arr = [];

  if (val == '') {
    arr = []
  } else {
    centerList.value.forEach(i => {
      if (i.centerName.includes(val)) {
        arr.push(i)
      }
    })
  }
  dialogSearchData.value = arr;
}

const listClose = () => {
  showList.value = false
  // searchTxt.value = ''
  // dialogListData.value = centerList.value;
}
const searchClose = () => {
  showSearch.value = false
  searchTxt.value = ''
  dialogSearchData.value = []
}

const onNavigation = (_center) => {
  let _latlng = _center.latlng.split(",");
  if (isWechat()) {
    let latlngList = wgs_gcj_encrypts([{ lat: parseFloat(_latlng[0]), lng: parseFloat(_latlng[1]) }])
    let latlng = latlngList[0]
    wx.openLocation({ latitude: latlng.lat, longitude: latlng.lng, scale: 15, name: _center.centerName })
  } else {
    showFailToast('请在微信中使用此功能');
  }
}
const onCall = (tel) => {
  window.location.href = "tel:" + tel
  wx.makePhoneCall({
    phoneNumber: tel
  })
}

const formatDistance = (str) => {
  if(!str) return
  let num = str.match(/[\d|.]/g).join("")
  let unit = str.match(/[a-zA-Z]/g).join("")
  return Number(num).toFixed(2) + unit
}


onBeforeUnmount(() => {
  if (timer) clearInterval(timer)
})

</script>

<template>
  <van-swipe style="
  position: absolute;
  top: 16px;
  backdrop-filter: blur(10px);
  width: 90%;
  height: 36px;
  left: 5%;
  border-radius: 999px;
  z-index: 999; background-color: rgba(255, 251, 232, 0.7);" vertical :autoplay="3000" :touchable="false" v-if="noticeList.length" :show-indicators="false">
    <van-swipe-item v-for="(item, index) in noticeList" :key="index" class="notice-item" @click="toNoticeList">
      <div class="notice-item-info">
        <van-icon class="notice-item-icon" name="warning-o" />
        <span>{{ item }}</span>
      </div>
      <van-icon class="notice-item-right" name="arrow" />
    </van-swipe-item>
  </van-swipe>

  <div id="mapDiv" style="position:absolute;width:100%; height:100%"></div>
  <!-- 右边功能按钮 -->
  <v-icon color="#353535" icon="mdi-crosshairs-gps" size="35" @click="toLocation"
    style="position: absolute; right: 2.5%; bottom: 200px; z-index: 999"></v-icon>
  <div class="right-btn">
    <div class="search-btn" @click="showList = true">
      <img src="@/assets/image/search.png" alt="">
      <span>列表</span>
    </div>
    <div class="search-btn" @click="toNoticeList">
      <img src="@/assets/image/msg.png" alt="">
      <span>消息</span>
    </div>
    <div class="search-btn" @click="showOtherList = true" style="height: 60px">
      <img src="@/assets/image/link.png" alt="">
      <span>业务<br/>地图</span>
    </div>
  </div>
  <div style="position: absolute; top: 1rem; z-index: 9999; width: 100%; text-align: center;" v-if="!showList && !showInfo">
    <van-button v-if="centerTypeList['SUB_CENTER'] && centerTypeList['SUB_CENTER'].expandData.show" :type="centerType == 'SUB_CENTER' ? 'primary' : 'default'" @click="showCenterList('SUB_CENTER')">首 页</van-button>
    <van-button v-if="centerTypeList['SELF_MACHINE'] && centerTypeList['SELF_MACHINE'].expandData.show" :type="centerType == 'SELF_MACHINE' ? 'primary' : 'default'" @click="showCenterList('SELF_MACHINE')" style="margin: 0 0.1rem">自助机</van-button>
    <van-button :type="centerType == 'BANK' ? 'primary' : 'default'" @click="showCenterList('BANK')">政银合作点</van-button>
    <van-button v-if="centerTypeList['WATER_GAS']" :type="centerType == 'WATER_GAS' ? 'primary' : 'default'" @click="showCenterList('WATER_GAS')" style="margin-left: 0.1rem">水电气网</van-button>
  </div>
  <van-sidebar v-model="ltype" style="position: absolute; top: 5rem; z-index: 9999;" v-if="!showList && waterTypeList.length" @change="changeLeft">
    <van-sidebar-item v-for="ltitle in waterTypeList" :title="ltitle" />
  </van-sidebar>
  <div class="bottom_info">
    <div v-show="showSmallInfo" class="show_small_info">
      <v-col cols="auto" style="position: absolute; right: 0px; top: 0px">
        <v-btn icon="mdi-close" size="x-small" @click="showSmallInfo = false"></v-btn>
      </v-col>
      <div style="padding-left: 15px;padding-top: 20px">
        <h3 class="show_small_info_title">{{ centerInfo.centerName }}</h3>
        <div class="show_small_info_tel" style="font-size: 14px;">
          {{centerInfo.addr }}
        </div>
        <div class="show_small_info_time" v-if="(centerInfo.workTime||'').trim()"><v-icon icon="mdi-clock-outline" class="mar_right" size="20"></v-icon> {{
          centerInfo.workTime }}
        </div>
        <div class="show_small_info_tel" v-if="centerTypeList[centerInfo.centerType] && centerTypeList[centerInfo.centerType].expandData && centerTypeList[centerInfo.centerType].expandData.tel">
          <v-icon icon="mdi-phone-in-talk-outline" class="mar_right" size="20"></v-icon>
          <span class="color_active" @click="onCall(centerInfo.tel)">{{ centerInfo.tel }}</span>
        </div>

        <div class="show_small_info_footer">
          <div style="display: none">
            <span>距您</span>
            <span class="txt_deleted color_active">{{ formatDistance(centerInfo.distance) }}</span>
          </div>
          <div>
            <span></span>
            <span class="txt_deleted color_active"></span>
          </div>
          <div>
            <v-btn class="me-2 text-none" color="#298adf" prepend-icon="mdi-navigation-variant" variant="flat"
                   v-if="centerTypeList[centerType] && centerTypeList[centerType].expandData && centerTypeList[centerType].expandData.project"
              @click="onNavigation(centerInfo)">导航</v-btn>
            <v-btn type="success" class="me-2 text-none" color="#1fb533"
              prepend-icon="mdi-information-variant-circle-outline" variant="flat"
              v-if="centerTypeList[centerInfo.centerType] && centerTypeList[centerInfo.centerType].expandData && centerTypeList[centerInfo.centerType].expandData.project"
              @click="showDetail(centerInfo)">详情</v-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 列表 -->
  <v-dialog v-model="showList" transition="dialog-bottom-transition" fullscreen>
    <div class="dialog-search">
      <div class="search-input">
        <div class="search-input-box">
          <img src="@/assets/image/search.png" alt="">
          <van-Field class="search-val" v-model="searchTxt" @input="listChange" />
        </div>
      </div>
      <div class="list-box">
        <div class="show_small_info list-item" v-for="(item, index) in dialogListData" :key="index">
          <div style="padding-left: 15px;padding-top: 20px">
            <h3 class="show_small_info_title">{{ item.centerName }}</h3>
            <div class="show_small_info_tel" style="font-size: 14px;">
              {{item.addr }}
            </div>
            <div class="show_small_info_line"></div>
            <div class="show_small_info_time" v-if="(centerInfo.workTime||'').trim()"><v-icon icon="mdi-clock-outline" class="mar_right" size="20"></v-icon> {{
              item.workTime }} </div>
            <div class="show_small_info_tel" v-if="centerTypeList[centerType] && centerTypeList[centerType].expandData && centerTypeList[centerType].expandData.tel"><v-icon icon="mdi-phone-in-talk-outline" class="mar_right"
                size="20"></v-icon> <span class="color_active" @click="onCall(centerInfo.tel)">{{ item.tel }}</span> </div>

            <div class="show_small_info_footer">
              <div style="display: none">
                <span>距您</span>
                <span class="txt_deleted color_active">{{ formatDistance(item.distance) }}</span>
              </div>
              <div>
                <span></span>
                <span class="txt_deleted color_active"></span>
              </div>
              <div>
                <v-btn class="me-2 text-none" color="#298adf" prepend-icon="mdi-navigation-variant" variant="flat"
                       v-if="centerTypeList[centerType] && centerTypeList[centerType].expandData && centerTypeList[centerType].expandData.nav != false"
                  @click="onNavigation(item)" >导航</v-btn>
                <v-btn type="success" class="me-2 text-none" color="#1fb533"
                       v-if="centerTypeList[centerType] && centerTypeList[centerType].expandData && centerTypeList[centerType].expandData.project"
                  prepend-icon="mdi-information-variant-circle-outline" variant="flat"
                  @click="showDetail(item)">详情</v-btn>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-floating-bubble style="z-index: 9999; background-color: #77beff" magnetic="x" axis="xy" :offset="{ y: 360 }">
      <v-icon icon="mdi-arrow-left" @click="listClose"></v-icon>
    </van-floating-bubble>
  </v-dialog>
  <!-- 搜索 -->
  <v-dialog v-model="showSearch" transition="dialog-bottom-transition" fullscreen>
    <div class="dialog-search">
      <div class="search-input">
        <div class="search-input-box">
          <img src="@/assets/image/search.png" alt="">
          <van-Field class="search-val" v-model="searchTxt" @input="searchChange" />
        </div>
      </div>
      <div class="list-box">
        <div class="show_small_info list-item" v-for="(item, index) in dialogSearchData" :key="index">
          <div style="padding-left: 15px;padding-top: 20px">
            <h3 class="show_small_info_title">{{ item.centerName }}</h3>
            <div class="show_small_info_time"><v-icon icon="mdi-clock-outline" class="mar_right" size="20"></v-icon> {{
              item.workTime }} </div>
            <div class="show_small_info_tel"><v-icon icon="mdi-phone-in-talk-outline" class="mar_right"
                size="20"></v-icon> <span class="color_active" @click="onCall(centerInfo.tel)">{{ item.tel }}</span> </div>

            <div class="show_small_info_footer">
              <div>
                <span>距您</span>
                <span class="txt_deleted color_active">{{ formatDistance(item.distance) }}</span>
              </div>
              <div>
                <v-btn class="me-2 text-none" color="#298adf" prepend-icon="mdi-navigation-variant" variant="flat"
                  @click="onNavigation(item)">导航</v-btn>
                <v-btn type="success" class="me-2 text-none" color="#1fb533"
                  prepend-icon="mdi-information-variant-circle-outline" variant="flat"
                  @click="showDetail(item)">详情</v-btn>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-floating-bubble style="z-index: 9999; background-color: #77beff" magnetic="x" axis="xy" :offset="{ y: 360 }">
      <v-icon icon="mdi-arrow-left" @click="searchClose"></v-icon>
    </van-floating-bubble>
  </v-dialog>
  <!-- 详情 -->
  <v-dialog v-model="showInfo" transition="dialog-bottom-transition" fullscreen>
    <v-card>
      <v-card-title style="padding: 0px">
        <v-carousel :show-arrows="false" :height="240" cycle hide-delimiter-background>
          <v-carousel-item :key="1" :src="urlImg + centerInfo.layout" ></v-carousel-item>
          <v-carousel-item :key="1" :src="urlImg + centerInfo.img" ></v-carousel-item>
        </v-carousel>
      </v-card-title>
      <v-card-title>
        {{ centerInfo.centerName }}
      </v-card-title>

      <v-card-subtitle style="margin-bottom: 10px">
        {{ centerInfo.addr }}
      </v-card-subtitle>
      <v-card-text style="background-color: #f1f1f1;padding: 16px 12px">
        <van-floating-bubble style="z-index: 9999; background-color: #77beff" magnetic="x" axis="xy" :offset="{ y: 360 }">
          <v-icon icon="mdi-arrow-left" @click="showInfo = false"></v-icon>
        </van-floating-bubble>

        <van-floating-bubble style="z-index: 9999; background-color: #77beff" magnetic="x" axis="xy" :offset="{ y: 440 }">
          <v-icon icon="mdi-navigation-variant" @click="onNavigation(centerInfo)"></v-icon>
        </van-floating-bubble>
        <v-card class="card-item">
          <v-card-title style="font-size: 1.2em">
            <v-icon icon="mdi-calendar-clock" size="20"></v-icon><span style="margin-right: 10px">工作时间</span>
          </v-card-title>
          <v-card-text>
            {{ centerInfo.workTime }}
          </v-card-text>
        </v-card>
        <v-card class="card-item" v-if="centerTypeList[centerInfo.centerType] && centerTypeList[centerInfo.centerType].expandData && centerTypeList[centerInfo.centerType].expandData.tel">
          <v-card-title style="font-size: 1.2em">
            <v-icon icon="mdi-phone-in-talk-outline" size="20"></v-icon><span style="margin-right: 10px">联系电话</span>
          </v-card-title>
          <v-card-text>
            <span class="color_active" @click="onCall(centerInfo.tel)">{{ centerInfo.tel }}</span>
          </v-card-text>
        </v-card>
        <v-card class="card-item">
          <v-card-title style="font-size: 1.2em">
            <v-icon icon="mdi-invoice-list-outline" size="20"></v-icon>高频事项
          </v-card-title>
          <v-list lines="one">
            <div v-for="(pinfo, pindex) in projectList" :key="pindex">
              <v-list-item :key="pinfo.id" :prepend-avatar="sortIcons[pindex]" v-if="pindex < 10">
                <template v-slot:prepend>
                  <div class="list-item-index color_active"> {{ pindex + 1 }}</div>
                </template>
                <van-button type="primary" size="mini" style="float: right;" v-if="pinfo.onlineUrl" @click="showOnline(pinfo.onlineUrl)">在线办理</van-button>
                <div slot="title" class="list-item-title">{{ pinfo.projectName }}</div>
                <div slot="subtitle" class="list-item-describe">{{ pinfo.projectDesc }}</div>
              </v-list-item>
              <v-divider v-show="pindex != (projectList.length - 1)" inset style="margin-inline-start: 3rem"></v-divider>
            </div>
          </v-list>
        </v-card>
      </v-card-text>
    </v-card>
  </v-dialog>
  <v-dialog v-model="showOtherList">
    <v-card
        class="mx-auto"
        prepend-icon="mdi-link"
        title="其他相关业务地图"
    ><v-divider></v-divider>
      <v-banner
          color="deep-purple-accent-4"
          lines="one"
      >
        <template v-slot:prepend>
          <v-avatar
              color="deep-purple-accent-4"
          >医保</v-avatar>
        </template>
        <v-banner-text>
          杭州市医保地图
        </v-banner-text>
        <template v-slot:actions>
          <v-btn href="https://www.hz12393.com/fwjz/#/areaGrid2?aab301=330109">打开</v-btn>
        </template>
      </v-banner>
      <v-banner
          color="deep-purple-accent-4"
          lines="one"
      >
        <template v-slot:prepend>
          <v-avatar
              color="#d72976"
          >公安</v-avatar>
        </template>
        <v-banner-text>
          浙警在线
        </v-banner-text>
        <template v-slot:actions>
          <v-btn href="https://portal.zjzwfw.gov.cn/download/?_aaid=4&preferredContainer=zlb&goto=zwfw%3A%2F%2Fwebview%3Fh5Url%3Dhttps%3A%2F%2Fmapi.zjzwfw.gov.cn%2Fweb%2Fmgop%2Fgov-open%2Fzj%2F2002311518%2Freserved%2Findex.html%3F_atid%3D_9a8f8e1d20c558302c2c73f5ddf5a1ac%26_aaid%3D4%26siteType%3D0%26siteCode%3D%26_atid_code%3D%26site_id%3D%26page_name%3D%25E7%25AB%2599%25E5%25A4%2596%25E6%258E%25A8%25E5%25B9%25BF%26first_use%3D%26day_first_use%3D%26forbidLoadBack%3Dyes%26spmCode%3D%26_atid%3D_9a8f8e1d20c558302c2c73f5ddf5a1ac%26_aaid%3D4">打开</v-btn>
        </template>
      </v-banner>
      <v-banner
          color="deep-purple-accent-4"
          lines="one"
      >
        <template v-slot:prepend>
          <v-avatar
              color="#0741ba"
          >税务</v-avatar>
        </template>
        <v-banner-text>
          办税地图
        </v-banner-text>
        <template v-slot:actions>
          <v-btn href="https://12366.chinatax.gov.cn/wap/pages/taxmap/tax-map.html">打开</v-btn>
        </template>
      </v-banner>
      <v-banner
          color="deep-purple-accent-4"
          lines="one"
      >
        <template v-slot:prepend>
          <v-avatar
              color="#2590f3"
          >就业</v-avatar>
        </template>
        <v-banner-text>
          就业创业一张图
        </v-banner-text>
        <template v-slot:actions>
          <v-btn href="https://hzjy.hrss.hangzhou.gov.cn/yzt/?type=zfbszjy#/pages/home/<USER>">打开</v-btn>
        </template>
      </v-banner>
    </v-card>
  </v-dialog>
</template>
<style lang="less">
.van-button--primary {
  color: white !important;
}
.notice-item{
  display: flex;
  align-items: center;
  font-size: 18px;
  justify-content: space-between;
  padding: 0 10px 0 10px;
  .notice-item-right{
    font-size: 16px;
    margin: 2px 0 0;
  }
  .notice-item-info{
    color: #FF0000;
    display: flex;
    align-items: center;
    width: 90%;
    .notice-item-icon{
      margin-right: 6px;
    }
    span{
      display: inline-block;
      flex: 1;
      overflow:hidden;
      text-overflow:ellipsis;
      white-space:nowrap;
    }
  }
}
.notice-swiper {
  position: fixed;
  // width: 90%;
  height: 50px;
  top: 16px;
  left: 5%;
  // backdrop-filter: blur(10px);
  // border-radius: 999px;
  // z-index: 999;
  // padding: 2px 2px 0 12px;

  // top: 0px;
  // left: 5%;
  // backdrop-filter: blur(10px);
}

.list-box {
  width: 100%;
  height: calc(100% - 60px);
  overflow-y: auto;
  padding: 0 0 20px;

  .list-item {
    // border: 1px solid #ccc;
    width: 96%;
    height: fit-content;
    background-color: #fff;
    margin: 16px 2% 0;
    border-radius: 4px;
    box-shadow: 1px 4px 5px rgba(0, 0, 0, 0.1);
    padding: 0 0 10px;

    .show_small_info_footer {
      width: 100%;
      display: flex;
      margin: 4px 0 0;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.dialog-search {
  --van-cell-background: #F6F7F8;
  width: 100%;
  height: 100%;
  background-color: #F6F7F8;

  .search-input {
    background-color: #fff;
    width: 100%;
    height: 60px;
    padding-top: 10px;

    .search-input-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 90%;
      height: 40px;
      margin: 0 5%;
      background-color: #F6F7F8;
      border-radius: 999px;

      .search-val {
        width: calc(100% - 60px);
      }

      img {
        width: 28px;
        height: 28px;
        margin: 0 0 0 11px;
      }

      input {
        height: 100%;
      }
    }
  }
}

.right-btn {
  position: absolute;
  right: 2.5%;
  top: 200px;
  z-index: 999;

  .search-btn {
    width: 40px;
    height: 40px;
    background-color: #fff;
    border-radius: 4px;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.85);
    box-shadow: 2px 4px 10px 0px rgba(0, 0, 0, 0.1);
    padding: 4px 0 0;
    margin-bottom: 10px;

    img {
      width: 20px;
      height: 20px;
      display: block;
      margin: 0 auto;
    }

    span {
      font-size: 12px;
      display: block;
      text-align: center;
      color: #298ADF;
    }
  }
}

.show_small_info_line {
  width: calc(100% - 15px);
  height: 1px;
  background-color: #ccc;
  margin: 4px;
}

.card-item {
  margin-top: 10px;

  .list-item-index {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 1rem;
    text-align: center;
    margin: 0 0.5rem 0 0;
    border: 1px solid #0669F8;
    border-radius: 999px;
  }

  .list-item-title {
    font-weight: bold;
  }

  .list-item-describe {
    font-size: 14px;
    color: #777;
  }
}

.bottom_info {
  position: absolute;
  z-index: 999;
  bottom: 20px;
  width: 95%;
  left: 2.5%;

  .show_small_info {
    width: 100%;
    height: fit-content;
    padding: 0 0 20px;
    backdrop-filter: blur(10px);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.7);
    box-shadow: 2px 4px 10px 0px rgba(0, 0, 0, 0.1);
    animation-duration: 0.3s;
    animation-name: showHide;

    @keyframes showHide {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }


    .show_small_info_title {
      color: rgb(66, 66, 66);
    }

    .show_small_info_time {
      color: #666;
    }

    .show_small_info_tel {
      color: #666;
    }

    .mar_right {
      margin-right: 4px;
    }

    .txt_deleted {
      font-size: 24px;
      margin-left: 5px;
    }

    .show_small_info_footer {
      width: 100%;
      display: flex;
      margin: 4px 0 0;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.color_active {
  color: #0669F8;
}

/*
动画激活时给予初始状态，这个基础状态用于定义后续过渡动作的起点
这里统一定义了组件创建和组件销毁的两个起点，主要是赋予 transition 使其有一个过渡
*/
.roll-enter-active,
.roll-leave-active {
  transition: all .3s;
  position: absolute;
  top: 0;
}

/*
  组件销毁，过渡的终点
  最开始的起点是原位，离开的时候滚到上面去
  */
.roll-leave-to {
  top: -30px;
}

/*
  组件创建，过渡的起点
  创建的时候是重下往上进入，所以元素最开始是在下面
  */
.roll-enter-active {
  top: 30px;
}

/*
  过渡的时候回到起点，达到重下往上滚动
  */
.roll-enter-to {
  top: 0;
}

.roll-container {
  position: relative;
  line-height: 30px;
  height: 30px;
  width: 100%;
  overflow: hidden;
  color: #ff4747;
  font-size: 16px;
}

.roll-number {
  width: 100%;
  display: flex;
  flex-flow: row;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;

  span {
    margin-left: 6px;
  }
}
.van-sidebar-item {
  width: 3.5rem;
}
.van-sidebar-item--select {
  color: white !important;
  background-color: #458dfd !important;
}
</style>
