<script setup>
import { ref, onMounted } from 'vue'

// 页面数据
const appTitle = ref('发现精彩应用')
const steps = ref([
  {
    number: 1,
    text: '长按二维码，选择保存图片到相册'
  },
  {
    number: 2,
    text: '打开微信扫一扫，点击刷保存到相册中的二维码'
  }
])

// 二维码图片URL - 这里需要替换为实际的二维码图片
const qrCodeUrl = ref('/src/assets/qr-code.png')
const bottomText = ref('📱 微信扫码立即体验')

onMounted(() => {
  // 页面加载完成后的逻辑
  console.log('推广页面加载完成')
})

// 长按保存图片功能
const handleLongPress = () => {
  // 在微信环境中，用户可以长按保存图片
  console.log('长按保存二维码')
}

// 点击二维码的处理
const handleQrClick = () => {
  console.log('点击二维码')
}
</script>

<template>
  <div class="promotion-container">
    <!-- 背景装饰 -->
    <div class="background-decoration"></div>
    
    <!-- 主要内容 -->
    <div class="content-wrapper">
      <!-- 标题 -->
      <h1 class="app-title">{{ appTitle }}</h1>
      
      <!-- 步骤说明 -->
      <div class="steps-container">
        <div 
          v-for="step in steps" 
          :key="step.number"
          class="step-item"
        >
          <div class="step-number">{{ step.number }}</div>
          <div class="step-text">{{ step.text }}</div>
        </div>
      </div>
      
      <!-- 二维码区域 -->
      <div class="qr-code-container">
        <div class="qr-code-wrapper">
          <img 
            :src="qrCodeUrl" 
            alt="小程序二维码"
            class="qr-code-image"
            @click="handleQrClick"
            @touchstart="handleLongPress"
          />
          <!-- 微信图标 -->
          <div class="wechat-icon">
            <svg viewBox="0 0 24 24" class="wechat-svg">
              <path d="M8.5 12.5c-.5 0-1-.5-1-1s.5-1 1-1 1 .5 1 1-.5 1-1 1zm7 0c-.5 0-1-.5-1-1s.5-1 1-1 1 .5 1 1-.5 1-1 1zm-3.5-8C6.5 4.5 2 8.4 2 13.2c0 2.8 1.5 5.3 3.8 6.8-.2-.8-.4-2.1.1-3 .4-.8 2.6-4.5 2.6-4.5s-.1.4-.1.9c0 1.6 1.3 2.9 2.9 2.9s2.9-1.3 2.9-2.9-1.3-2.9-2.9-2.9c-.4 0-.8.1-1.1.2 1.8-1.6 4.2-2.6 6.8-2.6 4.5 0 8.2 3.2 8.2 7.1 0 3.9-3.7 7.1-8.2 7.1-.9 0-1.7-.1-2.5-.4-2.1 1.4-4.7 1.4-6.8 0-.8.3-1.6.4-2.5.4C3.7 20.3 0 17.1 0 13.2 0 8.4 4.5 4.5 10 4.5h2z"/>
            </svg>
          </div>
        </div>
      </div>
      
      <!-- 底部提示文字 -->
      <div class="bottom-text">{{ bottomText }}</div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.promotion-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

.content-wrapper {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.app-title {
  font-size: 32px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 40px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
}

.steps-container {
  margin-bottom: 40px;
}

.step-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 20px;
  padding: 0 20px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #4285f4;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  margin-right: 16px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
}

.step-text {
  color: #ffffff;
  font-size: 16px;
  line-height: 1.5;
  text-align: left;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.qr-code-container {
  margin: 40px 0;
  display: flex;
  justify-content: center;
}

.qr-code-wrapper {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 280px;
  width: 100%;
}

.qr-code-image {
  width: 100%;
  height: auto;
  border-radius: 8px;
  display: block;
  background: #f5f5f5;
  min-height: 240px;
  object-fit: contain;
}

.wechat-icon {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: #07c160;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
}

.wechat-svg {
  width: 24px;
  height: 24px;
  fill: white;
}

.bottom-text {
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .app-title {
    font-size: 28px;
    margin-bottom: 30px;
  }
  
  .step-text {
    font-size: 15px;
  }
  
  .qr-code-wrapper {
    max-width: 260px;
    padding: 16px;
  }
  
  .bottom-text {
    font-size: 16px;
  }
}

/* 长按效果 */
.qr-code-image:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}
</style>
