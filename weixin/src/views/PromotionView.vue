<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'

// 定义props
const props = defineProps({
  qrCode: {
    type: String,
    default: ''
  }
})

// 获取路由信息
const route = useRoute()

// 页面数据
const appTitle = ref('发现精彩应用')
const steps = ref([
  {
    number: 1,
    text: '长按二维码，选择保存图片到相册'
  },
  {
    number: 2,
    text: '打开微信扫一扫，点击刷保存到相册中的二维码'
  }
])

// 二维码图片URL - 支持多种方式传递
const qrCodeUrl = computed(() => {
  // 优先级：路由参数 > props > query参数 > 默认值
  if (props.qrCode) {
    return decodeURIComponent(props.qrCode)
  }
  if (route.query.qrCode) {
    return decodeURIComponent(route.query.qrCode)
  }
  if (route.params.qrCode) {
    return decodeURIComponent(route.params.qrCode)
  }
  // 返回默认的二维码占位图片（SVG格式）
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDI0MCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyNDAiIGhlaWdodD0iMjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjMzMzIi8+CjxyZWN0IHg9IjMwIiB5PSIzMCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxyZWN0IHg9IjQwIiB5PSI0MCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMzMzIi8+CjxyZWN0IHg9IjE2MCIgeT0iMjAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgZmlsbD0iIzMzMyIvPgo8cmVjdCB4PSIxNzAiIHk9IjMwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9IiNGNUY1RjUiLz4KPHJlY3QgeD0iMTgwIiB5PSI0MCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMzMzIi8+CjxyZWN0IHg9IjIwIiB5PSIxNjAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgZmlsbD0iIzMzMyIvPgo8cmVjdCB4PSIzMCIgeT0iMTcwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9IiNGNUY1RjUiLz4KPHJlY3QgeD0iNDAiIHk9IjE4MCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjEyMCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiPuekuuS+i+S6jOe7tOeggeWbvueJhzwvdGV4dD4KPC9zdmc+'
})

const bottomText = ref('📱 微信扫码立即体验')

onMounted(() => {
  // 页面加载完成后的逻辑
  console.log('推广页面加载完成')
  console.log('二维码地址:', qrCodeUrl.value)
})

// 图片加载状态
const imageLoaded = ref(false)
const imageError = ref(false)

// 图片加载成功
const handleImageLoad = () => {
  imageLoaded.value = true
  imageError.value = false
}

// 图片加载失败
const handleImageError = () => {
  imageLoaded.value = false
  imageError.value = true
  console.error('二维码图片加载失败:', qrCodeUrl.value)
}

// 长按保存图片功能
const handleLongPress = () => {
  // 在微信环境中，用户可以长按保存图片
  console.log('长按保存二维码')
}

// 点击二维码的处理
const handleQrClick = () => {
  console.log('点击二维码')
}
</script>

<template>
  <div class="promotion-container">
    <!-- 背景装饰 -->
    <div class="background-decoration"></div>

    <!-- 主要内容 -->
    <div class="content-wrapper">
      <!-- 标题 -->
      <h1 class="app-title">{{ appTitle }}</h1>

      <!-- 步骤说明 -->
      <div class="steps-container">
        <div
          v-for="step in steps"
          :key="step.number"
          class="step-item"
        >
          <div class="step-number">{{ step.number }}</div>
          <div class="step-text">{{ step.text }}</div>
        </div>
      </div>

      <!-- 二维码区域 -->
      <div class="qr-code-container">
        <div class="qr-code-wrapper">
          <!-- 加载中状态 -->
          <div v-if="!imageLoaded && !imageError" class="qr-code-loading">
            <div class="loading-spinner"></div>
            <div class="loading-text">加载中...</div>
          </div>

          <!-- 二维码图片 -->
          <img
            v-show="imageLoaded && !imageError"
            :src="qrCodeUrl"
            alt="小程序二维码"
            class="qr-code-image"
            @click="handleQrClick"
            @touchstart="handleLongPress"
            @load="handleImageLoad"
            @error="handleImageError"
          />

          <!-- 错误状态 -->
          <div v-if="imageError" class="qr-code-error">
            <div class="error-icon">⚠️</div>
            <div class="error-text">二维码加载失败</div>
            <div class="error-url">{{ qrCodeUrl }}</div>
          </div>

        </div>
      </div>

      <!-- 底部提示文字 -->
      <div class="bottom-text">{{ bottomText }}</div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.promotion-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

.content-wrapper {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.app-title {
  font-size: 32px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 40px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
}

.steps-container {
  margin-bottom: 40px;
}

.step-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 20px;
  padding: 0 20px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #4285f4;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  margin-right: 16px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
}

.step-text {
  color: #ffffff;
  font-size: 16px;
  line-height: 1.5;
  text-align: left;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.qr-code-container {
  margin: 40px 0;
  display: flex;
  justify-content: center;
}

.qr-code-wrapper {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 280px;
  width: 100%;
}

.qr-code-image {
  width: 100%;
  height: auto;
  border-radius: 8px;
  display: block;
  background: #f5f5f5;
  min-height: 240px;
  object-fit: contain;
}

.qr-code-loading {
  width: 100%;
  min-height: 240px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #4285f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 14px;
}

.qr-code-error {
  width: 100%;
  min-height: 240px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-text {
  color: #ff4444;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.error-url {
  color: #999;
  font-size: 12px;
  word-break: break-all;
  line-height: 1.4;
}

.wechat-icon {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: #07c160;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
}

.wechat-svg {
  width: 24px;
  height: 24px;
  fill: white;
}

.bottom-text {
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .app-title {
    font-size: 28px;
    margin-bottom: 30px;
  }

  .step-text {
    font-size: 15px;
  }

  .qr-code-wrapper {
    max-width: 260px;
    padding: 16px;
  }

  .bottom-text {
    font-size: 16px;
  }
}

/* 长按效果 */
.qr-code-image:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}
</style>
