<template>
  <div class="test-container">
    <h1>推广页面测试</h1>
    
    <div class="test-section">
      <h2>测试不同的二维码地址</h2>
      
      <div class="test-item">
        <h3>1. 默认二维码</h3>
        <button @click="goToPromotion()">访问推广页面（默认）</button>
      </div>
      
      <div class="test-item">
        <h3>2. 自定义二维码地址</h3>
        <input 
          v-model="customQrCode" 
          placeholder="输入二维码图片地址"
          class="qr-input"
        />
        <button @click="goToPromotion(customQrCode)">访问推广页面（自定义）</button>
      </div>
      
      <div class="test-item">
        <h3>3. 预设测试地址</h3>
        <div class="preset-buttons">
          <button 
            v-for="preset in presetUrls" 
            :key="preset.name"
            @click="goToPromotion(preset.url)"
            class="preset-btn"
          >
            {{ preset.name }}
          </button>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h2>当前测试信息</h2>
      <div class="info-box">
        <p><strong>当前路由：</strong>{{ $route.path }}</p>
        <p><strong>最后测试的二维码：</strong>{{ lastQrCode || '无' }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 自定义二维码地址
const customQrCode = ref('')
const lastQrCode = ref('')

// 预设的测试地址
const presetUrls = ref([
  {
    name: '微信官方示例',
    url: 'https://res.wx.qq.com/wxdoc/dist/assets/img/demo.ef5c5bef.jpg'
  },
  {
    name: '本地图片',
    url: '/src/assets/logo.svg'
  },
  {
    name: '错误地址（测试错误处理）',
    url: 'https://example.com/nonexistent.png'
  }
])

// 跳转到推广页面
const goToPromotion = (qrCodeUrl = '') => {
  lastQrCode.value = qrCodeUrl
  
  if (qrCodeUrl) {
    router.push({
      name: 'promotion',
      query: {
        qrCode: encodeURIComponent(qrCodeUrl)
      }
    })
  } else {
    router.push({
      name: 'promotion'
    })
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  color: #555;
  border-bottom: 2px solid #4285f4;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

h3 {
  color: #666;
  margin-bottom: 10px;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 8px;
}

.test-item {
  margin-bottom: 25px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.qr-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 14px;
}

button {
  background: #4285f4;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

button:hover {
  background: #3367d6;
}

.preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.preset-btn {
  background: #34a853;
  font-size: 12px;
  padding: 8px 16px;
}

.preset-btn:hover {
  background: #2d8f47;
}

.info-box {
  background: white;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #4285f4;
}

.info-box p {
  margin: 8px 0;
  color: #555;
}

@media (max-width: 600px) {
  .test-container {
    padding: 15px;
  }
  
  .preset-buttons {
    flex-direction: column;
  }
  
  .preset-btn {
    width: 100%;
  }
}
</style>
