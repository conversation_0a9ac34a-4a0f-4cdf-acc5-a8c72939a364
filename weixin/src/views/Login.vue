<script setup>
import {onMounted, ref} from "vue";
import LoginUtil from '@/request/LoginUtil';
import router from "@/router";

onMounted(() => {
  let token = LoginUtil.getToken();
  if (token) {
    router.push("manage")
  }
  // getItemList();
})

const show = true;
const username = ref("")
const password = ref("")
const onSubmit = () => {
  LoginUtil.login({username: username.value, password: password.value});
}
</script>

<template>
  <van-dialog v-model:show="show" width="100%" :showConfirmButton="false" :overlayStyle="{'backgroundColor': '#eff2f5'}">
    <van-nav-bar title="登 录" />
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field
            v-model="username"
            name="用户名"
            label="用户名"
            placeholder="用户名"
            clearable
            :rules="[{ required: true, message: '请填写用户名' }]"
        />
        <van-field
            v-model="password"
            type="password"
            name="密 码"
            label="密 码"
            placeholder="密 码"
            clearable
            :rules="[{ required: true, message: '请填写密码' }]"
        />
      </van-cell-group>
      <div style="margin: 16px;">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>

  </van-dialog>
</template>

<style scoped>

</style>
