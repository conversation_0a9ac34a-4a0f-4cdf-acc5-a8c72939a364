<script setup>

import {reactive, ref, getCurrentInstance, onMounted, computed} from "vue";
import {showFailToast, showToast, showConfirmDialog} from 'vant';
import {weixinService, manageService} from '@/api'
import LoginUtil from '@/request/LoginUtil';
import router from "@/router";
import * as imageConversion from 'image-conversion'
import up_video from "@/assets/up_vedio.png"
import up_img from "@/assets/up_img.png"

onMounted(() => {
  let token = LoginUtil.getToken();
  if (!token) {
    router.push("login")
  }
  // getItemList();
})
const imageWidth = ref((document.body.clientWidth * 0.4) + "px");
const uploadWidth = ref((document.body.clientWidth * 0.3) + "px");
window.onresize = () => {
  imageWidth.value = (document.body.clientWidth * 0.4) + "px";
  uploadWidth.value = (document.body.clientWidth * 0.3) + "px";
}
const getItemList = () => {
  loading.value = true;
  weixinService.getItemList(pageInfo.value.pageNumber, pageInfo.value.pageSize, activeMenu.value + 1).then((res) => {
    loading.value = false;
    if (res.success) {
      pageInfo.value = res.data;
      list.value.push(...pageInfo.value.records);
      if (pageInfo.value.totalPage <= pageInfo.value.pageNumber) {
        finished.value = true;
      }
    }else {
      showFailToast('数据加载失败，' + res.code + ":" + res.msg);
    }
  }).catch(error => {
    finished.value = true;
    loading.value = false;
    showFailToast('数据加载失败，' + error.code + ":" + error.msg);
  })
}

const pageInfo = ref({
  "records": [],
  "pageNumber": 0,
  "pageSize": 10,
  "totalPage": 0,
  "totalRow": 0
})
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const activeMenu = ref(0);
const showView = ref(false)
const showInfo = ref({})

const imageBasePath="/item-image/"
const showViewModal = (_item) => {
  isEdit.value = _item ? true : false;
  if (_item) {
    item.value.itemNo = _item.itemNo;
    item.value.id = _item.id;
    item.value.videoUrl = _item.videoUrl;
    editImageList.value = [..._item.files||[]];
    editVideo.value = _item.videoUrl;
  }else {
    item.value.itemNo = '';
    item.value.id = null;
    editImageList.value = [];
    editVideo.value = "";
  }
  showView.value = true;
}
const onLoad = () => {
  pageInfo.value.pageNumber += 1;
  getItemList();
};
const swipeIndex = ref(0);
const swipeChange = (index) => {
  swipeIndex.value = index;
}
const typeChange = () => {
  isChangeInfo = false;
  pageInfo.value.pageNumber = 1;
  list.value = []
  getItemList();
}
const beforeRead = async (file) => {
  // 此时可以自行将文件上传至服务器
  // console.log(file)
  file.status = 'uploading'
  file.message = '上传中...'

  const formdata = new FormData()
  if (file.size > 2 * 1024 * 1024) {
    const blob = await imageConversion.compressAccurately(file, 2 * 1024)
    // Blob转FormData
    // append第三个参数为可选项，此处本项目接口需要
    formdata.append('file', blob, file.name)
  }else {
    formdata.append('file', file, file.name)
  }
  // 将图片精确压缩到600kb
  formdata.append("itemId", item.value.id)
  formdata.append("type", 1)
  // 调用fileUpload接口
  const rel = await manageService.upload(formdata)
  // 此处code为接口定义状态码，本项目成功为0
  if (rel.success) {
    showToast("上传图片成功")
    isChangeInfo = true;
    // file.status = 'done'
    // file.message = '上传成功'
    setTimeout(() => {
      showViewModal(rel.data)
    }, 300)
    // console.log(rel)
  } else {
    showFailToast("上传图片失败")
    // file.status = 'failed'
    // file.message = '上传失败'
  }
}
const beforeReadVedio = async (file) => {
  // 此时可以自行将文件上传至服务器
  // console.log(file)
  file.status = 'uploading'
  file.message = '上传中...'
  if (file.size > 100 * 1024 * 1024) {
    showFailToast('视频文件过大，大小不能超过100M！');
    file.status = 'failed'
    file.message = '上传失败'
    return false;
  }
  // 将图片精确压缩到600kb
  // const blob = await imageConversion.compressAccurately(file, 600)
  // Blob转FormData
  const formdata = new FormData()
  // append第三个参数为可选项，此处本项目接口需要
  formdata.append('file', file, file.name)
  formdata.append("itemId", item.value.id)
  formdata.append("type", 2)
  // 调用fileUpload接口
  const rel = await manageService.upload(formdata)
  // 此处code为接口定义状态码，本项目成功为0
  if (rel.success) {
    showToast("添加视频成功")
    isChangeInfo = true;
    file.status = 'done'
    file.message = '上传成功'
    showViewModal(rel.data)
    // console.log(rel)
  } else {
    file.status = 'failed'
    file.message = '上传失败'
  }
}
const isEdit = ref(true)
const item = ref({
  itemNo: '',
  id: null,
  videoUrl: ""
})
const editImageList = ref([])
const editVideo = ref("")
let isChangeInfo = false;
const maxImageSize = ref(9);
const onSubmit = () => {
  manageService.saveItem({...item.value, itemType: activeMenu.value + 1}).then(res => {
    if (res.success) {
      showToast("保存成功")
      isChangeInfo = true;
      showViewModal(res.data)
    }else {
      showFailToast({message: '保存产品失败，' + res.code + ":" + res.msg, duration: 5 * 1000})
    }
  }).catch(error => {
    showFailToast({message: '保存产品失败，' + error.code + ":" + error.msg, duration: 5 * 1000})
  })
}
const mainFile = (img) => {
  showConfirmDialog({
    title: '操作提示',
    message:
        '确定要将此图片设置为主图吗?',
  }).then(() => {
    manageService.mainFile(img.id).then(res => {
      if (res.success) {
        showToast("设置主图成功")
        isChangeInfo = true;
        showViewModal(res.data)
      }else {
        showFailToast({message: '设置主图失败，' + res.code + ":" + res.msg, duration: 5 * 1000})
      }
    }).catch(error => {
      showFailToast({message: '设置主图失败，' + error.code + ":" + error.msg, duration: 5 * 1000})
    })
  }).catch(() => {
  });
}
const delFile = (img) => {
  showConfirmDialog({
    title: '删除提示',
    message:
        '确定要删除此图片吗?',
  }).then(() => {
    manageService.delItemFile(img.id).then(res => {
      if (res.success) {
        showToast("删除产品图片成功")
        isChangeInfo = true;
        showViewModal(res.data)
      }else {
        showFailToast({message: '删除产品图片失败，' + res.code + ":" + res.msg, duration: 5 * 1000})
      }
    }).catch(error => {
      showFailToast({message: '删除产品图片失败，' + error.code + ":" + error.msg, duration: 5 * 1000})
    })
  }).catch(() => {
  });

}
const delItem = () => {
  showConfirmDialog({
    title: '删除提示',
    message:
        '确定要删除' + item.value.itemNo + "吗?",
  }).then(() => {
    manageService.delItem(item.value.id).then(res => {
      if (res.success) {
        showToast("删除成功")
        isChangeInfo = true;
        showView.value = false;
      }else {
        showFailToast({message: '删除产品附件失败，' + res.code + ":" + res.msg, duration: 5 * 1000})
      }
    }).catch(error => {
      showFailToast({message: '删除产品附件失败，' + error.code + ":" + error.msg, duration: 5 * 1000})
    })
  }).catch(() => {
  });
}
const closePopup = () => {
  if (isChangeInfo) {
    typeChange();
  }
}
</script>

<template>
    <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        style="padding-top: 10px"
    >
      <van-row>
        <van-col span="12" v-for="_item in list">
          <div class="card-main" :key="_item" @click="showViewModal(_item)">
            <van-image
                :style="{height: imageWidth, backgroundColor: 'white', width: '100%'}"
                fit="cover"
                :src="imageBasePath + (_item.files.length ? _item.files[0].fileName : '')"
                radius="8px 8px 0px 0px"
            >
              <template v-slot:loading>
                <van-loading type="spinner" size="20" />
              </template>
              <template v-slot:error style="height: 100%; width: 100%">加载失败</template>
            </van-image>
            <div class="card-title">{{_item.itemNo}}</div>
          </div>
        </van-col>
      </van-row>
    </van-list>
  <van-back-top bottom="55" />

  <van-popup
      v-model:show="showView"
      position="bottom"
      closeable
      :style="{ height: '100%',backgroundColor: '#eff2f5' }"
      @closed="closePopup"
  >
    <van-nav-bar :title="item.itemNo" />
    <van-form @submit="onSubmit" style="padding: 15px 10px" >
      <van-cell-group inset>
        <van-field
            v-model="item.itemNo"
            name="编码"
            label="编码"
            placeholder="编码"
            clearable
            :rules="[{ required: true, message: '请填写编码' }]"
        />
      </van-cell-group>
      <van-cell-group inset style="margin-top: 5px">
        <van-field
            v-model="item.videoUrl"
            name="视频地址"
            label="视频地址"
            placeholder="视频地址"
            type="textarea"
            :autosize="{ maxHeight: 150, minHeight: 100 }"
            clearable
        />
      </van-cell-group>
      <div style="margin: 16px; text-align: right">
        <van-button  type="danger" style="margin-right: 20px; width: 20%" v-if="isEdit" @click="delItem">删 除</van-button>
        <van-button  type="primary" native-type="submit" style="width: 60%">
          保 存
        </van-button>
      </div>
    </van-form>
    <div v-if="isEdit">
      <van-notice-bar
          left-icon="volume-o"
          :text="`最多可上传${maxImageSize}张图片。`"
      />
      <van-cell-group inset title="">
        <template #title>
          产品图片
        </template>
      </van-cell-group>
      <van-row gutter="6">
        <van-col span="8" v-for="img in editImageList" :style="{height: uploadWidth, padding: '1%'}">
          <van-image
              width="100%"
              height="100%"
              fit="contain"
              :src="imageBasePath + img.fileName"
          >
            <div style="position: absolute;top: 0px; background-color: rgb(238 242 245 / 68%); padding: 3px" @click="delFile(img)">
              <van-icon name="delete-o" size="25" color="red"  />
            </div>
            <div style="position: absolute;top: 0px; right: 0px; background-color: rgb(238 242 245 / 68%); padding: 3px" @click="mainFile(img)">
              <van-icon name="certificate" size="25" color="green" style="font-weight: bold" v-if="img.defaultFlag"/>
              <van-icon name="certificate" size="25" color="#8c8c8c" v-else/>
            </div>
          </van-image>
        </van-col>
        <van-col span="8" v-if="editImageList.length < maxImageSize" :style="{height: uploadWidth, padding: '1%'}">
          <van-uploader :before-read="beforeRead" style="height: 100%; width: 100%; background-color: #f7f8fa;" :upload-icon="up_img">
          </van-uploader>
        </van-col>
      </van-row>
    </div>

  </van-popup>
  <van-floating-bubble
      axis="xy"
      icon="add-o"
      magnetic="x"
      @click="showViewModal(false)"
  />
  <van-tabbar v-model="activeMenu" @change="typeChange">
    <van-tabbar-item icon="list-switch">被壳</van-tabbar-item>
    <van-tabbar-item icon="list-switching">羽绒</van-tabbar-item>
  </van-tabbar>
</template>
<style>

.van-uploader__upload {
  width: 100% !important;
  height: 100% !important;
}
.van-uploader__wrapper {
  width: 100% !important;
  height: 100% !important;
}
.card-main {
  background-color: rgb(6 10 61 / 70%);
  border-radius: 8px 8px 5px 5px;
  margin: 10px 5px;
}
.card-title {
  font-weight: bold;
  padding-bottom: 10px;
  width: 100%;
  text-align: center;
  color: white;
}
</style>
