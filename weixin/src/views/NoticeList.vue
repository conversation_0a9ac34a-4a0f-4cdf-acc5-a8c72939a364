<script setup>

import {reactive, ref, getCurrentInstance, onMounted, computed, watchEffect, nextTick} from "vue";
import {weixinService} from '@/api'
import { useRouter } from 'vue-router';
import {showFailToast} from "vant";
const router = useRouter();

onMounted(() => {
  onLoad();
})
const  items = ref([])
const notice_info_dialog = ref(false)
const messageInfo = ref({})
const onLoad = () => {
  getMessageList();
}
const toHome = () => {
  router.push("/")
}
const showNoticeInfo = (data) => {
  weixinService.getMessage(data.id).then(res => {
    if (res.success && res.data) {
      messageInfo.value = res.data;
      notice_info_dialog.value = true;
    }else {
      showFailToast("获取消息数据失败，")
    }
  }).catch(error => {
    showFailToast("获取消息数据失败，")
  })
}
const getMessageList = () => {
  weixinService.getMessageList(0, 10).then(res => {
    if (res.success) {
      items.value = res.data?.list;
    }else {
      showFailToast('加载消息数据失败');
    }
  })
}
</script>

<template>
  <!-- <v-layout> -->
    <div class="back-home" @click="toHome">
      <img src="@/assets/image/left.png" alt="" >
      <span>通知公告</span>
    </div>
    <!-- <v-app-bar color="deep-purple accent-4">
      <template v-slot:prepend>
        <v-icon icon="mdi-home" size="35"  color="#fff"></v-icon>
      </template>

      <v-app-bar-title style="color:#fff;font-weight: bold">通知公告</v-app-bar-title>

      <v-spacer></v-spacer>
    </v-app-bar> -->

    <v-list class="back-color" style="width: 100%;">
      <v-list-item v-for="(item, index) in items" :key="index" class="notice-item" >
        <v-card @click="showNoticeInfo(item)">
          <template v-slot:title>
            <div style="width: 100%;white-space: break-spaces">{{item.title}}</div>
          </template>
          <template v-slot:subtitle>
            <div style="width: 100%; text-align: right">{{item.createDate}}</div>
          </template>
        </v-card>
      </v-list-item>
    </v-list>
  <!-- </v-layout> -->
  <v-dialog
      v-model="notice_info_dialog"
      transition="dialog-bottom-transition"
      fullscreen
  >
    <v-card>
      <template v-slot:title>
        <div style="width: 100%; text-align: center;white-space: break-spaces">{{messageInfo.title}}</div>
      </template>
      <template v-slot:subtitle>
        <div style="width: 100%; text-align: right">{{messageInfo.createDate}} 发布</div>
      </template>
      <template v-slot:text>
        <v-divider></v-divider>
        <van-floating-bubble  style="z-index: 9999; background-color: #77beff" magnetic="x" axis="xy" :offset="{y:260}">
        <v-icon icon="mdi-arrow-left" @click="notice_info_dialog = false"></v-icon>
      </van-floating-bubble>
        <div style="height: calc(100vh - 9rem); overflow-y: auto;padding: 10px 0;" v-html="messageInfo.content">
        </div>
      </template>
    </v-card>
  </v-dialog>
</template>
<style lang="less">
.back-home{
  width: 100%;
  display: flex;
  align-items: center;
  height: 48px;
  border-bottom: 1px solid #ccc;
  position: relative;
  img{
    width: 20px;
    height: 20px;
    margin: 0 0 0 16px;
  }
  span{
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    display: inline-block;
    margin: 0 0 0 0;
    font-weight: 600;
  }
}
.back-color{
  background-color: #EFF2F5;
}
.notice-item{
  background-color: #EFF2F5;
}
</style>
