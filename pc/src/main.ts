import { createApp } from 'vue'
import App from './App.vue'
import router from './router'


// Vuetify
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'


const vuetify = createVuetify({
    ssr: true,
    components,
    directives
})


const app = createApp(App)

app.use(vuetify)
app.use(router)

app.mount('#app')
