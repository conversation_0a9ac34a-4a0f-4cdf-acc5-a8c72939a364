<script setup>
import {onMounted, ref} from "vue";
import LoginUtil from '@/request/LoginUtil';
import router from "@/router";

onMounted(() => {
  let token = LoginUtil.getToken();
  if (token) {
    router.push("manage")
  }
  // getItemList();
})

const show = true;
const username = ref("")
const password = ref("")
const onSubmit = () => {
  LoginUtil.login({username: username.value, password: password.value});
}
</script>

<template>
  <v-app>
    <v-main>
      <v-container>
        <v-row>
          <van-col cols="18">12</van-col>
          <van-col cols="18">12</van-col>
        </v-row>
      </v-container>
    </v-main>
  </v-app>
</template>

<style scoped>

</style>
