<script setup>

import {reactive, ref, getCurrentInstance, onMounted, computed, watchEffect, nextTick} from "vue";
import {weixinService} from '@/api'
import { useRouter } from 'vue-router';
const router = useRouter();
import location_marker_img from "@/assets/map-marker-radius.svg"
import town_marker_img from "@/assets/town-marker.svg"

onMounted(() => {
  onLoad();
})
const showInfo = ref(false)
const showSmallInfo = ref(false)
const showNotice = ref(true)
const noticeList = ref([
    "1将标注添加到地图中",
    "2将标注添加到地图中",
    "3将标注添加到地图中",
    "4将标注添加到地图中",
])
const images = ref([
    "https://cdn.vuetifyjs.com/images/cards/docks.jpg",
    "https://cdn.vuetifyjs.com/images/cards/hotel.jpg",
])
const noticeIndex = ref(0)
let map;
let zoom = 12;
const win_height = window.innerHeight;
const onLoad = () => {
  map = new T.Map('mapDiv');
  map.centerAndZoom(new T.LngLat(120.25989,30.18693), zoom);
  //创建标注对象
  var icon = new T.Icon({
    iconUrl: town_marker_img,
    iconSize: new T.Point(45, 40),
    iconAnchor: new T.Point(10, 25)
  });
  var marker = new T.Marker(new T.LngLat(120.25989,30.18693), {icon});
  marker.addEventListener("click", function () {
    clickInfo();
  });// 将标注添加到地图中
  //向地图上添加标注
  map.addOverLay(marker);
  doLocation(true);
  noticeChange();
}

const doLocation = (flag) => {
  var lo = new T.Geolocation();
  lo.getCurrentPosition(function (e) {
    console.info("获取定位结果", {e})
    if (e.lnglat){
      if (flag) {
        console.info("获取定位坐标："+e.lnglat.lat + "," + e.lnglat.lng)
        var icon = new T.Icon({
          iconUrl: location_marker_img,
          iconSize: new T.Point(30, 30),
          iconAnchor: new T.Point(10, 25)
        });
        //向地图上添加自定义标注
        var marker = new T.Marker(e.lnglat, {icon: icon});
        map.addOverLay(marker);
      }
      map.panTo(e.lnglat)
    }
  });
}
const clickInfo = () => {
  showSmallInfo.value = true;
}

const showDetail = () => {
  showInfo.value = true;
}
const toNoticeList = () => {
  router.push("/notice")
}

const noticeChange = () => {
  console.info(noticeIndex.value)
  if (noticeList.value.length > 1) {
    setTimeout(() => {
      showNotice.value = false;
      if (noticeIndex.value === noticeList.value.length - 1) {
        noticeIndex.value = 0;
      } else {
        noticeIndex.value += 1;
      }
      showNotice.value = true;
      noticeChange();
    }, 3000);
  }

}

</script>

<template>
    <v-banner
        sticky
        lines="one"
        style="    position: absolute;
    top: 0px;
    background-color: #fffbe8;
    z-index: 999;"
        v-if="noticeList.length"
        @click="toNoticeList"
    >
      <template v-slot:text>
        <div class="roll-container">
          <transition name="roll">
            <div class="roll-number" :key="noticeIndex"><v-icon size="20" icon="mdi-alert-circle-outline"></v-icon> {{ noticeList[noticeIndex] }}</div>
          </transition>
        </div>

      </template>

      <template v-slot:actions>
          <v-btn icon="mdi-greater-than" size="x-small"></v-btn>
      </template>
    </v-banner>
<!--  <van-floating-bubble style="background-color: #77beff" magnetic="x" axis="xy" :offset="{y: win_height - 240}">-->
<!--    <v-icon icon="mdi-crosshairs-gps" size="30" @click="doLocation"></v-icon>-->
<!--  </van-floating-bubble>-->

  <div id="mapDiv" style="position:absolute;width:100%; height:100%"></div>
    <v-icon
        color="#353535"
        icon="mdi-crosshairs-gps"
        size="35" @click="doLocation"
        style="position: absolute; right: 2.5%; bottom: 200px; z-index: 999"
    ></v-icon>
  <div class="bottom_info">
    <div v-if="showSmallInfo" class="show_small_info">
      <v-col cols="auto" style="position: absolute; right: 0px; top: 0px">
        <v-btn icon="mdi-close" size="x-small" @click="showSmallInfo = false"></v-btn>
      </v-col>
      <div style="padding-left: 15px;padding-top: 20px">
        <div><h3>萧山区盈丰街道办事服务中心</h3></div>
        <div style="font-size: 16px"><v-icon icon="mdi-clock-outline" size="20"></v-icon> 周一至周五9:00—16:00</div>
        <div style="font-size: 16px"><v-icon icon="mdi-phone-in-talk-outline" size="20"></v-icon> 13388888888 0571-87777777</div>

        <div style="float: right; position: absolute; right: 10px; bottom: 10px">
          <v-btn
              class="me-2 text-none"
              color="#298adf"
              prepend-icon="mdi-navigation-variant"
              variant="flat"
          >
            导航
          </v-btn>
          <v-btn
              type="success"
              class="me-2 text-none"
              color="#1fb533"
              prepend-icon="mdi-information-variant-circle-outline"
              variant="flat"
              @click="showDetail"
          >
            详情
          </v-btn>
        </div>
      </div>
    </div>
  </div>
  <!-- 详情 -->
  <v-dialog
      v-model="showInfo"
      transition="dialog-bottom-transition"
      fullscreen
  >

    <v-card>
      <v-card-title style="padding: 0px">
        <v-carousel :show-arrows="false" :height="240"
                    cycle
                    hide-delimiter-background>
          <v-carousel-item
              v-for="(item,i) in images"
              :key="i"
              :src="item"
              cover
          ></v-carousel-item>
        </v-carousel>
      </v-card-title>
      <v-card-title>
        萧山区盈丰街道办事服务中心
      </v-card-title>

      <v-card-subtitle>
        浙江省杭州市萧山区盈丰街道金鸡路2465号
      </v-card-subtitle>
      <v-card-text style="background-color: rgb(130,130,130,0.2);padding: 16px 12px">
        <van-floating-bubble  style="z-index: 9999; background-color: #77beff" magnetic="x" axis="xy" :offset="{y:260}">
          <v-icon icon="mdi-arrow-left" @click="showInfo = false"></v-icon>
        </van-floating-bubble>

        <van-floating-bubble  style="z-index: 9999; background-color: #77beff" magnetic="x" axis="xy" :offset="{y:320}">
          <v-icon icon="mdi-navigation-variant" @click="showInfo = false"></v-icon>
        </van-floating-bubble>
        <v-card style="margin-top: 10px; ">
          <v-card-title style="font-size: 1.2em">
            <v-icon icon="mdi-calendar-clock" size="20"></v-icon><span style="margin-right: 10px">工作时间</span>
          </v-card-title>
          <v-card-text>
            周一至周五9:00—16:00
          </v-card-text>
        </v-card>
        <v-card style="margin-top: 10px; ">
          <v-card-title style="font-size: 1.2em">
            <v-icon icon="mdi-phone-in-talk-outline" size="20"></v-icon><span style="margin-right: 10px">联系电话</span>
          </v-card-title>
          <v-card-text>
            13325252525 0571-88888888
          </v-card-text>
        </v-card>
        <v-card style="margin-top: 10px; "
        >
          <v-card-title style="font-size: 1.2em">
            <v-icon icon="mdi-invoice-list-outline" size="20"></v-icon>高频事项
          </v-card-title>
          <v-list lines="one">
            <v-list-item
                v-for="n in 23"
                :key="n"
                :title="'Item ' + n"
                subtitle="Lorem ipsum dolor sit amet consectetur adipisicing elit"
            ></v-list-item>
          </v-list>
        </v-card>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<style>
  .show_small_info {
    width: 100%;
    height: 150px;
    border-radius: 8px;
    background-color: #fff;
  }
  .bottom_info {
    position: absolute;
    z-index: 999;
    bottom: 20px;
    width: 95%;
    left: 2.5%
  }
  /*
动画激活时给予初始状态，这个基础状态用于定义后续过渡动作的起点
这里统一定义了组件创建和组件销毁的两个起点，主要是赋予 transition 使其有一个过渡
*/
  .roll-enter-active,
  .roll-leave-active {
    transition: all .3s;
    position: absolute;
    top: 0;
  }

  /*
  组件销毁，过渡的终点
  最开始的起点是原位，离开的时候滚到上面去
  */
  .roll-leave-to {
    top: -30px;
  }

  /*
  组件创建，过渡的起点
  创建的时候是重下往上进入，所以元素最开始是在下面
  */
  .roll-enter-active {
    top: 30px;
  }

  /*
  过渡的时候回到起点，达到重下往上滚动
  */
  .roll-enter-to {
    top: 0;
  }
  .roll-container {
    position: relative;
    line-height: 30px;
    height: 30px;
    width: 100%;
    overflow: hidden;
    color: #ff4747;
    font-size: 16px;
  }

  .roll-number {
    width: 100%;
  }

</style>
