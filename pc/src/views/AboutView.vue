<script setup>

import {reactive, ref, getCurrentInstance, onMounted, computed} from "vue";

const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const activeMenu = ref(1);
const showView = ref(false)
const images = [
  'https://fastly.jsdelivr.net/npm/@vant/assets/apple-1.jpeg',
  'https://fastly.jsdelivr.net/npm/@vant/assets/apple-2.jpeg',
];
const imageText = [
  "test",
  "test",
  "test",
  "test"
]
const showViewModal = () => {
  showView.value = true;
}
const onLoad = () => {
  // 异步更新数据
  // setTimeout 仅做示例，真实场景中一般为 ajax 请求
  setTimeout(() => {
    for (let i = 0; i < 10; i++) {
      list.value.push(list.value.length + 1);
    }
    console.info(list.value)

    // 加载状态结束
    loading.value = false;

    // 数据全部加载完成
    if (list.value.length >= 40) {
      finished.value = true;
    }
  }, 1000);
};
const swipeIndex = ref(0);
const swipeChange = (index) => {
  swipeIndex.value = index;
}
</script>

<template>
  <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
      style="padding-top: 10px"
  >
    <van-row>
      <van-col span="12" v-for="item in list">
        <div class="card-main" :key="item" @click="showViewModal" >
          <van-image
              width="100%"
              height="100%"
              src="https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg"
              radius="8px 8px 0px 0px"
          />
          <div class="card-title">RF-9834</div>
        </div>
      </van-col>
    </van-row>
  </van-list>
  <van-back-top bottom="55" />

  <van-popup
      v-model:show="showView"
      position="bottom"
      round
      closeable
      :style="{ height: '90%',backgroundColor: '#eff2f5' }"
  >
    <van-nav-bar title="RF-9834" />

    <van-cell-group inset title="" style="height: calc(50% - 90px)">
      <template #title>
        产品图片<div style="float: right">{{swipeIndex + 1}} / {{images.length}}</div>
      </template>
      <van-swipe :autoplay="3000" lazy-render @change="swipeChange">
        <van-swipe-item v-for="image in images" :key="image">
          <van-image :src="image" fit="contain" height="100%" width="100%"/>
        </van-swipe-item>
      </van-swipe>
    </van-cell-group>
    <van-cell-group inset title="产品视频" style="height: calc(50% - 90px)">
      <van-swipe :autoplay="3000" lazy-render>
        <van-swipe-item v-for="image in images" :key="image">
          <van-image :src="image" fit="contain" height="100%" width="100%"/>
        </van-swipe-item>
      </van-swipe>
    </van-cell-group>
  </van-popup>
  <van-tabbar v-model="activeMenu">
    <van-tabbar-item icon="list-switch">被壳</van-tabbar-item>
    <van-tabbar-item icon="list-switching">羽绒</van-tabbar-item>
  </van-tabbar>
</template>
<style>
.van-image-preview__cover {
  width: 100%;
  height: 100px;
}
.card-main {
  background-color: rgb(6 10 61 / 70%);
  border-radius: 8px 8px 5px 5px;
  margin: 10px 5px;
}
.card-title {
  font-weight: bold;
  padding-bottom: 10px;
  width: 100%;
  text-align: center;
  color: white;
}
</style>
