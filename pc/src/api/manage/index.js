import {get, post, upload, del} from '@/request/index.js'
import {urlData} from "@/config/index.js";


class ManageService {
    constructor() {
    }

    //列表
    async upload(formData) {
        return await upload(urlData.manage.upload, formData)
    }

    async saveItem(item) {
        return await post(urlData.manage.item, item)
    }
    async delItem(id) {
        return await del(`${urlData.manage.item}/${id}`)
    }

    async delItemFile(fileId) {
        return await del(urlData.manage.itemFile + "/" + fileId)
    }

    async mainFile(fileId) {
        return await post(urlData.manage.itemFile + "/" + fileId)
    }



}

export default ManageService
