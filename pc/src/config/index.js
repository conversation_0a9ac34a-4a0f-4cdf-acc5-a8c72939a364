// const basePath = process.env.NODE_ENV === 'production' ? 'http://127.0.0.1:8072' : 'http://************:8072';
const apiPath = '/rongvo-api'

const urlData = {
  //接口地址
  apiUrl: apiPath,
  //日志
  weixin: {
    value: apiPath + '/weixin',
    item: apiPath + '/weixin/item',
  },
  login: {
    value: apiPath + '/login'
  },
  manage: {
    upload: apiPath + '/upload',
    item: apiPath + '/item',
    itemFile: apiPath + '/item-file',
  },
  key: {
    cookie: {
      token: 'access_token',
      refreshToken: 'refresh_token',
      expires_in: 'expires_in',
      nickname: 'nickname',
      username: 'username',
    },
  },
}
export { urlData }
