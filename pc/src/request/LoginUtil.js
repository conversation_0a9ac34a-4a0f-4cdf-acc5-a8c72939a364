import Cookies from 'js-cookie';
import {urlData} from "@/config";
import router from "@/router";
import {ssoService} from '@/api';
import { showFailToast } from 'vant';

const LoginUtil = {
    refreshTimeout: null,
    /**
     * 登录
     */
    login(params, callback, failCallback) {
        ssoService.login(params.username, params.password).then(response => {
            if (response?.data?.access_token) {
                let rel = response.data
                this.refreshToken(rel)
                router.push({name: "manage"})
            } else {
                showFailToast({message: '账号或者密码不正确！', duration: 5 * 1000})
            }
        }).catch(err => {
            if (failCallback) {
                failCallback(err)
            }
        })
    },
    /**
     * 刷新token
     */
    refreshToken(token) {
        this.setToken(token.access_token)
        // Cookies.set(urlData.key.cookie.refreshToken, token.refresh_token)
        Cookies.set(urlData.key.cookie.expires_in, token.expires_in)
        this.setUsername(token.username);
        this.setNickname(decodeURI(token.nickname) || "");
        this.setUserno(token.username)
        // this.setRole(token.role)
        // this._clearRefreshTimeout();
        // this.refreshTimeout = setTimeout(() => {
        //     this._executeRefreshToken(token.refresh_token);
        // }, (token.expires_in - 100) * 1000);
    },
    /**
    /**
     * 清除定时器-执行刷新token
     */
    _clearRefreshTimeout() {
        if (this.refreshTimeout) {
            clearTimeout(this.refreshTimeout)
            this.refreshTimeout = null
        }
    },
    /**
     * 执行刷新token
     */
    async _executeRefreshToken(refresh_token) {
        let rel = await ssoService.refreshToken(refresh_token);
        if (rel.success) {
            this.refreshToken(rel.data);
        }
    },
    /**
     * 退出登录
     */
    logout() {
        this._clearRefreshTimeout();
        this.setToken("");
        this.setUsername("");
        this.setNickname("");
        this.setUserno("")
        this.setRole("")
    },
    /**
     * 设置token
     */
    setToken(token) {
        Cookies.set(urlData.key.cookie.token, token);
    },
    /**
     * 获取token
     */
    getToken() {
        return Cookies.get(urlData.key.cookie.token);
    },
    /**
     * 设置角色
     */
    setRole(role) {
        Cookies.set(urlData.key.cookie.role, JSON.stringify(role));
    },
    /**
     * 获取角色
     */
    getRole() {
        return Cookies.get(urlData.key.cookie.role);
    },
    /**
     * 设置用户名
     */
    setUsername(username) {
        Cookies.set(urlData.key.cookie.username, username);
    },
    /**
     * 获取用户名
     */
    getUsername() {
        return Cookies.get(urlData.key.cookie.username);
    },
    /**
     * 设置昵称
     */
    setNickname(nickname) {
        Cookies.set(urlData.key.cookie.nickname, nickname);
    },
    /**
     * 获取昵称
     */
    getNickname() {
        return Cookies.get(urlData.key.cookie.nickname);
    },
    /**
     * 设置用户ID
     */
    setUserno(userno) {
        Cookies.set(urlData.key.cookie.userno, userno);
    },
    /**
     * 获取用户ID
     */
    getUserno() {
        return Cookies.get(urlData.key.cookie.userno);
    },

}

export default LoginUtil
