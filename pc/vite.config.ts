import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: "0.0.0.0",
    proxy: {
      // Using the proxy instance
      '/rongvo-api': {
        target: 'http://127.0.0.1:9801',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/rongvo-api/, ''),
        configure: (proxy, options) => {

          // proxy will be an instance of 'http-proxy'
        },
      },
      '/item-image': {
        target: 'http://127.0.0.1:9001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\//, '/'),
        configure: (proxy, options) => {

          // proxy will be an instance of 'http-proxy'
        },
      },
    }
  }
})
