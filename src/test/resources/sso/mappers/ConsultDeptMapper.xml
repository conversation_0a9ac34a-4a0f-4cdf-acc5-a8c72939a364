<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.ConsultDeptMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.ConsultDept">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="consult_id" jdbcType="INTEGER" property="consultId" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="min_count" jdbcType="INTEGER" property="minCount" />
    <result column="max_count" jdbcType="INTEGER" property="maxCount" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="answer_count" jdbcType="INTEGER" property="answerCount" />
  </resultMap>
</mapper>