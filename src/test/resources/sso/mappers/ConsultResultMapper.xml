<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.ConsultResultMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.ConsultResult">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="head_id" jdbcType="INTEGER" property="headId" />
    <result column="consult_id" jdbcType="INTEGER" property="consultId" />
    <result column="consult_line_id" jdbcType="INTEGER" property="consultLineId" />
    <result column="result_value" jdbcType="VARCHAR" property="resultValue" />
    <result column="result_msg" jdbcType="VARCHAR" property="resultMsg" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
  </resultMap>
</mapper>