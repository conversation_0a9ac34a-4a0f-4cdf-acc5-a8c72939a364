<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.ConsultPeriodMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.ConsultPeriod">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="consult_id" jdbcType="INTEGER" property="consultId" />
    <result column="begin_time" jdbcType="CHAR" property="beginTime" />
    <result column="end_time" jdbcType="CHAR" property="endTime" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
  </resultMap>
</mapper>