<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.ConsultHeadMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.ConsultHead">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="vote_flag" jdbcType="CHAR" property="voteFlag" />
    <result column="msg_flag" jdbcType="CHAR" property="msgFlag" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="period" jdbcType="CHAR" property="period" />
    <result column="dept_count" jdbcType="INTEGER" property="deptCount" />
    <result column="avg_op" jdbcType="CHAR" property="avgOp" />
    <result column="avg_count" jdbcType="INTEGER" property="avgCount" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="deleted" jdbcType="CHAR" property="deleted" />
    <result column="published" jdbcType="CHAR" property="published" />
  </resultMap>
</mapper>