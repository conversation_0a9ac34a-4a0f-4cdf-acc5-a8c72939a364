<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.ConsultLineMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.ConsultLine">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="head_id" jdbcType="INTEGER" property="headId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="type" jdbcType="CHAR" property="type" />
    <result column="star_count" jdbcType="INTEGER" property="starCount" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="required" jdbcType="CHAR" property="required" />
    <result column="sort_num" jdbcType="INTEGER" property="sortNum" />
  </resultMap>
</mapper>