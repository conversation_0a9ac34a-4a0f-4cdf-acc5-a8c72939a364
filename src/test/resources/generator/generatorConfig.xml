<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ The MIT License (MIT)
  ~
  ~ Copyright (c) 2014 <EMAIL>
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in
  ~ all copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
  ~ THE SOFTWARE.
  -->

<!DOCTYPE generatorConfiguration
		PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
		"http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
<!--	<properties resource="generator/config.properties"/>-->

	<context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
		<property name="beginningDelimiter" value="`"/>
		<property name="endingDelimiter" value="`"/>

		<plugin type="tk.mybatis.mapper.generator.MapperPlugin">
			<property name="mappers" value="tk.mybatis.mapper.common.Mapper"/>
			<property name="caseSensitive" value="true"/>
		</plugin>

		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
						connectionURL="***********************************"
						userId="root"
						password="root">
		</jdbcConnection>

		<javaModelGenerator targetPackage="cn.ray.sso.dao.entity" targetProject="src/test/java"/>

		<sqlMapGenerator targetPackage="sso.mappers" targetProject="src/test/resources"/>

		<javaClientGenerator targetPackage="cn.ray.sso.dao.mapper" targetProject="src/test/java" type="XMLMAPPER"/>

		<table tableName="consult_period" domainObjectName="ConsultPeriod">
			<generatedKey column="id" sqlStatement="JDBC"/>
		</table>

	</context>
</generatorConfiguration>
