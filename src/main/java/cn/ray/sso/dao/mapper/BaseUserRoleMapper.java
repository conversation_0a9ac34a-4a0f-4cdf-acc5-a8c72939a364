package cn.ray.sso.dao.mapper;

import cn.ray.sso.dao.entity.BaseUserRole;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

public interface BaseUserRoleMapper extends Mapper<BaseUserRole> {

    @Insert("<script>" +
            "insert into base_user_role( user_id, role_id) values\n" +
            "    <foreach collection=\"list\" item=\"userId\" index=\"index\" separator=\",\">\n" +
            "      (#{userId},#{roleId})\n" +
            "    </foreach>" +
            "</script>")
    void insertListByRoleId(@Param("roleId") Long roleId, @Param("list") Integer[] userIds);

    @Insert("<script>" +
            "insert into base_user_role( user_id, role_id) values\n" +
            "    <foreach collection=\"list\" item=\"roleId\" index=\"index\" separator=\",\">\n" +
            "      (#{userId},#{roleId})\n" +
            "    </foreach>" +
            "</script>")
    void insertListByUserId(@Param("userId") Long userId, @Param("list") Integer[] roleIds);
}
