package cn.ray.sso.dao.mapper;

import cn.ray.sso.dao.entity.BizDept;
import tk.mybatis.mapper.additional.idlist.DeleteByIdListMapper;
import tk.mybatis.mapper.additional.idlist.SelectByIdListMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface BizDeptMapper extends Mapper<BizDept>, DeleteByIdListMapper<BizDept, Long>, InsertListMapper<BizDept>, SelectByIdListMapper<BizDept, Long> {
}
