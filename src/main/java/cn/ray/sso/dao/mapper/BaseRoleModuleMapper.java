package cn.ray.sso.dao.mapper;

import cn.ray.sso.dao.entity.BaseRoleModule;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface BaseRoleModuleMapper extends Mapper<BaseRoleModule> {

    /**
     * 批量插入
     * @param list
     */
    @Insert("<script>" +
            "    INSERT INTO base_role_module (role_id,module_id)\n" +
            "    VALUES\n" +
            "    <foreach collection=\"items\" item=\"item\" index=\"index\" separator=\",\">\n" +
            "      (#{item.roleId},#{item.moduleId})\n" +
            "    </foreach>" +
            "</script>")
    void batchAddRoleModule(@Param("items") List<BaseRoleModule> list);
}
