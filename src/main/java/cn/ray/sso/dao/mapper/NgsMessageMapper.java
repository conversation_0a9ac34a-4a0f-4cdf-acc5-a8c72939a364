package cn.ray.sso.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import cn.ray.sso.dao.entity.NgsMessage;

public interface NgsMessageMapper extends Mapper<NgsMessage> {

    @Select("select id, title,create_date createDate from ngs_message " +
            "where deleted = 0 and state = 1 " +
            "order by IF(main_expire_date >= '${date}', main_expire_date, null) desc, create_date desc limit 10")
    List<NgsMessage> getMainMessageList(@Param("date") String date);
}
