package cn.ray.sso.dao.mapper;

import java.util.List;

import cn.ray.sso.dao.DeptUserCount;
import cn.ray.sso.dao.entity.BizUserInfo;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.additional.idlist.DeleteByIdListMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface BizUserInfoMapper extends Mapper<BizUserInfo>, DeleteByIdListMapper<BizUserInfo, Long>, InsertListMapper<BizUserInfo> {

    @Select("select dept_id deptId, count(1) userCount from biz_user_info" +
            "    group by dept_id")
    List<DeptUserCount> getDeptUserCount();
}
