package cn.ray.sso.dao.entity;

import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Table(name = "base_group")
@Data
public class BaseGroup {
    @GeneratedValue(generator = "JDBC")
    @Id
    private Long id;

    @Column(name = "group_name")
    private String groupName;

    @Column(name = "group_code")
    private String groupCode;

    @Column(name = "group_desc")
    private String groupDesc;

    private Long parentId;

    private String state;

    private Boolean deleted;

    private String creator;

    @Column(name = "create_date")
    private Date createDate;

    private String updator;

    @Column(name = "update_date")
    private Date updateDate;

}
