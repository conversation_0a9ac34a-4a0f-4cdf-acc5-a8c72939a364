package cn.ray.sso.dao.entity;

import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Table(name = "base_module")
@Data
public class BaseModule {
    @Column(name = "ID")
    @GeneratedValue(generator = "JDBC")
    @Id
    private Long id;

    /**
     * 菜单名称
     */
    @Column(name = "MENU_NAME")
    private String menuName;

    /**
     * 地址
     */
    private String menuUrl;

    /**
     * 父菜单ID
     */
    @Column(name = "PARENT_ID")
    private Long parentId;

    /**
     * 排序
     */
    @Column(name = "ORDER_NUM")
    private Integer orderNum;

    /**
     * 状态
     */
    @Column(name = "STATE")
    private Integer state;

    @Column(name = "DELETED")
    private Boolean deleted;

    @Column(name = "CREATOR")
    private String creator;

    @Column(name = "CREATE_DATE")
    private Date createDate;

    @Column(name = "UPDATOR")
    private String updator;

    @Column(name = "UPDATE_DATE")
    private Date updateDate;

    /**
     * 类型 0为目录，1为菜单，2为按钮，3为内部资源
     */
    @Column(name = "MENU_TYPE")
    private String menuType;

    /**
     * 菜单编号
     */
    @Column(name = "MENU_NO")
    private Integer menuNo;

    @Column(name = "HAS_LOCK")
    private String hasLock;

    @Column(name = "ICON")
    private String icon;

    /**
     * 允许的请求方式，分别为:GET、POST、PUT、DEPTE；根据代码情况配置
     */
    @Column(name = "METHOD")
    private String method;


}
