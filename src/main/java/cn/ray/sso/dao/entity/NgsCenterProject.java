package cn.ray.sso.dao.entity;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Table(name = "ngs_center_project")
@Data
public class NgsCenterProject {


    @GeneratedValue(generator = "JDBC")
    @Id
    private Long id;

    private Long centerId;

    private String projectName;

    private String projectDesc;

    private Integer sortNum;

    private String onlineUrl;

}
