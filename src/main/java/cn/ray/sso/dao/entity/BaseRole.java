package cn.ray.sso.dao.entity;

import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Table(name = "base_role")
@Data
public class BaseRole {
    @GeneratedValue(generator = "JDBC")
    @Id
    private Long id;

    /**
     * 角色名称
     */
    @Column(name = "role_name")
    private String roleName;

    /**
     * 状态
     */
    private Integer state;

    private Boolean deleted;

    private String creator;

    @Column(name = "create_date")
    private Date createDate;

    private String updator;

    @Column(name = "update_date")
    private Date updateDate;
}
