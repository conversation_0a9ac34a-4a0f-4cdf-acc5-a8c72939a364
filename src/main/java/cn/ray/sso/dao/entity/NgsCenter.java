package cn.ray.sso.dao.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

import lombok.Data;

@Table(name = "ngs_center")
@Data
public class NgsCenter {
    @GeneratedValue(generator = "JDBC")
    @Id
    private Long id;

    private String centerName;

    private String centerType;

    private Long townId;

    private String layout;

    private String addr;

    private String latlng;

    private String workTime;
    private String tel;
    private String img;

    private Long state;
    /**
     * 删除状态1：已删除；0：未删除
     */
    private String deleted;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    private String createDate;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    private String updateDate;

    /**
     * 更新人
     */
    private String updator;

    @Transient
    private List<NgsCenterUser> adminList;

    @Transient
    private String usernos;

    @Transient
    private String distance;

    @Transient
    private String townName;

}
