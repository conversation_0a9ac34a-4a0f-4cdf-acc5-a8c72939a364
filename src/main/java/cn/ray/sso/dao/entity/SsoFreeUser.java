package cn.ray.sso.dao.entity;

import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Table(name = "sso_free_user")
@Data
public class SsoFreeUser {
    @GeneratedValue(generator = "JDBC")
    @Id
    private Long id;

    /**
     * 登录名
     */
    @Column(name = "user_no")
    private String userNo;
    /**
     * 昵称
     */
    @Column(name = "nick_name")
    private String nickName;

    /**
     * 来源说明
     */
    @Column(name = "from_type")
    private String fromType;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
}
