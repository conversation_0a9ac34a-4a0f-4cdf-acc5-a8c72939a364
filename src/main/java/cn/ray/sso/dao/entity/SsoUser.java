package cn.ray.sso.dao.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "sso_user")
@Data
public class SsoUser {
    @GeneratedValue(generator = "JDBC")
    @Id
    private Long id;

    /**
     * 昵称
     */
    @Column(name = "nick_name")
    private String nickName;

    /**
     * 登录名
     */
    @Column(name = "user_no")
    private String userNo;

    /**
     * 加密后的登录密码
     */
    private String passwd;

    /**
     * 状态1：有效，0：无效
     */
    private String state;

    /**
     * 删除状态1：已删除；0：未删除
     */
    private String deleted;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    private Date createDate;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    private Date updateDate;

    /**
     * 更新人
     */
    private String updator;
    /**
     * 所有分组
     */
    private Long groupId;

}
