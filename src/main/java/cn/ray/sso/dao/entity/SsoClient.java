package cn.ray.sso.dao.entity;

import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Table(name = "sso_client")
@Data
public class SsoClient {
    @GeneratedValue(generator = "JDBC")
    @Id
    private Long id;

    /**
     * appid
     */
    private String appid;

    /**
     * 前端名称
     */
    @Column(name = "client_name")
    private String clientName;

    /**
     * 密钥
     */
    private String secret;

    @Column(name = "update_date")
    private Date updateDate;

    private String updator;

    @Column(name = "create_date")
    private Date createDate;

    private String state;

    private String deleted;

    private String creator;

    /**
     * 授权模式：授权码（认证码）模式:authorization_code,用户名密码模式:password,刷新token:refresh_token,客户端模式:client_credentials, 简化（隐形）模式 :Impilict
     */
    @Column(name = "authorized_grant_types")
    private String authorizedGrantTypes;

    /**
     * 作用域
     */
    private String scope;


}
