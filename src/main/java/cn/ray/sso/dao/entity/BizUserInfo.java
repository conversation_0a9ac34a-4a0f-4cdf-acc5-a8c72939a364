package cn.ray.sso.dao.entity;

import javax.persistence.*;

import lombok.Data;

@Table(name = "biz_user_info")
@Data
public class BizUserInfo {


    @GeneratedValue(generator = "JDBC")
    @Id
    private Long id;

    /**
     * 部门id
     */
    @Column(name = "dept_id")
    private Long deptId;

    /**
     * 身份证号
     */
    @Column(name = "id_card")
    private String idCard;

    /**
     * 姓名
     */
    private String username;


    /**
     * 微信Openid
     */
    @Column(name = "open_id")
    private String openId;

    /**
     * 用户类型1：内部人员；2：外部人员
     */
    private String userType;

    /**
     * 表自增id
     */
    private Long outId;

}
