package cn.ray.sso.dao.entity;

import javax.persistence.*;
import java.util.Date;

import lombok.Data;

@Table(name = "ngs_message")
@Data
public class NgsMessage {
    @GeneratedValue(generator = "JDBC")
    @Id
    private Long id;

    private String title;

    private String filePath;

    /**
     *
     */
    private String mainExpireDate;

    private String content;


    private Integer state;
    /**
     * 删除状态1：已删除；0：未删除
     */
    private String deleted;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    private String createDate;

    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建人
     */
    @Transient
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    private String updateDate;

    /**
     * 更新人
     */
    private String updator;


}
