package cn.ray.sso.manager;

import javax.annotation.Resource;

import cn.ray.sso.dao.entity.SsoClient;
import cn.ray.sso.dao.mapper.SsoClientMapper;
import cn.ray.sso.dto.SsoClientDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.HashSet;
import java.util.Set;

@Component
public class SysSsoManager {

    @Resource
    private SsoClientMapper ssoClientMapper;

    public ClientDetails getClientByClientId(String clientId) {
        Example example = Example.builder(SsoClient.class)
                .andWhere(
                        WeekendSqls.<SsoClient>custom().andEqualTo(SsoClient::getAppid, clientId)
                        .andEqualTo(SsoClient::getDeleted, false)
                        .andEqualTo(SsoClient::getState, 1)
                ).build();
        SsoClient ssoClient = ssoClientMapper.selectOneByExample(example);
        return transfar(ssoClient);
    }

    private ClientDetails transfar(SsoClient ssoClient) {
        if (ssoClient == null) {
            return null;
        }
        SsoClientDto sso = new SsoClientDto();
        sso.setClientId(ssoClient.getAppid());
        sso.setId(ssoClient.getId());
        sso.setClientSecret(ssoClient.getSecret());
        Set<String> grantTypes = new HashSet<>();
        String[] grantTypeStr = ssoClient.getAuthorizedGrantTypes().split(",");
        for (String grantType : grantTypeStr) {
            grantTypes.add(grantType);
        }
        sso.setAuthorizedGrantTypes(grantTypes);

        Set<String> scopes = new HashSet<>();
        String[] scopeStr = ssoClient.getScope().split(",");
        for (String scope : scopeStr) {
            scopes.add(scope);
        }
        sso.setScope(scopes);
        sso.setAuthorities(new HashSet<>());

        return sso;
    }
}
