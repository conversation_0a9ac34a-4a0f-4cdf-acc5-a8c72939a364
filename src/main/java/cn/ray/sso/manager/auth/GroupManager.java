package cn.ray.sso.manager.auth;

import javax.annotation.Resource;

import cn.ray.core.common.TreeHelper;
import cn.ray.sso.dao.entity.BaseGroup;
import cn.ray.sso.dao.entity.BaseModule;
import cn.ray.sso.dao.mapper.BaseGroupMapper;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class GroupManager {

    @Resource
    private BaseGroupMapper baseGroupMapper;

    public TreeHelper getGroupTree(Long parentId) {

        List<BaseGroup> moduleList = baseGroupMapper.selectAll();

        TreeHelper tree = new TreeHelper(new String[]{"id", "groupName", "groupCode", "sortNum", "parentId"},moduleList,4) {
            @Override
            public Object root() {
                return parentId;
            }

            @Override
            public Object[] idTextData(Object[] objects) {
                return objects;
            }

            @Override
            protected void setJsonAttributesData(JSONObject jo, String[] keys, Object[] nodeData) {
                super.setJsonAttributesData(jo, keys, nodeData);
                jo.put("title",jo.get("name"));
                jo.put("deptName",jo.get("groupName"));
                jo.put("label",jo.get("groupName"));
                jo.put("deptNo",jo.get("groupCode"));
                jo.put("expand" , true);
            }
        };

        return tree;
    }
}
