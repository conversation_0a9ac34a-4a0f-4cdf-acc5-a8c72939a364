package cn.ray.sso.manager.auth;

import javax.annotation.Resource;

import cn.ray.core.common.exception.RuleException;
import cn.ray.core.jdbc.enumeration.DataSourceErrors;
import cn.ray.sso.dao.entity.BaseGroup;
import cn.ray.sso.dao.mapper.BaseGroupMapper;
import cn.ray.sso.enumeration.ValidateErrors;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.List;

/**
 * 部门业务实现层
 *
 * <AUTHOR>
 * @create 2018-03-2
 */
@Component
public class DeptManager {

    @Resource
    private BaseGroupMapper baseGroupMapper;



    public List<BaseGroup> doGetDeptTreeByParentId(Long parentId) {
        return baseGroupMapper.selectByExample(Example.builder(BaseGroup.class)
        .andWhere(WeekendSqls.<BaseGroup>custom().andEqualTo(BaseGroup::getDeleted, false)
        .andEqualTo(BaseGroup::getParentId, parentId)).build());
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveDept(BaseGroup dept) {
        if (StringUtils.isBlank(dept.getGroupCode())) {
            throw new RuleException(ValidateErrors.INVALID_PARAMETERS);
        }
        int old= baseGroupMapper.selectCountByExample(Example.builder(BaseGroup.class)
                .andWhere(WeekendSqls.<BaseGroup>custom().andEqualTo(BaseGroup::getGroupCode, dept.getGroupCode())
						.andEqualTo(BaseGroup::getDeleted,"0")
                .andNotEqualTo(BaseGroup::getId, dept.getId() == null ? -1 : dept.getId()))
        .build());
        if (old>0){
            throw new RuleException(DataSourceErrors.DUPLICATION_OF_DATA);
        }
        if (dept.getId() == null){
            dept.setCreateDate(dept.getUpdateDate());
            dept.setCreator(dept.getUpdator());
            insertDept(dept);
        }else{
            updateDept(dept);
        }
    }


    public void insertDept(BaseGroup dept) {
        baseGroupMapper.insertSelective(dept);
    }


    public void updateDept(BaseGroup dept) {
        baseGroupMapper.updateByPrimaryKeySelective(dept);
    }


    public BaseGroup getDeptById(Long id) {
        return baseGroupMapper.selectByPrimaryKey(id);
    }



    public void deleteDept(Long id) {
        baseGroupMapper.deleteByPrimaryKey(id);
    }

}
