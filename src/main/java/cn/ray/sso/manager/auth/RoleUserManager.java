package cn.ray.sso.manager.auth;

import javax.annotation.Resource;

import cn.ray.sso.dao.entity.BaseRole;
import cn.ray.sso.dao.entity.BaseUserRole;
import cn.ray.sso.dao.mapper.BaseUserRoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.List;

/**
 * @Author: wyz
 * @Date: 2018/3/12 10:21
 * @Description:
 */

@Component
public class RoleUserManager {
    @Resource
    private BaseUserRoleMapper baseUserRoleMapper;


    @Transactional(rollbackFor = Exception.class)
    public void saveByList(Long roleId, Integer[] list,Long userId) {
        //TODO 将角色人员对应关系存入到角色表中的RoleId中
        Example example = getBaseUserRoleExample(roleId, userId);

        List<BaseUserRole> baseUserRoles = baseUserRoleMapper.selectByExample(example);
        //该角色是否有分配人员
        if (null != baseUserRoles && baseUserRoles.size() > 0) {
            baseUserRoleMapper.deleteByExample(example);
        }
        if (list.length > 0) {
            if (null != roleId) {
                baseUserRoleMapper.insertListByRoleId(roleId, list);
            }
            if (null != userId){
                baseUserRoleMapper.insertListByUserId(userId, list);
            }
        }
    }

    private Example getBaseUserRoleExample(Long roleId, Long userId) {
        WeekendSqls<BaseUserRole> weekendSqls = WeekendSqls.custom();
        BaseUserRole roleUser = new BaseUserRole();
        if (null != roleId){
            roleUser.setRoleId(roleId);
            weekendSqls.andEqualTo(BaseUserRole::getRoleId, roleId);
        }
        if (null != userId){
            roleUser.setUserId(userId);
            weekendSqls.andEqualTo(BaseUserRole::getUserId, userId);
        }
        return Example.builder(BaseUserRole.class).andWhere(weekendSqls).build();
    }


    public List<BaseUserRole> selectRoleUserByEntity(BaseUserRole roleUser) {
        return baseUserRoleMapper.selectByExample(getBaseUserRoleExample(roleUser.getRoleId(), roleUser.getUserId()));
    }

}
