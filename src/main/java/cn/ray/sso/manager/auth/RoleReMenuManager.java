package cn.ray.sso.manager.auth;

import javax.annotation.Resource;

import cn.ray.sso.dao.entity.BaseRoleModule;
import cn.ray.sso.dao.mapper.BaseRoleModuleMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.ArrayList;
import java.util.List;

/**
 * 角色菜单业务实现层
 *
 * <AUTHOR>
 * @create 2018-03-2
 */

@Component
public class RoleReMenuManager {

    @Resource
    private BaseRoleModuleMapper baseRoleModuleMapper;


    public List<BaseRoleModule> selectMenuIdByRole(Long roleId) {
        return baseRoleModuleMapper.selectByExample(Example.builder(BaseRoleModule.class)
        .andWhere(WeekendSqls.<BaseRoleModule>custom()
        .andEqualTo(BaseRoleModule::getRoleId, roleId)).build());
    }


    @Transactional(rollbackFor = Exception.class)
    public void addRoleReMenu(Long roleId, String[] menuId) {
        // 先删除  再添加
        baseRoleModuleMapper.deleteByExample(Example.builder(BaseRoleModule.class)
        .andWhere(WeekendSqls.<BaseRoleModule>custom().andEqualTo(BaseRoleModule::getRoleId, roleId)).build());

        if (menuId != null && menuId.length > 0) {
            List<BaseRoleModule> list = new ArrayList();
            for (String mid:menuId) {
                if (StringUtils.isBlank(mid)) {
                    continue;
                }
                BaseRoleModule brm = new BaseRoleModule();
                brm.setRoleId(roleId);
                brm.setModuleId(Long.valueOf(mid));
                list.add(brm);
            }
            if (!list.isEmpty()) {
                baseRoleModuleMapper.batchAddRoleModule(list);
            }
        }
    }
}
