package cn.ray.sso.manager.auth;

import javax.annotation.Resource;

import cn.ray.core.common.DateUtil;
import cn.ray.core.common.exception.RuleException;
import cn.ray.core.jdbc.enumeration.DataSourceErrors;
import cn.ray.sso.dao.entity.BaseModule;
import cn.ray.sso.dao.entity.BaseRoleModule;
import cn.ray.sso.dao.entity.BaseUserRole;
import cn.ray.sso.dao.entity.SsoUser;
import cn.ray.sso.dao.entity.BaseGroup;
import cn.ray.sso.dao.mapper.*;
import cn.ray.sso.dubbo.dto.auth.BaseModuleDto;
import cn.ray.sso.dubbo.dto.auth.SsoUserDto;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@RefreshScope
public class SsoUserManager {

    @Resource
    private SsoUserMapper ssoUserMapper;

    @Resource
    private BaseUserRoleMapper baseUserRoleMapper;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private BaseModuleMapper baseModuleMapper;

    @Resource
    private BaseRoleModuleMapper baseRoleModuleMapper;

    @Resource
    private BaseGroupMapper baseGroupMapper;

    @Value("${ray.security.pwd-model}")
    private String pwdModel;

    @Value("${ray.security.root}")
    private String rootUser;

    public PageInfo<SsoUserDto> getUserPage(Integer pageIndex, Integer pageSize, SsoUser ssoUser) {

        Page page = PageHelper.startPage(pageIndex, pageSize);
        ssoUser.setDeleted("0");
        WeekendSqls<SsoUser> weekendSqls = WeekendSqls.custom();
        weekendSqls.andEqualTo(SsoUser::getDeleted, "0");
        if (StringUtils.isNotBlank(ssoUser.getUserNo())) {
            weekendSqls.andLike(SsoUser::getUserNo, "%" + ssoUser.getUserNo() + "%");
        }
        if (StringUtils.isNotBlank(ssoUser.getNickName())) {
            weekendSqls.andLike(SsoUser::getNickName, "%" + ssoUser.getNickName() + "%");
        }
        List<SsoUser> list = ssoUserMapper.selectByExample(Example.builder(SsoUser.class).andWhere(weekendSqls).orderByDesc("id").build());
        List<SsoUserDto> returnList = new ArrayList<>();
//        list.forEach(ssoUser1 -> userIds.add(ssoUser1.getId()));
        if (!list.isEmpty()){
            List<Long> userIds = list.stream().map(SsoUser::getId).collect(Collectors.toList());
            List<BaseUserRole> baseUserRoles = baseUserRoleMapper.selectByExample(Example.builder(BaseUserRole.class).andWhere(
                    WeekendSqls.<BaseUserRole>custom().andIn(BaseUserRole::getUserId, userIds)
            ).build());
            Map<Long, JSONArray> userRoleMap = new HashMap<>();
            baseUserRoles.forEach(baseUserRole -> {
                JSONArray ja = userRoleMap.get(baseUserRole.getUserId());
                if (ja == null) {
                    ja = new JSONArray();
                }
                ja.add(baseUserRole.getRoleId());
                userRoleMap.put(baseUserRole.getUserId(), ja);
            });
            list.forEach(ssoUser1 -> {
                SsoUserDto ssoUserDto = transfar(ssoUser1);
                JSONArray ja = userRoleMap.get(ssoUserDto.getId());
                if (ja != null) {
                    ssoUserDto.setRoleIds(ja.toJSONString());
                }
                returnList.add(ssoUserDto);
            });
        }
        PageInfo  pageInfo = new PageInfo<>(page);
        pageInfo.setList(returnList);
        return pageInfo;
    }

    private SsoUserDto transfar(SsoUser ssoUser) {
        if (ssoUser == null) {
            return null;
        }
        SsoUserDto ssoUserDto = new SsoUserDto();
        ssoUserDto.setNickName(ssoUser.getNickName());
//        ssoUserDto.setPasswd(ssoUser.getPasswd());
        ssoUserDto.setUserNo(ssoUser.getUserNo());
        ssoUserDto.setId(ssoUser.getId());
        ssoUserDto.setState(ssoUser.getState());
        ssoUserDto.setDeleted(ssoUser.getDeleted());
        ssoUserDto.setCreateDate(ssoUser.getCreateDate());
        ssoUserDto.setCreator(ssoUser.getCreator());
        ssoUserDto.setGroupId(ssoUser.getGroupId());
        ssoUserDto.setUpdateDate(ssoUser.getUpdateDate());
        ssoUserDto.setUpdator(ssoUser.getUpdator());
        return ssoUserDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveUser(SsoUserDto ssoUserDto) {
        SsoUser ssoUser = transfar(ssoUserDto);

        //add wyy 2020年8月3日15:56:12 增加登录名重复验证
		int countAlready= ssoUserMapper.selectCountByExample(Example.builder(SsoUser.class)
				.andWhere(WeekendSqls.<SsoUser>custom().andEqualTo(SsoUser::getUserNo, ssoUser.getUserNo())
						.andEqualTo(SsoUser::getDeleted,"0")
						.andNotEqualTo(SsoUser::getId, ssoUser.getId() == null ? -1 : ssoUser.getId()))
				.build());
		if(countAlready>0){
			throw new RuleException(DataSourceErrors.DUPLICATION_OF_DATA);
		}
        if (ssoUser.getId() == null || ssoUser.getId() <=0) {

            ssoUser.setCreateDate(ssoUser.getUpdateDate());
            ssoUser.setCreator(ssoUser.getUpdator());
            ssoUser.setPasswd(getDefaultPwd(ssoUser.getUserNo()));
            ssoUserMapper.insertSelective(ssoUser);
            ssoUserDto.setId(ssoUser.getId());
        }else {
            ssoUserMapper.updateByPrimaryKeySelective(ssoUser);
            baseUserRoleMapper.deleteByExample(Example.builder(BaseUserRole.class).andWhere(WeekendSqls.<BaseUserRole>custom()
                    .andEqualTo(BaseUserRole::getUserId, ssoUser.getId())).build());
        }
        if (StringUtils.isNotBlank(ssoUserDto.getRoleIds())) {
            Integer[] roleIds = JSONObject.parseArray("[" + ssoUserDto.getRoleIds() + "]").toArray(new Integer[0]);
            if (roleIds.length > 0) {
                baseUserRoleMapper.insertListByUserId(ssoUser.getId(), roleIds);
            }
        }
    }
    private SsoUser transfar(SsoUserDto ssoUserDto) {
        SsoUser ssoUser = new SsoUser();
        ssoUser.setNickName(ssoUserDto.getNickName());
//        ssoUser.setPasswd(ssoUserDto.getPasswd());
        ssoUser.setUserNo(ssoUserDto.getUserNo());
        ssoUser.setId(ssoUserDto.getId());
        ssoUser.setState(ssoUserDto.getState());
        ssoUser.setDeleted(ssoUserDto.getDeleted());
        ssoUser.setCreateDate(ssoUserDto.getCreateDate());
        ssoUser.setCreator(ssoUserDto.getCreator());
        ssoUser.setGroupId(ssoUserDto.getGroupId());
        ssoUser.setUpdateDate(ssoUserDto.getUpdateDate());
        ssoUser.setUpdator(ssoUserDto.getUpdator());
        return ssoUser;
    }

    @Transactional(rollbackFor = Exception.class)
    public void delUser(String operatorUser, List<Long> ids) {
        SsoUser ssoUser = new SsoUser();
        ssoUser.setDeleted("1");
        ssoUser.setUpdator(operatorUser);
        ssoUser.setUpdateDate(DateUtil.now());
        ssoUserMapper.updateByExampleSelective(ssoUser, Example.builder(SsoUser.class).andWhere(WeekendSqls.<SsoUser>custom()
        .andIn(SsoUser::getId, ids)).build());
        baseUserRoleMapper.deleteByExample(Example.builder(BaseUserRole.class).andWhere(WeekendSqls.<BaseUserRole>custom()
                .andIn(BaseUserRole::getUserId, ids)).build());
    }

    public void resetPwd(String operatorUser, List<Long> ids) {
        SsoUser ssoUser = new SsoUser();
        ssoUser.setUpdator(operatorUser);
        ssoUser.setUpdateDate(DateUtil.now());
        List<SsoUser> list = ssoUserMapper.selectByExample(Example.builder(SsoUser.class).andWhere(WeekendSqls.<SsoUser>custom()
                .andIn(SsoUser::getId, ids)).build());
        list.forEach(ssoUser1 -> {
            ssoUser1.setUpdateDate(ssoUser.getUpdateDate());
            ssoUser1.setUpdator(ssoUser.getUpdator());
            ssoUser1.setPasswd(getDefaultPwd(ssoUser1.getUserNo()));
            ssoUserMapper.updateByPrimaryKeySelective(ssoUser1);
        });
    }

    private String getDefaultPwd(String userNo) {
        String pwdmodel = "right:6";
        if (StringUtils.isNotBlank(pwdmodel)) {
            String[] pwdmodels = this.pwdModel.split(":");
            if (pwdmodels.length == 2) {
                pwdmodel= this.pwdModel;
            }
        }
        String[] modelInfo = pwdmodel.split(":");
        String model = modelInfo[0];
        String info = modelInfo[1];
        String pwd = "";
        try{
            switch (model) {
                case "right":
                    pwd = StringUtils.right("000000" + userNo, Integer.valueOf(info));
                    break;
                case "left":
                    pwd = StringUtils.left(userNo + "000000", Integer.valueOf(info));
                    break;
                case "fixed":
                    pwd = info;
                    break;
                default:
                    pwd = StringUtils.right("000000" + userNo,6);
            }
        }catch (Exception e) {
            log.error("初始密码策略[{}]配置有误，{}", this.pwdModel, e.getMessage());
            e.printStackTrace();
            pwd = StringUtils.right("000000" + userNo,6);
        }
        return passwordEncoder.encode(pwd);
    }

    public void updatePwd(String operatorUser, String oldPass, String newPass) {
        SsoUser queryUser = new SsoUser();
        queryUser.setUserNo(operatorUser);
        queryUser.setDeleted("0");
        SsoUser ssoUser = ssoUserMapper.selectOne(queryUser);
        if (ssoUser == null){
            return;
        }
        if (passwordEncoder.matches(oldPass, ssoUser.getPasswd())) {
            ssoUser.setPasswd(passwordEncoder.encode(newPass));
            ssoUserMapper.updateByPrimaryKeySelective(ssoUser);
        }else {
            throw new RuleException(RuleException.UnknowErrors.UNKNOW.getCode(), "密码不正确，无法修改密码");
        }
    }


    public List<BaseModuleDto> getUserAuth(String userNo) {
        List<BaseModule> baseModules;
        if (rootUser.equals(userNo)) {
            baseModules = baseModuleMapper.selectAll();
        }else {

            SsoUser ssoUser = new SsoUser();
            ssoUser.setUserNo(userNo);
            //根据用户编码，获取用户信息，包含所有角色
            PageInfo<SsoUserDto> pageInfo = getUserPage(0,1, ssoUser);
            if (pageInfo == null) {
                //用户信息不存在
                return new ArrayList<>();
            }
            List<SsoUserDto> ssoUserDtos = pageInfo.getList();
            if (ssoUserDtos == null || ssoUserDtos.isEmpty()) {
                //用户信息不存在
                return  new ArrayList<>();
            }
            SsoUserDto ssoUserDto = ssoUserDtos.get(0);
            String roleIds = ssoUserDto.getRoleIds();
            if (StringUtils.isBlank(roleIds)) {
                //用户没有匹配的角色
                return  new ArrayList<>();
            }
            List<Integer> roleIdList = JSONArray.parseArray(roleIds, Integer.class);
            List<BaseRoleModule> baseRoleModules = baseRoleModuleMapper.selectByExample(Example.builder(BaseRoleModule.class)
                    .andWhere(WeekendSqls.<BaseRoleModule>custom().andIn(BaseRoleModule::getRoleId, roleIdList)).build());
            if (baseRoleModules == null || baseRoleModules.isEmpty()) {
                //用户没有权限配置
                return  new ArrayList<>();
            }
            Set<Long> moduleIds = new HashSet<>();
            baseRoleModules.forEach(baseRoleModule -> moduleIds.add(baseRoleModule.getModuleId()));
            baseModules = baseModuleMapper.selectByExample(Example.builder(BaseModule.class)
                    .andWhere(WeekendSqls.<BaseModule>custom().andIn(BaseModule::getId, moduleIds))
                    .orWhere(WeekendSqls.<BaseModule>custom().andIn(BaseModule::getParentId, moduleIds).andEqualTo(BaseModule::getMenuType, 3)).build());
        }
        List<BaseModuleDto> resultList = new ArrayList<>();
        baseModules.forEach(baseModule -> resultList.add(transfar(baseModule)));
        return resultList;
    }

    private BaseModuleDto transfar(BaseModule baseModule) {
        BaseModuleDto baseModuleDto = new BaseModuleDto();
        baseModuleDto.setHasLock(baseModule.getHasLock());
        baseModuleDto.setIcon(baseModule.getIcon());
        baseModuleDto.setId(baseModule.getId());
        baseModuleDto.setMenuName(baseModule.getMenuName());
        baseModuleDto.setMenuNo(baseModule.getMenuNo());
        baseModuleDto.setMenuType(baseModule.getMenuType());
        baseModuleDto.setMenuUrl(baseModule.getMenuUrl());
        baseModuleDto.setMethod(baseModule.getMethod());
        baseModuleDto.setOrderNum(baseModule.getOrderNum());
        baseModuleDto.setParentId(baseModule.getParentId());
        baseModuleDto.setState(baseModule.getState());
        return baseModuleDto;
    }


    public SsoUserDto getSsoUser(String username) {
        SsoUser ssoUser = ssoUserMapper.selectOneByExample(Example.builder(SsoUser.class)
                .andWhere(
                        WeekendSqls.<SsoUser>custom().andEqualTo(SsoUser::getUserNo, username)
                        .andEqualTo(SsoUser::getDeleted, 0)
                ).build());
        SsoUserDto ssoUserDto = transfar(ssoUser);
        if (ssoUserDto == null) {
            return null;
        }
        BaseGroup baseGroup = baseGroupMapper.selectByPrimaryKey(ssoUser.getGroupId());
        ssoUserDto.setGroupName(baseGroup.getGroupName());
        return ssoUserDto;
    }


}
