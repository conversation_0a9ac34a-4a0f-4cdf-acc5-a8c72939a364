package cn.ray.sso.manager.auth;

import javax.annotation.Resource;

import cn.ray.core.common.TreeHelper;
import cn.ray.sso.dao.entity.BaseModule;
import cn.ray.sso.dao.entity.BaseRole;
import cn.ray.sso.dao.mapper.BaseModuleMapper;
import cn.ray.sso.token.SsoSecurityUtils;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class MenuManager {

    @Resource
    private BaseModuleMapper baseModuleMapper;

    @Autowired
    private RoleUserManager roleUserManager;

    public TreeHelper getModuleTree(Long parentId) {
        String user = SsoSecurityUtils.getLoginUser();
        List<BaseModule> moduleList = baseModuleMapper.selectByExample(Example.builder(BaseModule.class)
        .andWhere(WeekendSqls.<BaseModule>custom().andEqualTo(BaseModule::getDeleted, false)).build());
        if (!"admin".equals(user)) {
            moduleList = moduleList.stream().filter(baseModule -> {
                if (baseModule.getId() == 596 || baseModule.getId() == 571 || baseModule.getId() == 598) {
                    if (baseModule.getId() == 571) {
                     baseModule.setParentId(parentId);
                    }
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
        }
        TreeHelper tree = new TreeHelper(new String[]{"id", "menuName", "menuNo", "menuUrl", "orderNum", "icon", "method", "parentId","menuType"},moduleList,7) {
            @Override
            public Object root() {
                return parentId;
            }

            @Override
            public Object[] idTextData(Object[] objects) {
                return objects;
            }

            @Override
            protected void setJsonAttributesData(JSONObject jo, String[] keys, Object[] nodeData) {
                super.setJsonAttributesData(jo, keys, nodeData);
                jo.put("title",jo.get("name"));
                jo.put("expand" , true);
            }
        };

        return tree;
    }

    public BaseModule getMenuByNo(Integer menuNo) {
        return baseModuleMapper.selectOneByExample(Example.builder(BaseModule.class)
        .andWhere(WeekendSqls.<BaseModule>custom().andEqualTo(BaseModule::getDeleted, false)
        .andEqualTo(BaseModule::getMenuNo, menuNo)).build());
    }

    public List<BaseModule> doGetNavByMenuNo(Integer menuNo) {
        List<BaseModule> moduleList = baseModuleMapper.selectAll();
        Map<Integer, BaseModule> baseModuleMap = new HashMap<>();
        moduleList.forEach(baseModule -> baseModuleMap.put(baseModule.getMenuNo(), baseModule));
        BaseModule parentModule = baseModuleMap.get(menuNo);
        List<BaseModule> resultList = new ArrayList<>();
        int index = 0;
        if (parentModule != null) {
            resultList.add(parentModule);
            while (parentModule != null) {
                parentModule = baseModuleMap.get(parentModule.getMenuNo());
                resultList.add(0, parentModule);
                if (index++ > 10){
                    break;
                }
            }
        }
        return resultList;

    }

    public void saveMenu(BaseModule menu) {
        if (menu.getId() == null) {
            menu.setCreateDate(menu.getUpdateDate());
            menu.setUpdator(menu.getUpdator());
            baseModuleMapper.insertSelective(menu);
        }else {
            baseModuleMapper.updateByPrimaryKeySelective(menu);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteMenu(String ids) {
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            baseModuleMapper.deleteByPrimaryKey(id);
        }
    }
}
