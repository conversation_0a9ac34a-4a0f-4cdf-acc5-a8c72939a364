package cn.ray.sso.manager.auth;

import javax.annotation.Resource;

import cn.ray.sso.dao.entity.BaseRole;
import cn.ray.sso.dao.entity.BaseRoleModule;
import cn.ray.sso.dao.entity.BaseUserRole;
import cn.ray.sso.dao.mapper.BaseRoleMapper;
import cn.ray.sso.dao.mapper.BaseRoleModuleMapper;
import cn.ray.sso.dao.mapper.BaseUserRoleMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.List;

/**
 * 角色业务实现层
 *
 * <AUTHOR>
 * @create 2018-03-2
 */

@Component
public class RoleManager {

    @Resource
    private BaseRoleMapper baseRoleMapper;

    @Resource
    private BaseRoleModuleMapper baseRoleModuleMapper;

    @Resource
    private BaseUserRoleMapper baseUserRoleMapper;

    public Page<BaseRole> selectRoleList(int pageIndex, int pageSize) {
        Page<BaseRole> pageInfo = PageHelper.startPage(pageIndex,pageSize);
        List<BaseRole> roles= selectRoleList();
        return pageInfo;
    }


    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Long id) {
        baseRoleMapper.deleteByPrimaryKey(id);
        baseRoleModuleMapper.deleteByExample(Example.builder(BaseRoleModule.class).andWhere(WeekendSqls.<BaseRoleModule>custom()
        .andEqualTo(BaseRoleModule::getRoleId, id)).build());
        baseUserRoleMapper.deleteByExample(Example.builder(BaseUserRole.class).andWhere(WeekendSqls.<BaseUserRole>custom()
                .andEqualTo(BaseUserRole::getRoleId, id)).build());
    }


    public BaseRole selectRoleById(Long id) {
        return baseRoleMapper.selectByPrimaryKey(id);
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveRole(BaseRole role) {
        if (role.getId() !=null ){
            baseRoleMapper.updateByPrimaryKeySelective(role);
        }else{
            role.setState(1);
            role.setDeleted(false);
            role.setCreator(role.getUpdator());
            role.setCreateDate(role.getUpdateDate());
            baseRoleMapper.insertSelective(role);
        }
    }


    public List<BaseRole> selectRoleList() {
        List<BaseRole> roles= baseRoleMapper.selectByExample(
                Example.builder(BaseRole.class)
                        .andWhere(getBaseWeekendSqls())
                        .build());
        return roles;
    }

    private WeekendSqls getBaseWeekendSqls() {
        return WeekendSqls.<BaseRole>custom().andEqualTo(BaseRole::getDeleted, false);
    }
}
