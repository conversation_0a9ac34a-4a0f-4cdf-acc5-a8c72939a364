package cn.ray.sso.excel;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.StringUtils;

public class ExcelExport {
    /**
     * 导出Excel
     * @param columns
     * @param list
     * @param fileName
     * @param response
     * @throws IOException
     */
    public static void downLoadExcel(List<ExcelCell[]> columns, List<Map<String,Object>> list, String fileName, HttpServletResponse response)throws IOException{
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        createWorkBook(fileName,list,columns).write(os);
        byte[] content = os.toByteArray();
        InputStream is = new ByteArrayInputStream(content);
        response.reset();
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        if(StringUtils.isEmpty(fileName)){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            fileName = sdf.format(new Date());
        }
        response.setHeader("Content-Disposition", "attachment;filename="+ new String((fileName + ".xls").getBytes(), "iso-8859-1"));
        ServletOutputStream out = response.getOutputStream();
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        try {
            bis = new BufferedInputStream(is);
            bos = new BufferedOutputStream(out);
            byte[] buff = new byte[2048];
            int bytesRead;
            // Simple read/write loop.
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
            }
        } catch (final IOException e) {
            throw e;
        } finally {
            if (bis != null) {
                bis.close();
            }
            if (bos != null) {
                bos.close();
            }
        }
    }

    /**
     * 创建excel文档，
     * @param list 数据
     * @param columns excel的列名
     * */
    public static Workbook createWorkBook(String fileName, List<Map<String, Object>> list, List<ExcelCell[]> columns) {
        // 创建excel工作簿
        Workbook wb = new HSSFWorkbook();
        // 创建第一个sheet（页），并命名
        Sheet sheet = wb.createSheet(fileName.split("\\.")[0]);
        // 手动设置列宽。第一个参数表示要为第几列设；，第二个参数表示列的宽度，n为列高的像素数。
        ExcelCell[] keysCell = columns.get(columns.size() - 1);
        for(int i=0;i<keysCell.length;i++){
            sheet.setColumnWidth((short) i, (short) (35.7 * 150));
        }

        // 创建表头样式
        CellStyle headerCellStyle = wb.createCellStyle();
        Font headerFont = wb.createFont();
        // 创建字体样式（用于列名）
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.BLACK.getIndex());
        headerFont.setBold(true);
        // 设置单元格的样式（用于列名）
        headerCellStyle.setFont(headerFont);
        headerCellStyle.setBorderLeft(BorderStyle.THIN);
        headerCellStyle.setBorderRight(BorderStyle.THIN);
        headerCellStyle.setBorderTop(BorderStyle.THIN);
        headerCellStyle.setBorderBottom(BorderStyle.THIN);
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);


        CellStyle valueCellStyle = wb.createCellStyle();
        Font valueFont = wb.createFont();
        // 创建字体样式（用于值）
        valueFont.setFontHeightInPoints((short) 12);
        valueFont.setColor(IndexedColors.BLACK.getIndex());
        // 设置单元格的样式（用于值）
        valueCellStyle.setFont(valueFont);
        valueCellStyle.setBorderLeft(BorderStyle.THIN);
        valueCellStyle.setBorderRight(BorderStyle.THIN);
        valueCellStyle.setBorderTop(BorderStyle.THIN);
        valueCellStyle.setBorderBottom(BorderStyle.THIN);
        valueCellStyle.setAlignment(HorizontalAlignment.CENTER);
//        valueCellStyle.setWrapText(true);

        headerFont.setBold(true);

        int rowCount = columns.size();
        //设置列名
        for(int i=0; i<rowCount; i++){
            // 创建表头
            Row row = sheet.createRow((short) i);
            ExcelCell[] cells = columns.get(i);
            for (ExcelCell excelCell : cells) {
                if (excelCell.getCells() < 0 || excelCell.getRows() < 0) {
                    //占位列
                    continue;
                }
                Cell cell = row.createCell(excelCell.getStartCellIndex());
                cell.setCellValue(excelCell.getTitle());
                cell.setCellStyle(headerCellStyle);
                if (excelCell.getCells() > 1 || excelCell.getRows() > 1) {
                    CellRangeAddress region = new CellRangeAddress(i, i+excelCell.getRows()-1, excelCell.getStartCellIndex(), excelCell.getStartCellIndex()+excelCell.getCells()-1);
                    sheet.addMergedRegion(region);
                }
            }
        }

        //设置每行每列的值
        for (int i = 0; i < list.size(); i++) {
            // Row 行,Cell 方格 , Row 和 Cell 都是从0开始计数的
            // 创建一行，在页sheet上
            Row row1 = sheet.createRow(i + rowCount);
            Map<String, Object> valueMap = list.get(i);
            // 在row行上创建一个方格
            for(short j=0;j<keysCell.length;j++){
                Cell cell = row1.createCell(j);
                ExcelCell excelCell = keysCell[j];
                Object value = valueMap.getOrDefault(excelCell.getKey(), "");
                cell.setCellValue(value.toString());
                cell.setCellStyle(valueCellStyle);
            }
        }
        return wb;
    }
}

