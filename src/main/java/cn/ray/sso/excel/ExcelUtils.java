package cn.ray.sso.excel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

public class ExcelUtils {

    private static Logger logger = Logger.getLogger(ExcelUtils.class);

    /**
     *
     * <B>概要说明：是否是2003的excel，返回true是2003</B><BR>
     *
     * @param: [filePath]
     * @see: so.songe.zhdt.usercenter.util.ExcelUtils#isExcel2003( )
     * @return: boolean
     *
     */
    public static boolean isExcel2003(String filePath)  {
        return filePath.matches("^.+\\.(?i)(xls)$");
    }

    /**
     *
     * <B>概要说明：是否是2007的excel，返回true是2007</B><BR>
     *
     * @param: [filePath]
     * @see: so.songe.zhdt.usercenter.util.ExcelUtils#isExcel2007()
     * @return: boolean
     *
     */
    public static boolean isExcel2007(String filePath)  {
        return filePath.matches("^.+\\.(?i)(xlsx)$");
    }

    /**
     *
     * <B>概要说明：验证验证EXCEL文件</B><BR>
     *
     * @param: [filePath]
     * @see: so.songe.zhdt.usercenter.util.ExcelUtils#validateExcel()
     * @return: boolean
     *
     */
    public static boolean isExcelFile(String filePath){
        if (null == filePath || !(isExcel2003(filePath) || isExcel2007(filePath))){
            return false;
        }
        return true;
    }

    /**
     *
     * <B>概要说明：获取excel数据</B><BR>
     *
     * @param: [wb]
     * @see: so.songe.zhdt.usercenter.util.ExcelUtils#getExcelInfo()
     * @return: java.util.List<java.util.Map<java.lang.Object,java.lang.String>>
     *
     */
    public static List<Map<Object, String>> getExcelInfo(Sheet sheet){
        return getExcelInfo(sheet, 1);
    }
    /**
     *
     * <B>概要说明：获取excel数据</B><BR>
     *
     * @param: [wb]
     * @see: so.songe.zhdt.usercenter.util.ExcelUtils#getExcelInfo()
     * @return: java.util.List<java.util.Map<java.lang.Object,java.lang.String>>
     *
     */
    public static List<Map<Object, String>> getExcelInfo(Sheet sheet, int dataBeginRowIndex){

        if(null == sheet){
            return null;
        }
        try{
            // 得到Excel的行数
            int totalRows = sheet.getPhysicalNumberOfRows();
            // 总列数
            int totalCells = 0;
            // 得到Excel的列数(前提是有行数)，从第二行算起
//            if(totalRows >= 2 && sheet.getRow(1) != null){
//                totalCells = sheet.getRow(1).getPhysicalNumberOfCells();
//            }
			// 得到Excel的列数(前提是有行数)，从第一行算，拿到多少列，第二行后面可能为空
			if(totalRows > dataBeginRowIndex && sheet.getRow(0) != null){
				totalCells = sheet.getRow(dataBeginRowIndex-1).getPhysicalNumberOfCells();
			}
            List<Map<Object, String>> relList = new ArrayList<Map<Object, String>>();

            // 循环Excel行数,从第二行开始
            for(int r = dataBeginRowIndex; r < totalRows; r++){
                Map<Object, String> map = new HashMap<Object, String>();
                Row row = sheet.getRow(r);
                // 循环Excel的列
                for(int c = 0; c < totalCells; c++){
                    Cell cell = row.getCell(c);
                    if (cell==null){
						map.put(c, "");
//                        break;
                    }else{
						cell.setCellType(CellType.STRING);
                        map.put(c, StringUtils.trim(cell.getStringCellValue()));
					}
                }
                relList.add(map);
            }

            return relList;
        }catch(Exception e){
            logger.error(e.getMessage());
        }

        return null;
    }

}
