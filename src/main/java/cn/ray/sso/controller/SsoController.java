/**
 *
 */
package cn.ray.sso.controller;

import cn.ray.core.result.MvcResult;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 *
 * <AUTHOR>
 */
@RestController
public class SsoController {

	@RequestMapping(value = "/session/user", method = {RequestMethod.GET,RequestMethod.POST})
	public MvcResult<Authentication> sessionUser(Authentication user){
		return MvcResult.success(user);
	}

}
