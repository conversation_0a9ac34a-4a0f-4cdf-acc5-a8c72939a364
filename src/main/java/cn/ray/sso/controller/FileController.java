package cn.ray.sso.controller;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.ray.core.result.MvcResult;

@RestController
@RequestMapping("/file")
@Slf4j
public class FileController {

    @Value("${file.path}")
    private String uploadPath;

    @Value("${file.prefix:/static/image/consult/}")
    private String imageBasePath;

    @PostMapping("/upload")
    public MvcResult upload(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            MvcResult.failure("文件为空");
        }

        String fileName = file.getOriginalFilename();
        String filePath = uploadPath;
        File folder = new File(filePath);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        String newFileName = UUID.randomUUID() + fileName.substring(fileName.lastIndexOf("."));
        File dest = new File(filePath + newFileName);
        try {
            file.transferTo(dest);
            log.info("上传成功");
            return  MvcResult.success(imageBasePath + newFileName);
        } catch (IOException e) {
            log.error("上传文件失败：{}", e.getMessage() ,e);
            return  MvcResult.failure(e.getMessage());
        }
    }
}
