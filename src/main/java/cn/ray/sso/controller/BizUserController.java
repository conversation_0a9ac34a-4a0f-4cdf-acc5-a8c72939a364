package cn.ray.sso.controller;

import javax.annotation.Resource;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.ray.core.result.MvcResult;
import cn.ray.sso.dao.entity.BizUserInfo;
import cn.ray.sso.dao.mapper.BizUserInfoMapper;
import cn.ray.sso.excel.ExcelUtils;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

/**
 * @Title BizUserController
 * <AUTHOR>
 * @Description //TODO
 * @Date 2021/2/27 13:08
 **/
@RestController
@RequestMapping("/bizUser")
@Slf4j
public class BizUserController {

    public static final Long OUT_DEPT_ID = 999999L;

    @Resource
    private BizUserInfoMapper bizUserInfoMapper;

    @GetMapping
    public MvcResult getUserPage(String username, Integer pageNum, Integer pageSize) {
        Page page = PageHelper.startPage(pageNum, pageSize);
        WeekendSqls<BizUserInfo> weekendSqls = WeekendSqls.custom();
        weekendSqls.andEqualTo(BizUserInfo::getUserType, "2");
        if (StringUtils.isNotBlank(username)) {
            weekendSqls.andLike(BizUserInfo::getUsername, "%" + username + "%");
        }
        bizUserInfoMapper.selectByExample(Example.builder(BizUserInfo.class).andWhere(weekendSqls).orderByDesc("id").build());
        PageInfo pageInfo = new PageInfo(page);
        return MvcResult.success(pageInfo);
    }

    @GetMapping("/all")
    public MvcResult getUserPage2(String username, Integer pageNum, Integer pageSize) {
        Page page = PageHelper.startPage(pageNum, pageSize);
        WeekendSqls<BizUserInfo> weekendSqls = WeekendSqls.custom();
        if (StringUtils.isNotBlank(username)) {
            weekendSqls.andLike(BizUserInfo::getUsername, "%" + username + "%");
        }
        bizUserInfoMapper.selectByExample(Example.builder(BizUserInfo.class).andWhere(weekendSqls).orderByDesc("id").build());
        PageInfo pageInfo = new PageInfo(page);
        return MvcResult.success(pageInfo);
    }

    @GetMapping("/{id}")
    public MvcResult getUser(@PathVariable("id") Long id) {
        BizUserInfo bizUserInfo = bizUserInfoMapper.selectByPrimaryKey(id);
        return MvcResult.success(bizUserInfo);
    }

    @RequestMapping(method = {RequestMethod.POST, RequestMethod.PUT})
    public MvcResult saveUser(BizUserInfo bizUserInfo) {
        bizUserInfo.setUserType("2");
        bizUserInfo.setDeptId(BizUserController.OUT_DEPT_ID);
        if (StringUtils.isBlank(bizUserInfo.getOpenId())) {
            bizUserInfo.setOpenId(null);
        }
        if (bizUserInfo.getOutId() == null) {
            BizUserInfo oldInfo = bizUserInfoMapper.selectOneByExample(Example.builder(BizUserInfo.class).andWhere(
                    WeekendSqls.<BizUserInfo>custom().andEqualTo(BizUserInfo::getIdCard, bizUserInfo.getIdCard())
            ).build());
            if (oldInfo != null) {
                return MvcResult.failure(-1001, "身份证号已经存在，不能保存");
            }
            bizUserInfoMapper.insertSelective(bizUserInfo);
        }else {
            BizUserInfo oldInfo = bizUserInfoMapper.selectOneByExample(Example.builder(BizUserInfo.class).andWhere(
                    WeekendSqls.<BizUserInfo>custom().andEqualTo(BizUserInfo::getIdCard, bizUserInfo.getIdCard())
                    .andNotEqualTo(BizUserInfo::getOutId, bizUserInfo.getOutId())
            ).build());
            if (oldInfo != null) {
                return MvcResult.failure(-1001, "身份证号已经存在，不能保存");
            }
            bizUserInfoMapper.updateByExampleSelective(bizUserInfo, Example.builder(BizUserInfo.class).andWhere(
                    WeekendSqls.<BizUserInfo>custom().andEqualTo(BizUserInfo::getOutId, bizUserInfo.getOutId())
            ).build());
        }
        return MvcResult.success();
    }

    @DeleteMapping
    public MvcResult deleteUser(String ids) {
        List<Long> userIds = JSONArray.parseArray(ids, Long.class);
        bizUserInfoMapper.deleteByExample(Example.builder(BizUserInfo.class).andWhere(
                WeekendSqls.<BizUserInfo>custom().andIn(BizUserInfo::getOutId, userIds)
        ).build());
        return MvcResult.success();
    }

    @PostMapping("/import")
    public MvcResult upload(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return MvcResult.failure("上传失败，请选择文件");
        }

        try {
            String fileName = file.getOriginalFilename();
            Workbook wb;
            // 根据文件名判断文件是2003版本还是2007版本
            if (ExcelUtils.isExcel2007(fileName)) {
                wb = new XSSFWorkbook(file.getInputStream());
            } else {
                wb = new HSSFWorkbook(file.getInputStream());
            }

            Sheet sheet = wb.getSheetAt(0);
            List<Map<Object, String>> excelInfo = ExcelUtils.getExcelInfo(sheet, 2);
            if (null == excelInfo) {
                // EXCEL格式有误
                return MvcResult.failure(-1002, "文件格式有误");
            }
            List<BizUserInfo> userInfos = new ArrayList<>();
            excelInfo.forEach(row -> {
                BizUserInfo oldInfo = bizUserInfoMapper.selectOneByExample(Example.builder(BizUserInfo.class).andWhere(
                        WeekendSqls.<BizUserInfo>custom().andEqualTo(BizUserInfo::getIdCard, row.get(2))
                ).build());
                if (oldInfo == null) {
                    BizUserInfo bizUserInfo = new BizUserInfo();
                    bizUserInfo.setUsername(row.get(1));
                    bizUserInfo.setIdCard(row.get(2));
                    bizUserInfo.setUserType("2");
                    bizUserInfo.setDeptId(999999L);
                    userInfos.add(bizUserInfo);
                }
            });
            if (userInfos.isEmpty()) {
                return MvcResult.failure(-1003, "未发现数据");
            }
            int cnt = bizUserInfoMapper.insertList(userInfos);
            // 执行导入
            return MvcResult.success(cnt);
        } catch (IOException e) {
            e.printStackTrace();
            log.error("导入课程失败,{}", e.getMessage());
            return MvcResult.failure(-1009, "上传失败, " + e.getMessage());
        }

    }



}
