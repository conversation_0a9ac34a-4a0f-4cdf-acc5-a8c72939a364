package cn.ray.sso.controller;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import cn.ray.core.common.DateUtil;
import cn.ray.core.result.MvcResult;
import cn.ray.sso.dao.entity.*;
import cn.ray.sso.dao.mapper.*;
import cn.ray.sso.token.SsoSecurityUtils;

/**
 * @Title NgsCenterController
 * <AUTHOR>
 * @Description //TODO
 * @Date 2024/8/20 23:03
 **/
@RequestMapping("/center")
@RestController
public class NgsCenterController {

    @Resource
    private NgsCenterMapper ngsCenterMapper;

    @Resource
    private NgsCenterUserMapper ngsCenterUserMapper;

    @Resource
    private SsoUserMapper ssoUserMapper;

    @Resource
    private NgsCenterTypeMapper ngsCenterTypeMapper;

    @Resource
    private BaseGroupMapper baseGroupMapper;


    @GetMapping("/type")
    public MvcResult getCenterList() {
        List<NgsCenterType> ngsCenterTypeList = ngsCenterTypeMapper.selectAll();
        return MvcResult.success(ngsCenterTypeList);
    }

    @GetMapping
    public MvcResult getCenterList(String lnglat, String centerName, String centerType, Integer pageNum, Integer pageSize) {
        WeekendSqls<NgsCenter> weekendSqls = WeekendSqls.custom();
        weekendSqls.andEqualTo(NgsCenter::getDeleted, "0");
        if (StringUtils.isNotBlank(centerName)) {
            weekendSqls.andLike(NgsCenter::getCenterName, "%" + centerName + "%");
        }
        if (StringUtils.isNotBlank(centerType)) {
            weekendSqls.andEqualTo(NgsCenter::getCenterType, centerType);
        }
        JSONObject lnglatJson = new JSONObject();
        if (StringUtils.isNotBlank(lnglat)) {
            String[] arr = lnglat.split(",");
            if (arr.length == 2) {
                lnglatJson.put("lng", arr[0]);
                lnglatJson.put("lat", arr[1]);
            }
        }
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<NgsCenter> ngsCenterList = ngsCenterMapper.selectByExample(Example.builder(NgsCenter.class).andWhere(weekendSqls).orderBy("centerName").build());
        if (!ngsCenterList.isEmpty()) {
            Set<Long> townIds = ngsCenterList.stream().map(NgsCenter::getTownId).collect(Collectors.toSet());
            List<BaseGroup> baseGroupList = baseGroupMapper.selectByExample(Example.builder(BaseGroup.class).andWhere(
                    WeekendSqls.<BaseGroup>custom().andIn(BaseGroup::getId, townIds)
            ).build());
            if (!baseGroupList.isEmpty()) {
                Map<Long, String> townMap =  baseGroupList.stream()
                        .collect(Collectors.toMap(BaseGroup::getId, BaseGroup::getGroupName));
                ngsCenterList.stream().forEach(ngsCenter -> {
                    if (ngsCenter.getTownId() != null) {
                        ngsCenter.setTownName(townMap.getOrDefault(ngsCenter.getTownId(),  ngsCenter.getTownId().toString()));
                    }
                    if (!lnglatJson.isEmpty() && StringUtils.isNotBlank(ngsCenter.getLatlng())) {
                        String[] centerLatLng = ngsCenter.getLatlng().split(",");
                        if (centerLatLng.length == 2) {
                            ngsCenter.setDistance(getDistance(lnglatJson.getDoubleValue("lng"), lnglatJson.getDoubleValue("lat"), Double.valueOf(centerLatLng[0]).doubleValue(), Double.valueOf(centerLatLng[1]).doubleValue()));
                        }
                    }
                });
            }
        }
        return MvcResult.success(new PageInfo<>(page));
    }


    @PostMapping
    public MvcResult saveCenter(NgsCenter ngsCenter) {
        ngsCenter.setUpdator(SsoSecurityUtils.getLoginUser());
        ngsCenter.setUpdateDate(DateUtil.getNowDate());
        if (StringUtils.isBlank(ngsCenter.getCreateDate())) {
            ngsCenter.setCreator(ngsCenter.getUpdator());
            ngsCenter.setCreateDate(ngsCenter.getUpdateDate());
        }
        if (ngsCenter.getId() == null) {
            ngsCenterMapper.insertSelective(ngsCenter);
        }else {
            ngsCenterMapper.updateByPrimaryKeySelective(ngsCenter);
        }
        String usernos = ngsCenter.getUsernos();
        if (StringUtils.isNotBlank(usernos)) {
            ngsCenterUserMapper.deleteByExample(Example.builder(NgsCenterUser.class).andWhere(
                    WeekendSqls.<NgsCenterUser>custom().andEqualTo(NgsCenterUser::getCenterId, ngsCenter.getId())
            ).build());
            String[] usernoList = usernos.split(",");
            List<NgsCenterUser> centerUserList = new ArrayList<>();
            for (String userno : usernoList) {
                NgsCenterUser ngsCenterUser = new NgsCenterUser();
                ngsCenterUser.setCenterId(ngsCenter.getId());
                ngsCenterUser.setUserNo(userno);
                centerUserList.add(ngsCenterUser);
            }
            ngsCenterUserMapper.insertList(centerUserList);
        }
        return MvcResult.success();
    }

    @DeleteMapping
    public MvcResult deleteCenter(Long id) {
        NgsCenter ngsCenter = new NgsCenter();
        ngsCenter.setId(id);
        ngsCenter.setDeleted("1");
        ngsCenterMapper.updateByPrimaryKeySelective(ngsCenter);
        return MvcResult.success();
    }


    @GetMapping("owner")
    public MvcResult getOwnerCenterList() {
        String userno = SsoSecurityUtils.getLoginUser();
//        List<NgsCenterUser> centerUserList = ngsCenterUserMapper.selectByExample(Example.builder(NgsCenterUser.class).andWhere(
//                WeekendSqls.<NgsCenterUser>custom().andEqualTo(NgsCenterUser::getUserNo, userno)
//        ).build());
//        if (!centerUserList.isEmpty()) {
//        Set<Long> centerIds = centerUserList.stream().map(NgsCenterUser::getCenterId).collect(Collectors.toSet());
        SsoUser ssoUser = ssoUserMapper.selectOneByExample(Example.builder(SsoUser.class).andWhere(
                WeekendSqls.<SsoUser>custom().andEqualTo(SsoUser::getUserNo, userno)
                        .andEqualTo(SsoUser::getDeleted, "0")
        ).build());
        WeekendSqls<NgsCenter> weekendSqls = WeekendSqls.custom();
        if (!ssoUser.getGroupId().equals(1L)) {
            weekendSqls.andEqualTo(NgsCenter::getTownId, ssoUser.getGroupId());
        }
        weekendSqls.andEqualTo(NgsCenter::getDeleted, "0");
        List<NgsCenter> centerList = ngsCenterMapper.selectByExample(Example.builder(NgsCenter.class).andWhere(weekendSqls).build());
        if (!centerList.isEmpty()) {
            List<NgsCenterType> ngsCenterTypeList = ngsCenterTypeMapper.selectAll();
            Map<String, String> ngsCenterTypeMap = ngsCenterTypeList.stream().collect(Collectors.toMap(NgsCenterType::getCod, NgsCenterType::getTitle));
            List<BaseGroup> groupList = baseGroupMapper.selectAll();
            Map<Long, String> groupMap = groupList.stream().collect(Collectors.toMap(BaseGroup::getId, BaseGroup::getGroupName));
            Map<Long, Map<String, List<NgsCenter>>> childs = new HashMap<>();
            centerList.stream().forEach(ngsCenter -> {
                ngsCenter.setTownName(groupMap.getOrDefault(ngsCenter.getTownId(), ngsCenter.getTownName()));
                Map<String, List<NgsCenter>> centerTypeMap = childs.getOrDefault(ngsCenter.getTownId(), new HashMap<>());
                List<NgsCenter> data = centerTypeMap.getOrDefault(ngsCenter.getCenterType(), new ArrayList<>());
                data.add(ngsCenter);
                centerTypeMap.put(ngsCenter.getCenterType(), data);
                childs.put(ngsCenter.getTownId(), centerTypeMap);
            });
            List<JSONObject> relData = new ArrayList<>();
            childs.forEach((townId, townMap) -> {
                JSONObject data = new JSONObject();
                data.put("id", townId);
                data.put("centerName", groupMap.getOrDefault(townId, townId.toString()));
                List<JSONObject> subDatas = new ArrayList<>();
                townMap.forEach((centerType, centerTypeList) -> {
                    JSONObject subData = new JSONObject();
                    subData.put("id", centerType);
                    subData.put("typeId", townId);
                    subData.put("centerName", ngsCenterTypeMap.getOrDefault(centerType, centerType));
                    subData.put("children", centerTypeList);
                    subDatas.add(subData);
                });
                data.put("children", subDatas);
                data.put("expand", true);
                data.put("disabled", true);
                relData.add(data);
            });
            return MvcResult.success(relData);
        }
        return MvcResult.success();

//        }
    }

    /**
     * 赤道半径（单位：米）
     */
    private static final double EQUATOR_RADIUS = 6378137;

    private String getDistance(double longitude1, double latitude1, double longitude2, double latitude2) {
        // 纬度
        double lat1 = Math.toRadians(latitude1);
        double lat2 = Math.toRadians(latitude2);
        // 经度
        double lon1 = Math.toRadians(longitude1);
        double lon2 = Math.toRadians(longitude2);
        // 纬度之差
        double a = lat1 - lat2;
        // 经度之差
        double b = lon1 - lon2;
        // 计算两点距离的公式
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        // 弧长乘赤道半径, 返回单位: 米
        s = s * EQUATOR_RADIUS;
//        return s;
        if (s > 500) {
            return new BigDecimal(s / 1000).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() + "km";
        }else {
            return s + "m";
        }
    }
}
