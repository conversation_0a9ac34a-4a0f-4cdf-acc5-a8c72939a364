package cn.ray.sso.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import cn.ray.core.result.MvcResult;

/**
 * @Title IpController
 * <AUTHOR>
 * @Description //TODO
 * @Date 2024/10/22 17:13
 **/
@RestController
@RequestMapping("/ip")
@Slf4j
public class IpController {

    @Resource
    private RestTemplate restTemplate;

    public static final Map<String, JSONObject> LAT_LNG_MAP = new HashMap<>();

    @GetMapping
    public MvcResult getLatLng(String key, HttpServletRequest request) {
        String ip = request.getHeader("X-Real-IP");
        log.info("取定位ip：{}", ip);
        if (StringUtils.isBlank(ip)) {
            return MvcResult.failure(ip);
        }
        JSONObject latlng = LAT_LNG_MAP.get(ip);
        if (latlng == null) {
            String rel = restTemplate.getForObject("https://apis.map.qq.com/ws/location/v1/ip?key=" + key + "&ip=" + ip, String.class);
            JSONObject relJson = JSONObject.parseObject(rel);
            if (relJson.getIntValue("status") == 0) {
                latlng = relJson.getJSONObject("result").getJSONObject("location");
                LAT_LNG_MAP.put(ip, latlng);
            }
        }
        return MvcResult.success(latlng);
    }
}
