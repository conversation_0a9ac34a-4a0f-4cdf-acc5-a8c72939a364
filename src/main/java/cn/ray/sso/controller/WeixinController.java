package cn.ray.sso.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import cn.ray.core.result.MvcResult;
import cn.ray.sso.rsa.RSACoder;

/**
 * @Title WeixinController
 * <AUTHOR>
 * @Description //TODO
 * @Date 2024/9/13 17:05
 **/
@RequestMapping("/weixin")
@RestController
@Slf4j
public class WeixinController {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RSACoder rsaCoder;

    private String appId = "91760024";

    private String secret = "909174d4-6cb4-4e2e-95da-8406038d7a8f";

    @GetMapping("/iptest")
    public MvcResult getTicket(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");

        return MvcResult.success(ip);
    }
    @GetMapping("/ticket")
    public MvcResult getTicket(String url, HttpServletRequest request) {
        String timestamp = request.getHeader("timestamp");
        String sign = request.getHeader("sign");
        if (StringUtils.isBlank(url) ||StringUtils.isBlank(timestamp) ||StringUtils.isBlank(sign)) {
            return MvcResult.failure("必要参数为空");
        }
        String paramsInfo = "";
        try {
            paramsInfo = rsaCoder.decryptByPrivateKey(Base64.decodeBase64(sign));
        } catch (Exception e) {
            log.error("解密参数{}失败：{}", sign, e.getMessage(), e);
            return MvcResult.failure("参数有误");
        }
//        String[] params = paramsInfo.split("&");
        log.info("解密结果： {}", paramsInfo);
//        if (params.length != 2) {
//            return MvcResult.failure("加密参数不正确");
//        }
//        Map<String, String> paramMap = new HashMap<>();
//        paramMap.put("timestamp", params[0].substring(10));
//        paramMap.put("url", params[1].substring(4));
        if (StringUtils.isBlank(paramsInfo)) {
            return MvcResult.failure("无权限");
        }
        String signTimestamp = paramsInfo.substring(10);
        Long nowTime = System.currentTimeMillis();
        long time = Math.abs(nowTime - Long.valueOf(signTimestamp));
        if (time > 5 * 60 * 1000) {
            log.error("加密超时,当前时间：{}，参数时间：{},相差：{}毫秒", nowTime, signTimestamp, time);
            return MvcResult.failure("无权限");
        }
        String rel = restTemplate.getForObject("http://10.32.84.245:9801/baseinf/remote/wechatserver/getUrlSign?appId="+appId+"&secret="+secret+"&url=" + url, String.class);
        JSONObject data = JSONObject.parseObject(rel);
        return MvcResult.success(data);
    }

    public String getMD5Str(String str) {
        byte[] digest = null;
        try {
            MessageDigest md5 = MessageDigest.getInstance("md5");
            digest  = md5.digest(str.getBytes("utf-8"));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //16是表示转换为16进制数
        String md5Str = new BigInteger(1, digest).toString(16);
        return md5Str;
    }


    public static void main(String[] args) {
        WeixinController weixinController = new WeixinController();
        System.out.println(weixinController.getMD5Str(weixinController.getMD5Str("appId=" + weixinController.appId + "&url="+"https://www.xsbsfwzx.com") + weixinController.secret));
    }
}
