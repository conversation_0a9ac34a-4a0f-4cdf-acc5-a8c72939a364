package cn.ray.sso.controller;

import javax.annotation.Resource;

import java.util.List;

import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import cn.ray.core.result.MvcResult;
import cn.ray.sso.dao.entity.NgsCenterProject;
import cn.ray.sso.dao.mapper.NgsCenterProjectMapper;

/**
 * @Title NgsCenterProjectController
 * <AUTHOR>
 * @Description //TODO
 * @Date 2024/8/24 19:52
 **/
@RequestMapping("/center/project")
@RestController
public class NgsCenterProjectController {

    @Resource
    private NgsCenterProjectMapper ngsCenterProjectMapper;

    @GetMapping("/{centerId}")
    public MvcResult getList(@PathVariable Long centerId) {
        if (centerId == null) {
            return MvcResult.success();
        }
        List<NgsCenterProject> centerProjectList = ngsCenterProjectMapper.selectByExample(Example.builder(NgsCenterProject.class).andWhere(
                WeekendSqls.<NgsCenterProject>custom().andEqualTo(NgsCenterProject::getCenterId, centerId)
        ).orderByDesc("sortNum").build());
        return MvcResult.success(centerProjectList);
    }

    @PostMapping
    public MvcResult save(NgsCenterProject ngsCenterProject) {
        if (ngsCenterProject.getId() == null) {
            ngsCenterProjectMapper.insertSelective(ngsCenterProject);
        }else {
            ngsCenterProjectMapper.updateByPrimaryKeySelective(ngsCenterProject);
        }
        return MvcResult.success();
    }

    @DeleteMapping("/{id}")
    public MvcResult del(@PathVariable("id") Long id) {
        if (id == null) {
            return MvcResult.failure("参数为空");
        }
        ngsCenterProjectMapper.deleteByPrimaryKey(id);
        return MvcResult.success();
    }
}
