package cn.ray.sso.controller;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.ray.core.result.MvcResult;
import cn.ray.sso.dao.DeptUserCount;
import cn.ray.sso.dao.entity.BizDept;
import cn.ray.sso.dao.mapper.BizDeptMapper;
import cn.ray.sso.dao.mapper.BizUserInfoMapper;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

@RequestMapping("/bizDept")
@RestController
public class BizDeptController {

    @Resource
    private BizDeptMapper bizDeptMapper;

    @Resource
    private BizUserInfoMapper bizUserInfoMapper;

    @GetMapping
    public MvcResult getDeptPage(BizDept bizDept, int pageIndex, int pageSize) {

        List<DeptUserCount> deptUserCounts = bizUserInfoMapper.getDeptUserCount();
        Map<Long, Integer> deptUserCountMap = new HashMap<>();
        deptUserCounts.forEach(deptUserCount -> deptUserCountMap.put(deptUserCount.getDeptId(), deptUserCount.getUserCount()));
        Page page = PageHelper.startPage(pageIndex, pageSize);
        bizDeptMapper.selectByExample(Example.builder(BizDept.class).orderBy("deptName").build());
        PageInfo<BizDept> pageInfo = new PageInfo<>(page);
        pageInfo.getList().forEach(bd -> {
            Integer count = deptUserCountMap.get(bd.getId());
            bd.setUserCount(count == null ? 0 : count);
        });
        return MvcResult.success(pageInfo);
    }
    @RequestMapping(method = {RequestMethod.POST, RequestMethod.PUT})
    public MvcResult saveDept(BizDept bizDept) {
        if (bizDept.getId() == null) {
            bizDeptMapper.insert(bizDept);
        }else {
            bizDeptMapper.updateByPrimaryKey(bizDept);
        }
        return MvcResult.success(bizDept);
    }
}
