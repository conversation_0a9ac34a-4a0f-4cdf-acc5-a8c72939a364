package cn.ray.sso.controller.auth;

import cn.ray.core.common.DateUtil;
import cn.ray.core.common.TreeHelper;
import cn.ray.core.result.MvcResult;
import cn.ray.sso.dao.entity.BaseModule;
import cn.ray.sso.dao.mapper.BaseModuleMapper;
import cn.ray.sso.manager.auth.MenuManager;
import cn.ray.sso.manager.auth.RoleUserManager;
import cn.ray.sso.token.SsoSecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/menu")
@Slf4j
public class MenuController {

    @Autowired
    private MenuManager menuManager;

    @Autowired
    private BaseModuleMapper baseModuleMapper;


    @GetMapping("/tree/{parentId}")
    public MvcResult<TreeHelper> getModuleTree(@PathVariable("parentId") Long parentId) {
        if (parentId == null) {
            log.error("父节点为空");
            return MvcResult.failure(-1001, "父节点为空");
        }
        TreeHelper treeHelper = menuManager.getModuleTree(parentId);
        return MvcResult.success(treeHelper);
    }
    @GetMapping("/{id:\\d+}")
    public MvcResult<BaseModule> getMenuById(@PathVariable("id") Long id){
        BaseModule menu = baseModuleMapper.selectByPrimaryKey(id);
        return MvcResult.success(menu);
    }
    @GetMapping("/{menuNo}")
    public MvcResult<BaseModule> getMenuByMenuNo(@PathVariable("menuNo") Integer menuNo){
        BaseModule menu = menuManager.getMenuByNo(menuNo);
        return MvcResult.success(menu);
    }

    @GetMapping("nav/{menuNo}")
    public MvcResult<List<BaseModule>> getNavByCode(@PathVariable("menuNo") Integer menuNo){
        List<BaseModule> result = menuManager.doGetNavByMenuNo(menuNo);
        return MvcResult.success(result);
    }


    @PostMapping
    public MvcResult addMenu(BaseModule menu){
        return saveMenu(menu);
    }

    @PutMapping
    public MvcResult saveMenu(BaseModule menu){
        BaseModule old = menuManager.getMenuByNo(menu.getMenuNo());
        boolean result = old == null || old.getId().equals(menu.getId());
        if(result){
            menu.setUpdateDate(DateUtil.now());
            menu.setUpdator(SsoSecurityUtils.getLoginUser());
            menuManager.saveMenu(menu);
            return MvcResult.success(menu);
        }
        return MvcResult.failure(-1001, "菜单编号已存在");

    }

    /**
     *
     * @return
     */
    @GetMapping("exists")
    public MvcResult exsitMenuNo(Long id, Integer menuNo) {
        BaseModule old = menuManager.getMenuByNo(menuNo);
        boolean result = old == null || !old.getId().equals(id);
        return MvcResult.success(result);
    }

    /**
     *删除菜单
     *
     */
    @DeleteMapping
    public MvcResult deleteMenu(@RequestParam String ids) {
        if (ids == null) {
            return MvcResult.failure("请选择数据进行删除");
        }
        menuManager.deleteMenu(ids);
        return MvcResult.success();
    }
}
