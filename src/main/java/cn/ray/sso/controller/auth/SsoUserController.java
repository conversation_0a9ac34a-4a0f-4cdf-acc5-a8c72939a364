package cn.ray.sso.controller.auth;

import java.util.List;

import cn.ray.core.common.DateUtil;
import cn.ray.core.common.exception.RuleException;
import cn.ray.core.result.MvcResult;
import cn.ray.sso.dao.entity.SsoUser;
import cn.ray.sso.dubbo.dto.auth.BaseModuleDto;
import cn.ray.sso.dubbo.dto.auth.SsoUserDto;
import cn.ray.sso.manager.auth.SsoUserManager;
import cn.ray.sso.token.SsoSecurityUtils;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;

@RestController()
@RequestMapping("/user")
public class SsoUserController {

    @Autowired
    private SsoUserManager ssoUserManager;

    /**
     * 获取登录用户列表信息
     * @param pageIndex
     * @param pageSize
     * @param ssoUser
     * @return
     */
    @GetMapping
    public MvcResult<PageInfo<SsoUserDto>> getUserPage(Integer pageIndex, Integer pageSize, SsoUser ssoUser) {
        if (pageIndex == null) {
            pageIndex = 0;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        PageInfo pageInfo = ssoUserManager.getUserPage(pageIndex, pageSize, ssoUser);
        return MvcResult.success(pageInfo);
    }

    /**
     * 保存用户信息
     * @param ssoUserDto
     * @return
     */
    @RequestMapping(method = {RequestMethod.POST, RequestMethod.PUT})
    public MvcResult saveUser(SsoUserDto ssoUserDto) {
        ssoUserDto.setUpdator(SsoSecurityUtils.getLoginUser());
        ssoUserDto.setUpdateDate(DateUtil.now());
        ssoUserManager.saveUser(ssoUserDto);
        return MvcResult.success();
    }

    @DeleteMapping
    public MvcResult delUser(String ids) {
        ssoUserManager.delUser(SsoSecurityUtils.getLoginUser(), JSONArray.parseArray(String.format("[%s]", ids)).toJavaList(Long.class));
        return MvcResult.success();
    }

    @PutMapping("/resetPwd")
    public MvcResult resetPwd(String ids) {
        ssoUserManager.resetPwd(SsoSecurityUtils.getLoginUser(), JSONArray.parseArray(ids).toJavaList(Long.class));
        return MvcResult.success();
    }

    @GetMapping("/auth")
    public MvcResult getUserAuth(String userNo, String timestamp, String sign) {
        List<BaseModuleDto> modules = ssoUserManager.getUserAuth(userNo);
        return MvcResult.success(modules);
    }

    @GetMapping("/login")
    public MvcResult getLoginUserInfo() {
        String userNo = SsoSecurityUtils.getLoginUser();
        SsoUserDto ssoUserDto = ssoUserManager.getSsoUser(userNo);
        return MvcResult.success(ssoUserDto);
    }
    @PostMapping("/updatePwd")
    public MvcResult updatePwd(String oldPass, String newPass) {
        String userNo = SsoSecurityUtils.getLoginUser();
        try {
            ssoUserManager.updatePwd(userNo, oldPass, newPass);
        } catch (RuleException e) {
            e.printStackTrace();
            return MvcResult.failure(e.getCode(), e.getMessage());
        }catch (Exception e) {
            e.printStackTrace();
            return MvcResult.failure(e.getMessage());
        }
        return MvcResult.success();
    }
}
