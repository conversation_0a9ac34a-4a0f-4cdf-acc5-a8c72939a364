package cn.ray.sso.controller.auth;


import cn.ray.core.common.DateUtil;
import cn.ray.core.common.TreeHelper;
import cn.ray.core.result.MvcResult;
import cn.ray.sso.dao.entity.BaseGroup;
import cn.ray.sso.dubbo.dto.auth.BaseGroupDto;
import cn.ray.sso.manager.auth.DeptManager;
import cn.ray.sso.manager.auth.GroupManager;
import cn.ray.sso.token.SsoSecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dept")
@Slf4j
public class GroupController {

    @Autowired
    private DeptManager deptManager;

    @Autowired
    private GroupManager groupManager;

    @GetMapping("/tree/{parentId}")
    public MvcResult<TreeHelper> getModuleTree(@PathVariable("parentId") Long parentId) {
        if (parentId == null) {
            log.error("父节点为空");
            return null;
        }
        TreeHelper treeHelper = groupManager.getGroupTree(parentId);
        return MvcResult.success(treeHelper);
    }

    /**
     * 新增  修改
     * @param dept
     * @return
     */
    @RequestMapping(method = {RequestMethod.POST, RequestMethod.PUT})
    public MvcResult addDept(BaseGroupDto dept){
        BaseGroup baseGroup = new BaseGroup();
        baseGroup.setId(dept.getId());
        baseGroup.setGroupName(dept.getDeptName());
        baseGroup.setGroupCode(dept.getDeptNo());
        baseGroup.setParentId(dept.getParentId());
        baseGroup.setUpdateDate(DateUtil.now());
        baseGroup.setUpdator(SsoSecurityUtils.getLoginUser());
        deptManager.saveDept(baseGroup);
        return MvcResult.success(baseGroup);
    }

    /**
     * 删除指定部门
     * @param id 部门Id
     * @return
     */
    @DeleteMapping
    public MvcResult deleteDept(@RequestParam("id") Long id){
        deptManager.deleteDept(id);
        return MvcResult.success();
    }
}
