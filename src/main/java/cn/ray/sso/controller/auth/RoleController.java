package cn.ray.sso.controller.auth;


import cn.ray.core.common.DateUtil;
import cn.ray.core.result.MvcResult;
import cn.ray.sso.dao.entity.BaseRole;
import cn.ray.sso.dao.entity.BaseRoleModule;
import cn.ray.sso.dubbo.dto.auth.BaseRoleModuleDto;
import cn.ray.sso.manager.auth.RoleManager;
import cn.ray.sso.manager.auth.RoleReMenuManager;
import cn.ray.sso.token.SsoSecurityUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * 角色管理控制器
 *
 * <AUTHOR>
 * @create 2018-03-2
 */
@RestController
@RequestMapping("role")
public class RoleController {

    @Autowired
    private RoleManager roleManager;

    @Autowired
    private RoleReMenuManager roleReMenuManager;

    /**
     * 查询用户信息
     * @return
     */
    @GetMapping
    public MvcResult<PageInfo> list( int pageIndex, int pageSize){
        Page page = roleManager.selectRoleList(pageIndex,pageSize);
        PageInfo pageInfo = new PageInfo(page);
        return MvcResult.success(pageInfo);
    }
    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping
    public MvcResult deleteRole(Long id){
        roleManager.deleteRole(id);
        return MvcResult.success();
    }

    /**
     * 根据编号查询role进入修改页面
     * @param id
     * @return
     */
    @GetMapping ("/{id:\\d+}")
    public MvcResult<BaseRole> getRoleById(@PathVariable("id") Long id){
        BaseRole baseRole= roleManager.selectRoleById(id);
        return MvcResult.success(baseRole);
    }

    /**
     *新增/修改功能
     * @param baseRole
     * @return
     */
    @RequestMapping(method = {RequestMethod.POST, RequestMethod.PUT})
    public MvcResult addRole(BaseRole baseRole){
        baseRole.setUpdateDate(DateUtil.now());
        baseRole.setUpdator(SsoSecurityUtils.getLoginUser());
        roleManager.saveRole(baseRole);
        return MvcResult.success();
    }

    /**
     * 新增角色菜单
     * @param roleId
     * @return
     */
    @PostMapping("menus")
    public MvcResult roleReMenu(Long roleId, String menuIds){
        String[] menuId = menuIds.split(",");
        roleReMenuManager.addRoleReMenu(roleId, menuId);
        return MvcResult.success();
    }

    /**
     * 根据role查询roleReMenu信息
     * @param roleId
     * @return
     */
    @GetMapping("menus")
    public MvcResult<List<BaseRoleModuleDto>> searchReMu(Long roleId){
        List<BaseRoleModule> roleModules= roleReMenuManager.selectMenuIdByRole(roleId);
        List<BaseRoleModuleDto> dtoList = new ArrayList<>();
        roleModules.forEach(baseRoleModule -> {
            BaseRoleModuleDto baseRoleModuleDto = new BaseRoleModuleDto();
            baseRoleModuleDto.setModuleId(baseRoleModule.getModuleId());
            baseRoleModuleDto.setRoleId(baseRoleModule.getRoleId());
            baseRoleModuleDto.setMenuId(baseRoleModule.getModuleId());
            baseRoleModuleDto.setId(baseRoleModule.getId());
            dtoList.add(baseRoleModuleDto);
        });
        return MvcResult.success(dtoList);
    }

}
