package cn.ray.sso.controller.auth;


import cn.ray.core.result.MvcResult;
import cn.ray.sso.dao.entity.BaseRole;
import cn.ray.sso.dao.entity.BaseUserRole;
import cn.ray.sso.manager.auth.RoleManager;
import cn.ray.sso.manager.auth.RoleUserManager;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Author: wyz
 * @Date: 2018/3/12 10:14
 * @Description: 这是角色人员管理视图控制层；
 */

@RestController
@RequestMapping("roleuser")
public class RoleUserController {

   @Autowired
    private RoleManager roleManager;
   @Autowired
    private RoleUserManager roleUserManager;

    /**
     * 分页查询角色
     * @return
     */
    @GetMapping("roles")
    public MvcResult<PageInfo> list( int pageIndex, int pageSize) {
        //查询用户列表
        Page<BaseRole> page = roleManager.selectRoleList( pageIndex + 1, pageSize);
        PageInfo pageInfo = new PageInfo(page);
        return MvcResult.success(pageInfo);
    }

    /**
     * 获取用户
     *
     * @param roleId
     * @return
     */
    @GetMapping("users/{roleId}")
    public MvcResult<List<BaseUserRole>> selectUserList(@PathVariable("roleId") Long roleId) {
        BaseUserRole user = new BaseUserRole();
        user.setRoleId(roleId);
        List<BaseUserRole> usersList = roleUserManager.selectRoleUserByEntity(user);
        return MvcResult.success(usersList);
    }
    /**
     * 获取角色
     *
     * @param userId
     * @return
     */
    @GetMapping("roles/{userId}")
    public MvcResult<List<BaseUserRole>> getRoleUserByUserId(@PathVariable("userId") Long userId) {
        BaseUserRole user = new BaseUserRole();
        user.setUserId(userId);
        List<BaseUserRole> usersList = roleUserManager.selectRoleUserByEntity(user);
        return MvcResult.success(usersList);
    }

    /**
     * 给角色分配人员
     *
     * @param roleId
     * @param ids
     * @return
     */
    @RequestMapping(method = {RequestMethod.POST, RequestMethod.PUT})
    public MvcResult save(Long roleId, String ids, Long userId) {
//        // ids 转换为list
        roleUserManager.saveByList(roleId, JSONArray.parseArray(ids).toArray(new Integer[0]),userId);
        return MvcResult.success();
    }
}
