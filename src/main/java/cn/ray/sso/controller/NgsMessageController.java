package cn.ray.sso.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import cn.ray.core.common.DateUtil;
import cn.ray.core.result.MvcResult;
import cn.ray.sso.dao.entity.NgsCenter;
import cn.ray.sso.dao.entity.NgsMessage;
import cn.ray.sso.dao.entity.SsoUser;
import cn.ray.sso.dao.mapper.NgsMessageMapper;
import cn.ray.sso.dao.mapper.SsoUserMapper;
import cn.ray.sso.token.SsoSecurityUtils;

/**
 * @Title NgsMessageController
 * <AUTHOR>
 * @Description //TODO
 * @Date 2024/8/20 23:03
 **/
@RequestMapping("/message")
@RestController
public class NgsMessageController {

    @Resource
    private NgsMessageMapper ngsMessageMapper;

    @Resource
    private SsoUserMapper ssoUserMapper;




    @GetMapping
    public MvcResult getList(String title, String state, Integer pageNum, Integer pageSize) {
        WeekendSqls<NgsMessage> weekendSqls = WeekendSqls.custom();
        weekendSqls.andEqualTo(NgsMessage::getDeleted, "0");
        if (StringUtils.isNotBlank(title)) {
            weekendSqls.andLike(NgsMessage::getTitle, "%" + title + "%");
        }
        if (state != null) {
            weekendSqls.andEqualTo(NgsMessage::getState, state);
        }
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<NgsMessage> messageList = ngsMessageMapper.selectByExample(Example.builder(NgsMessage.class).notSelect("content")
                .andWhere(weekendSqls).orderByDesc("id")
                .build());
        if (!messageList.isEmpty()) {
            Set<String> usernoList = messageList.stream().map(NgsMessage::getCreator).collect(Collectors.toSet());
            List<SsoUser> userList = ssoUserMapper.selectByExample(Example.builder(SsoUser.class).andWhere(
                    WeekendSqls.<SsoUser>custom().andIn(SsoUser::getUserNo, usernoList)
            ).build());
            Map<String, String> userMap = userList.stream().collect(Collectors.toMap(SsoUser::getUserNo, SsoUser::getNickName));
            messageList.stream().forEach(ngsMessage -> ngsMessage.setCreateUserName(userMap.getOrDefault(ngsMessage.getCreator(), ngsMessage.getCreator())));
        }
        return MvcResult.success(new PageInfo<>(page));
    }

    @GetMapping("/owner")
    public MvcResult getOwnerList(String title, String state, Integer pageNum, Integer pageSize) {
        String userno = SsoSecurityUtils.getLoginUser();
        WeekendSqls<NgsMessage> weekendSqls = WeekendSqls.custom();
        weekendSqls.andEqualTo(NgsMessage::getDeleted, "0")
                .andEqualTo(NgsMessage::getCreator, userno);
        if (StringUtils.isNotBlank(title)) {
            weekendSqls.andLike(NgsMessage::getTitle, "%" + title + "%");
        }
        if (state != null) {
            weekendSqls.andEqualTo(NgsMessage::getState, state);
        }
        Page page = PageHelper.startPage(pageNum, pageSize);
        ngsMessageMapper.selectByExample(Example.builder(NgsMessage.class).notSelect("content")
                .andWhere(weekendSqls).orderByDesc("id")
                .build());
        return MvcResult.success(new PageInfo<>(page));
    }

    @PostMapping
    public MvcResult saveMessage(NgsMessage ngsMessage) {
        ngsMessage.setUpdator(SsoSecurityUtils.getLoginUser());
        ngsMessage.setUpdateDate(DateUtil.getNowDateTime());
        if (ngsMessage.getId() == null) {
            ngsMessage.setCreator(ngsMessage.getUpdator());
            ngsMessage.setCreateDate(ngsMessage.getUpdateDate());
            ngsMessageMapper.insertSelective(ngsMessage);
        }else {
            ngsMessageMapper.updateByPrimaryKeySelective(ngsMessage);
        }
        return MvcResult.success();
    }

    @GetMapping("/{id}")
    public MvcResult getContent(@PathVariable("id") Long id) {
        if (id ==  null) {
            return MvcResult.success();
        }
        NgsMessage ngsMessage = ngsMessageMapper.selectByPrimaryKey(id);
        return MvcResult.success(ngsMessage);
    }

    @GetMapping("/ip")
    public MvcResult getLatLng() {
        String ip = "";//request.getHeader("X-Forwarded-For");

        return MvcResult.success(ip);
    }
    @DeleteMapping
    public MvcResult deleteMessage(Long id) {
        NgsMessage ngsMessage = new NgsMessage();
        ngsMessage.setId(id);
        ngsMessage.setDeleted("1");
        ngsMessageMapper.updateByPrimaryKeySelective(ngsMessage);
        return MvcResult.success();
    }

    @GetMapping("/main")
    public MvcResult getMainMessageList() {
        PageHelper.startPage(0, 10);
        String date = DateUtil.getNowDate();
        List<NgsMessage> ngsMessageList = ngsMessageMapper.selectByExample(Example.builder(NgsMessage.class)
                .notSelect("content")
                .andWhere(WeekendSqls.<NgsMessage>custom().andGreaterThan(NgsMessage::getMainExpireDate, date).andEqualTo(NgsMessage::getState, 1))
                .build());
        return MvcResult.success(ngsMessageList);
    }

    @PostMapping("/audit")
    public MvcResult audit(String ids, Integer state) {
        if (StringUtils.isBlank(ids) || state == null || state == 0) {
            return MvcResult.failure(-1001, "参数错误");
        }
        NgsMessage ngsMessage = new NgsMessage();
        ngsMessage.setState(state == 1 ? 1 : 2);
        List<String> idStrList = Arrays.asList(ids.split(","));
        List<Long> idList = idStrList.stream().map(id -> Long.valueOf(id)).collect(Collectors.toList());
        ngsMessageMapper.updateByExampleSelective(ngsMessage, Example.builder(NgsMessage.class).andWhere(
                WeekendSqls.<NgsMessage>custom().andIn(NgsMessage::getId, idList)
        ).build());
        return MvcResult.success();
    }

}
