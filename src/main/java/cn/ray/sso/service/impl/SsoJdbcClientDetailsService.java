package cn.ray.sso.service.impl;

import cn.ray.sso.manager.SysSsoManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.exceptions.InvalidClientException;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.client.JdbcClientDetailsService;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;


/**
 * <AUTHOR>
 */
@Component
public class SsoJdbcClientDetailsService extends JdbcClientDetailsService {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private SysSsoManager ssoUserManager;

    public SsoJdbcClientDetailsService(DataSource dataSource) {
        super(dataSource);
        this.setPasswordEncoder(passwordEncoder);
    }

    @Override
    public ClientDetails loadClientByClientId(String clientId) throws InvalidClientException {
        ClientDetails clientDetails = ssoUserManager.getClientByClientId(clientId);
        return clientDetails;
    }
}
