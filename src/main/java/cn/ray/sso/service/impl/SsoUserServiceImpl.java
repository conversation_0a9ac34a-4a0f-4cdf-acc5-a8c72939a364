package cn.ray.sso.service.impl;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import cn.ray.sso.dao.entity.SsoFreeUser;
import cn.ray.sso.dao.entity.SsoUser;
import cn.ray.sso.dao.mapper.SsoFreeUserMapper;
import cn.ray.sso.dao.mapper.SsoUserMapper;
import cn.ray.sso.dto.LoginUserDto;

@Component
public class SsoUserServiceImpl implements UserDetailsService {

    @Autowired
    private SsoUserMapper ssoUserMapper;

    @Autowired
    private SsoFreeUserMapper ssoFreeUserMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        if (StringUtils.isBlank(username)) {
            return null;
        }
        boolean freeFlag = StringUtils.endsWith(username,"#free");
        if (freeFlag) {
            Example example = Example.builder(SsoUser.class).andWhere(
                    WeekendSqls.<SsoFreeUser>custom().andEqualTo(SsoFreeUser::getUserNo, username.substring(0, username.length() - 5))
            ).build();
            SsoFreeUser ssoFreeUser = ssoFreeUserMapper.selectOneByExample(example);
            return transfar(ssoFreeUser);
        }else {
            Example example = Example.builder(SsoUser.class).andWhere(
                    WeekendSqls.<SsoUser>custom().andEqualTo(SsoUser::getUserNo, username)
                            .andEqualTo(SsoUser::getDeleted, false)
                            .andEqualTo(SsoUser::getState, 1)
            ).build();
            SsoUser ssoUser = ssoUserMapper.selectOneByExample(example);
            return transfar(ssoUser);
        }
    }

    public UserDetails transfar(SsoUser ssoUser) {
        if (ssoUser == null) {
            return null;
        }
        LoginUserDto loginUser = new LoginUserDto();
        loginUser.setNickname(ssoUser.getNickName());
        loginUser.setUsername(ssoUser.getUserNo());
        loginUser.setPassword(ssoUser.getPasswd());
        return loginUser;
    }
    public UserDetails transfar(SsoFreeUser ssoUser) {
        if (ssoUser == null) {
            return null;
        }
        LoginUserDto loginUser = new LoginUserDto();
        loginUser.setNickname(ssoUser.getNickName());
        loginUser.setUsername(ssoUser.getUserNo());
        loginUser.setPassword(new BCryptPasswordEncoder().encode("free"));
        return loginUser;
    }
}
