package cn.ray.sso.enumeration;

import cn.ray.core.common.constant.IDefinedError;

/**
 * 验证相关的错误信息
 * 1000开头
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-02-07 下午4:31
 */
public enum ValidateErrors implements IDefinedError {
    /**
     * -1001:API_ID错误
     */
    INVALID_API_ID(-1001, "API_ID错误"),
    /**
     * -1002:ACCESS_TOKEN错误
     */
    INVALID_ACCESS_TOKEN(-1002, "ACCESS_TOKEN错误"),
    /**
     * -1003:您没有该接口操作权限
     */
    INVALID_INTERFACE_AUTH(-1003,"您没有该接口操作权限"),
    /**
     * -1004:参数至少有一个为空
     */
    PARAMETERS_IS_NULL(-1004,"参数至少有一个为空"),
    /**
     * -1005:系统繁忙
     */
    SYSTEM_BUZY(-1005,"系统繁忙"),
    /**
     * -1006:参数不正确,不符合系统要求
     */
    INVALID_PARAMETERS(-1006,"参数不正确,不符合系统要求"),
    /**
     * -1007:操作权限不足
     */
    AUTHORIZE_NOT_FOUND(-1007,"操作权限不足"),
    /**
     * -1999:远程接口调用失败
     */
    CALL_REMOTE_INTERFACE_FAIL(-1999,"远程接口调用失败")
    ;

    ValidateErrors(Integer code, String message){
        this.code = code;
        this.message = message;
    }

    private Integer code;

    private String message;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public String toString() {
        return getCode() + ":" + getMessage();
    }

    @Override
    public IDefinedError setMessage(String message) {
        this.message = message;
        return this;
    }
}
