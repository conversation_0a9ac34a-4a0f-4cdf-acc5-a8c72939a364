/**
 *
 */
package cn.ray.sso;

import cn.ray.core.common.properties.SsoProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * <AUTHOR>
 *
 */
@SpringBootApplication(scanBasePackages = "cn.ray")
//@MapperScan("cn.ray.sso.dao")
@EnableConfigurationProperties(SsoProperties.class)
@EnableScheduling
public class BossServerApplication {

	public static void main(String[] args) {
		SpringApplication.run(BossServerApplication.class, args);
	}

	@Bean
	public RestTemplate restTemplate() {
		return new RestTemplate();
	}
}
