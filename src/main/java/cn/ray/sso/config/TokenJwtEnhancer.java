/**
 *
 */
package cn.ray.sso.config;

import cn.ray.sso.dto.LoginUserDto;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class TokenJwtEnhancer implements TokenEnhancer {

	@Override
	public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
		Map<String, Object> info = new HashMap<>();
		Object principal = authentication.getPrincipal();
		if (principal instanceof LoginUserDto) {
			LoginUserDto user = (LoginUserDto) principal;
			info.put("username",user.getUsername());
			info.put("nickname",user.getNickname());
		} else {
			info.put("username",principal);
			info.put("nickname",principal);
		}
		((DefaultOAuth2AccessToken)accessToken).setAdditionalInformation(info);
		return accessToken;
	}

}
