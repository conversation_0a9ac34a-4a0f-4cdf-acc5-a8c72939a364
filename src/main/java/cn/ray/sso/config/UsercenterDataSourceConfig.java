package cn.ray.sso.config;

import javax.sql.DataSource;

import cn.ray.core.jdbc.autoconfigure.DataSourceHolder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * 用户中心session构造器
 */
@Configuration
@MapperScan(basePackages = "cn.ray.sso.usercenter.dao", sqlSessionFactoryRef = "usercenterSqlSessionFactory")
public class UsercenterDataSourceConfig {

    @Autowired
    private DataSourceHolder dataSourceHolder;

    @Bean(name = "usercenterSqlSessionFactory")
    public SqlSessionFactory clusterSqlSessionFactory()
            throws Exception {
        DataSource clusterDataSource = dataSourceHolder.getDataSource("usercenter");
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(clusterDataSource);
        return sessionFactory.getObject();
    }

}
