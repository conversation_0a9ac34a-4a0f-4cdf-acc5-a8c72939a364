/**
 *
 */
package cn.ray.sso.config;

import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.stereotype.Component;

/**
 * 权限过滤器
 *
 * <AUTHOR>
 *
 */
@Component
@Order(Integer.MIN_VALUE)
public class OauthConfigProvider {

	public boolean config(ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry config) {
		config.antMatchers(HttpMethod.GET,
				"/oauth/*",
				"/login",
				"/center",
				"/center/message"
		).permitAll();
		return false;
	}

}
