package cn.ray.sso.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;

/**
 *
 */
@Configuration
@EnableResourceServer
public class ResourceServerConfig extends ResourceServerConfigurerAdapter {

    @Autowired
    private FormAuthenticationConfig formAuthenticationConfig;
    @Override
    public void configure(HttpSecurity http) throws Exception {
        formAuthenticationConfig.configure(http);
        http
            .authorizeRequests()
                .mvcMatchers("/oauth/**", "/weixin/**", "/ip").permitAll()
                .mvcMatchers(HttpMethod.GET, "/center","/center/project/**","/center/type", "/message/**", "/ip").permitAll()
                .anyRequest().authenticated()
                .and()
                .csrf()
                .disable();
    }

}
