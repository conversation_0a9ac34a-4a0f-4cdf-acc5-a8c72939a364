/**
 *
 */
package cn.ray.sso.token;

import cn.ray.core.common.properties.SsoProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;


/**
 * <AUTHOR>
 */
@Configuration
public class TokenStoreConfig {

	@Autowired
	private SsoProperties ssoProperties;

	/**
	 * 使用jwt时的配置，默认生效
	 *
	 * <AUTHOR>
	 *
	 */
	@Configuration
	@ConditionalOnProperty(prefix = "ray.security.oauth2", name = "tokenStore", havingValue = "jwt", matchIfMissing = true)
	public class JwtConfig {

		@Autowired
		private JwtAccessTokenConverter jwtAccessTokenConverter;

		/**
		 * @return
		 */
		@Bean
		public TokenStore jwtTokenStore() {
			return new JwtTokenStore(jwtAccessTokenConverter);
		}

	}

	/**
	 * @return
	 */
	@Bean
	public JwtAccessTokenConverter jwtAccessTokenConverter(){
		JwtAccessTokenConverter converter = new JwtAccessTokenConverter();
		converter.setSigningKey(ssoProperties.getJwtSigningKey());
		return converter;
	}


}
