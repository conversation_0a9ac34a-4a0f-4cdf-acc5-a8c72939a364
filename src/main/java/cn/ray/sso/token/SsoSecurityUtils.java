package cn.ray.sso.token;


import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 权限相关操作
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @create 2018-02-27 下午5:23
 */
@Component
public class SsoSecurityUtils {
    /**
     * 获取登录信息
     * @return
     */
    public static String getLoginUser(){
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof OAuth2Authentication) {
            OAuth2Authentication oAuth2Authentication = (OAuth2Authentication)authentication;
            Authentication info = oAuth2Authentication.getUserAuthentication();
            if (info != null) {
                Object details = info.getDetails();
                if (details instanceof Map) {
                    Object username = ((Map)details).get("name");
                    return username.toString();
                }else {
                    return authentication.getPrincipal().toString();
                }
            }else {
                Object auth = authentication.getPrincipal();
                return auth.toString();
            }
        } else {
            Object auth = authentication == null ? null : authentication.getPrincipal();
            if (auth instanceof UserDetails){
                UserDetails userDetails = (UserDetails)auth;
                return userDetails.getUsername();
            }else if(auth!=null){
                return auth.toString();
            }
        }
        return null;
    }
}
