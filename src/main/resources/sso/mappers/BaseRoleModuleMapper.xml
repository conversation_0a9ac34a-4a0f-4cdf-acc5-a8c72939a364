<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.BaseRoleModuleMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.BaseRoleModule">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="module_id" jdbcType="INTEGER" property="moduleId" />
  </resultMap>

  <insert id="batchAddRoleModule" parameterType="java.util.List">
    INSERT INTO base_role_module (role_id,module_id)
    VALUES
    <foreach collection="items" item="item" index="index" separator=",">
      (#{item.roleId},#{item.moduleId})
    </foreach>
  </insert>
</mapper>
