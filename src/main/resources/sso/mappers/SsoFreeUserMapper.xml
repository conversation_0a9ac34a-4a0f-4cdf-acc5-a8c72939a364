<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.SsoFreeUserMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.SsoFreeUser">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="user_no" jdbcType="VARCHAR" property="userNo" />
    <result column="from_type" jdbcType="VARCHAR" property="fromType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>
