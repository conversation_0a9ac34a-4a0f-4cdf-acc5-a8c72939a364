<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.SsoClientMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.SsoClient">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="appid" jdbcType="VARCHAR" property="appid" />
    <result column="client_name" jdbcType="VARCHAR" property="clientName" />
    <result column="secret" jdbcType="VARCHAR" property="secret" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="state" jdbcType="CHAR" property="state" />
    <result column="deleted" jdbcType="CHAR" property="deleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="authorized_grant_types" jdbcType="VARCHAR" property="authorizedGrantTypes" />
    <result column="scope" jdbcType="VARCHAR" property="scope" />
    <result column="pass_client" jdbcType="CHAR" property="passClient" />
  </resultMap>
</mapper>