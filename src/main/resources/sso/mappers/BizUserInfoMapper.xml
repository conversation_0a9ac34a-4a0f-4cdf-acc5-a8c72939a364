<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.BizUserInfoMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.BizUserInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
  </resultMap>

  <select id="getDeptUserCount" resultType="cn.ray.sso.dao.DeptUserCount">
    select dept_id deptId, count(1) userCount from biz_user_info
    group by dept_id
  </select>
</mapper>
