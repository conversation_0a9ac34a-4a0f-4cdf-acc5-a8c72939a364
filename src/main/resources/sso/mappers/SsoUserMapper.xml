<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.SsoUserMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.SsoUser">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="user_no" jdbcType="VARCHAR" property="userNo" />
    <result column="passwd" jdbcType="VARCHAR" property="passwd" />
    <result column="state" jdbcType="CHAR" property="state" />
    <result column="deleted" jdbcType="CHAR" property="deleted" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
  </resultMap>
</mapper>