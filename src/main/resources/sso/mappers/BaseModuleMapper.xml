<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.BaseModuleMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.BaseModule">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="ID" jdbcType="INTEGER" property="id" />
    <result column="MENU_NAME" jdbcType="VARCHAR" property="menuName" />
    <result column="URL" jdbcType="VARCHAR" property="url" />
    <result column="PARENT_ID" jdbcType="INTEGER" property="parentId" />
    <result column="ORDER_NUM" jdbcType="INTEGER" property="orderNum" />
    <result column="STATE" jdbcType="CHAR" property="state" />
    <result column="DELETED" jdbcType="CHAR" property="deleted" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="UPDATOR" jdbcType="VARCHAR" property="updator" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="MENU_TYPE" jdbcType="CHAR" property="menuType" />
    <result column="MENU_NO" jdbcType="INTEGER" property="menuNo" />
    <result column="HAS_LOCK" jdbcType="CHAR" property="hasLock" />
    <result column="ICON" jdbcType="VARCHAR" property="icon" />
    <result column="METHOD" jdbcType="VARCHAR" property="method" />
  </resultMap>
</mapper>