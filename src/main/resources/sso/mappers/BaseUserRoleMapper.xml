<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ray.sso.dao.mapper.BaseUserRoleMapper">
  <resultMap id="BaseResultMap" type="cn.ray.sso.dao.entity.BaseUserRole">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
  </resultMap>

  <insert id="insertListByRoleId">
    insert into base_user_role( user_id, role_id) values
    <foreach collection="list" item="userId" index="index" separator=",">
      (#{userId},#{roleId})
    </foreach>
  </insert>

  <insert id="insertListByUserId">
    insert into base_user_role( user_id, role_id) values
    <foreach collection="list" item="roleId" index="index" separator=",">
      (#{userId},#{roleId})
    </foreach>
  </insert>

</mapper>
