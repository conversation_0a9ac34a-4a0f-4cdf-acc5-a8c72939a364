mybatis:
  mapper-locations: classpath:sso/mappers/*.xml
server:
  port: 7801
spring:
  session:
    store-type: none
  datasource:
    name: test
    url: jdbc:mysql://${DB_HOST:**********:2306/nearest_gov_services}?useSSL=true&characterEncoding=utf8&serverTimezone=UTC
    username: ${DB_USER:root}
    password: ${DB_PWD:Msql_145.1029}
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
    filters: stat,wall,slf4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # 合并多个DruidDataSource的监控数据
    useGlobalDataSourceStat: true
    tomcat:
      max-active: 100
      initial-size: 5
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: true
      test-on-return: true
    dbcp2:
      max-open-prepared-statements: 20
security:
  oauth2:
    client:
      auto-approve-scopes: all
logging:
  level:
    cn.ray: debug
ray:
  security:
    oauth2:
      tokenStore: ${SECURITY_TOKEN_KEY:jwt}
    root: ${ROOT_USERNAME:admin}
    pwd-model: ${PASSWORD_MODEL:right:6}
file:
  prefix: /images/
  path: /images/
