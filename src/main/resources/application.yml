mybatis:
  mapper-locations: classpath:sso/mappers/*.xml
server:
  port: 7901
spring:
  session:
    store-type: none
  datasource:
    name: test
    url: jdbc:mysql://${DB_HOST:127.0.0.1:3306/nearest_gov_services}?useSSL=true&characterEncoding=utf8&serverTimezone=UTC
    username: ${DB_USER:root}
    password: ${DB_PWD:root}
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
    filters: stat,wall,slf4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # 合并多个DruidDataSource的监控数据
    useGlobalDataSourceStat: true
    stat-view-servlet:
      enabled: true
      url-pattern: /druid/*
    tomcat:
      max-active: 100
      initial-size: 5
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: true
      test-on-return: true
    dbcp2:
      max-open-prepared-statements: 20
  servlet:
    multipart:
      max-file-size: 3MB
      max-request-size: 3MB
security:
  oauth2:
    client:
      auto-approve-scopes: all
logging:
  level:
    cn.ray: debug
ray:
  security:
    oauth2:
      tokenStore: ${SECURITY_TOKEN_KEY:jwt}
    root: ${ROOT_USERNAME:admin}
    pwd-model: ${PASSWORD_MODEL:right:6}
  datasource:
    routes:
      usercenter:
        name: usercenter
        url: ****************************************************************************
        username: root
        password: root123654
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        # 合并多个DruidDataSource的监控数据
        useGlobalDataSourceStat: true
      ams:
        name: ams
        url: ********************************************************************************************************************
        username: root
        password: 123!@#
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        # 合并多个DruidDataSource的监控数据
        useGlobalDataSourceStat: true
file:
  path: /Users/<USER>/Documents/ideawork/ray/nearest_gov_services/upload/
rsa:
  public: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMHfy1ary/02P0N+BjkF5othhL7sgDyLqI/FgV/Dg70XFOvR9cJpV9O2r6cCK1fAL64WiwARz2fMlCsbYMT5E7sCAwEAAQ==
  private: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAwd/LVqvL/TY/Q34GOQXmi2GEvuyAPIuoj8WBX8ODvRcU69H1wmlX07avpwIrV8AvrhaLABHPZ8yUKxtgxPkTuwIDAQABAkBuUBJ5DGOTfkxc8NZFl+/oGuLXZ+ZHjQeqVq0Yejz5V60h2lY25Kc5zcO0e+JB4GSWUERPoRIlPRctWsyqMwrxAiEA50LpagFkPFcpZdgJi+DywhsmGUBu4/jfAWtHgAMpplUCIQDWnQkPAafn4p2W5ufhdALAxjc7qnt87ivWQf2FhtBBzwIge8UI7W4POmgfxN0Jxu4+nnovOsaMUsCIgdbrrmyC5v0CIGMWwF74YcDz5dUNqdRzju3Y1xmqXjfRc9YKQ/bJxxALAiEAoa5FzyDouH7w6rwN5RVACw2di9oJGUXLR5Nvf/qkEXI=

