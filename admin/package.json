{"name": "boss", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@stomp/stompjs": "^5.4.4", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^0.19.2", "core-js": "^3.6.4", "dayjs": "^1.11.13", "echarts": "^4.8.0", "js-cookie": "^2.2.1", "less": "^3.11.1", "less-loader": "^5.0.0", "node-sass": "^4.14.1", "qrcodejs2": "0.0.2", "sass-loader": "^8.0.2", "sockjs-client": "^1.4.0", "view-design": "^4.3.2", "vue": "^2.6.11", "vue-pdf": "^4.3.0", "vue-router": "^3.1.5", "vuedraggable": "^2.24.1", "vuex": "^3.1.2", "wangeditor": "^3.1.1", "xlsx": "^0.16.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.2.0", "@vue/cli-plugin-eslint": "~4.2.0", "@vue/cli-plugin-router": "^4.2.3", "@vue/cli-plugin-vuex": "^4.2.3", "@vue/cli-service": "~4.2.0", "babel-eslint": "^10.0.3", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.1.2", "node-sass": "^4.14.1", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions"]}