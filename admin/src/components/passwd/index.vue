<template>
    <Form :model="formItem" label-colon :label-width="80" :rules="formValidate">
        <FormItem label="用户姓名">
            <span>{{formItem.username}}</span>
        </FormItem>
        <FormItem prop="password" label="旧密码">
            <Input type="password" v-model="formItem.password" placeholder="Password">
                <Icon type="ios-lock-outline" slot="prepend"></Icon>
            </Input>
        </FormItem>
        <FormItem prop="password">
            <Input type="password" v-model="formItem.password" placeholder="Password">
                <Icon type="ios-lock-outline" slot="prepend"></Icon>
            </Input>
        </FormItem>
        <FormItem prop="newPassword" label="新密码">
            <Input type="password" v-model="formItem.newPassword" placeholder="Password">
                <Icon type="ios-lock-outline" slot="prepend"></Icon>
            </Input>
        </FormItem>
        <FormItem prop="password" label="确认密码">
            <Input type="password" v-model="formItem.confirmNewPassword" placeholder="Password">
                <Icon type="ios-lock-outline" slot="prepend"></Icon>
            </Input>
        </FormItem>
        <FormItem>
            <Button type="primary">确认修改</Button>
            <Button style="margin-left: 8px">取消</Button>
        </FormItem>
    </Form>
</template>
<script>
  export default {
    name: 'changePassword',
    data () {
      return {
        formItem: {
          username: '',
          password: '',
          newPassword: '',
          confirmNewPassword: ''
        },
        formValidate:{
          password: [
            {required: true, message: '旧密码不能为空', trigger: 'blur'},
            {validator:(rule,value,callback) => {
              value === 'test';
            }, trigger: 'blur'}
          ],
          newPassword: [
            {required: true, message: '新密码不能为空', trigger: 'blur'},
            { validator: validatePass, trigger: 'blur' },
            { message: '密码长度只能在6到20位之间', trigger: 'blur',min: 6, max:20}
          ],
          confirmNewPassword: [
            {required: true, message: '确认密码不能为空', trigger: 'blur'},
            { validator: validatePassCheck, trigger: 'blur' }
          ],
        }
      }
    }
  }
</script>
