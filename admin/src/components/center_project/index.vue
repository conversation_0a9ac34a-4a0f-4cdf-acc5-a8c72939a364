<template>
  <div>
    <Modal v-model="value" width="60" title="事项列表" >
      <Table :columns="columns" :data="data" height="520" ></Table>
      <div slot="footer">
        <Button type="primary" @click="openAdd">新增</Button>
        <Button @click="close">关闭</Button>
      </div>
    </Modal>
    <Modal v-model="edit_modal" width="40" title="编辑事项" class-name="vertical-center-modal">
      <Form :label-width="100">
        <FormItem label="排序">
          <InputNumber :min="1" :step="1" v-model="centerProject.sortNum"></InputNumber>
        </FormItem>
        <FormItem label="事项名称">
          <Input v-model="centerProject.projectName" placeholder="请输入事项名称"></Input>
        </FormItem>
        <FormItem label="事项描述">
          <Input v-model="centerProject.projectDesc" type="textarea" :rows="4" placeholder="请输入事项描述"></Input>
        </FormItem>
        <FormItem label="在线办理地址">
          <Input v-model="centerProject.onlineUrl" type="textarea" :rows="2"  placeholder="请输入事项在线办理地址"></Input>
          <a style="float: right; display: inline; margin-left: 10px" target="_blank" href="/pdf-info/online-url.pdf">如何获取地址？</a>
          <a style="float: right; display: inline" target="_blank" href="https://www.zjzwfw.gov.cn/zjservice-fe/#/home?siteCode=330109000000">浙江省政务服务网</a>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="save">保存</Button>
        <Button @click="edit_modal = false">关闭</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { centerService} from '@/api';
  export default {
    name: 'CenterProject',
    emits: ['close'],
    props: {
      value: {
        type: Boolean,
        default: false
      },
      centerId: {
        type: Number,
        default: null
      },
    },
    data () {
      return {
        onlineUrlDialog: false,
        centerProject: {
        },
        edit_modal: false,
        columns: [
          {
            title: '排序',
            key: 'sortNum',
            width: 80,
          },
          {
            title: '事项名称',
            key: 'projectName',
            width: 260,
            ellipsis: true,
            tooltip: true
          },
          {
            title: '事项描述',
            key: 'projectDesc',
            ellipsis: true,
            tooltip: true
          },
          {
            title: '操作',
            key: 'age',
            width: 140,
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'success',
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px'
                  },
                  on: {
                    click: () => {
                      this.openEdit(params.row)
                    }
                  }
                }, '修改'),
                h('Button', {
                  props: {
                    type: 'success',
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px'
                  },
                  on: {
                    click: () => {
                      this.del(params.row)
                    }
                  }
                }, '删除')]);
            }
          }
        ],
        data: []
      }
    },
    methods: {
      openEdit(data) {
        this.centerProject = {...data}
        this.edit_modal = true;
      },
      del(data) {
        this.$Modal.confirm({
          title: "删除提醒",
          content: "确定要删除此事项吗?",
          onOk: () => {
            centerService.deleteProject(data.id).then(res => {
              if (res.success) {
                this.$Message.success("删除事项成功")
                this.getCenterProjectList();
              }else {
                this.$Message.error("删除事项失败，" + res.code + ":" + res.msg)
              }
            }).catch(error => {
              this.$Message.error("删除事项失败，" + error.code + ":" + error.msg)
            })
          }
        })
      },
      save() {
        if (!this.centerProject.sortNum) {
          return this.$Message.error("排序不能为空，")
        }
        if (!this.centerProject.projectName || !this.centerProject.projectName.trim()) {
          return this.$Message.error("事项名称不能为空，")
        }
        centerService.saveProject(this.centerProject).then(res => {
          if (res.success) {
              this.$Message.success("保存成功")
              this.edit_modal = false;
              this.getCenterProjectList();
          }else {
            this.$Message.error("获取事项数据失败，" + res.code + ":" + res.msg)
          }
        }).catch(error => {
          this.$Message.error("获取事项数据失败，" + error.code + ":" + error.msg)
        })
      },
      openAdd() {
        this.centerProject = {
          sortNum: 1,
          centerId: this.centerId
        }
        this.edit_modal = true;
      },
      getCenterProjectList(centerId) {
        centerService.getPojectList(centerId || this.centerId).then(res => {
          if (res.success) {
            this.data = res.data;
          }else {
            this.$Message.error("获取事项数据失败，" + res.code + ":" + res.msg)
          }
        }).catch(error => {
          this.$Message.error("获取事项数据失败，" + error.code + ":" + error.msg)
        })
      },
      close() {
        this.value = false
        this.$emit('close', false)
      }
    },
    watch: {
      centerId(nid, oid) {
        if (nid != oid) {
          if (!nid) {
            this.data = [];
          }else {
            this.getCenterProjectList(nid);
          }
        }
      }
    }
  }
</script>
<style lang="less">
.vertical-center-modal{
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal{
    top: 0;
  }
  .ivu-table-cell {
    white-space: nowrap;
  }
}
</style>
