<template>
  <div style="height:100%">

    <Row style="height: 100%" >
      <Col span="5" style="height: 100%" >
        <Card style="padding-bottom: 70px;height: 100%" >
          <p slot="title">
            <Icon type="android-options"></Icon>
            {{title}}列表
          </p>
          <Tree :data="centerDataList" :render="renderContent" :xpand-node="true" :multiple="false" empty-text="没有数据" ></Tree>
        </Card>
      </Col>
      <Col span="19" style="height: 100%">
        <Card  style="height: 100%;" >
          <p slot="title">
            <Icon type="ios-list-outline"></Icon>
            {{title}}详情
          </p>
          <Spin fix style="z-index: 999" v-show="editType == 0"><span style="font-size: 18px">请点击左侧列表选择服务中心</span></Spin>
          <Form ref="adminForm" :model="curCenter" :label-width="110" >
            <Row>
              <Col span="12">
                <FormItem label="中心类型" prop="centerType">
                  <Select v-model="curCenter.centerType" placeholder="请选择服务中心类型...">
                    <Option v-for="item in centerTypes" :value="item.cod" :key="item.cod">{{ item.title }}</Option>
                  </Select>
                </FormItem>
                <FormItem label="所属镇街" prop="centerType">
                  <Select v-model="curCenter.townId" placeholder="请选择服务所属镇街...">
                    <Option v-for="item in groups" :value="item.id" :key="item.id">{{ item.title }}</Option>
                  </Select>
                </FormItem>
                <FormItem label="中心名称" prop="centerName">
                  <Input v-model="curCenter.centerName" placeholder="请输入服务中心名称..." :clearable="true">
                    <template #append>
                      <Button @click="querMapFromAddr(curCenter.centerName)">反解析</Button>
                    </template>
                  </Input>
                </FormItem>
              </Col>
              <Col span="12">
                <Row>
                  <Col span="12">
                    <FormItem label="大厅布局图" v-if="centerTypeMap[curCenter.centerType] && centerTypeMap[curCenter.centerType].expandData && centerTypeMap[curCenter.centerType].expandData.layout">
                      <Upload
                          ref="upload"
                          :show-upload-list="false"
                          :on-success="handleSuccess0"
                          :headers="{'Authorization': 'Bearer ' + userToken}"
                          :format="['jpg','jpeg','png']"
                          :max-size="2048"
                          :on-format-error="handleFormatError"
                          :on-exceeded-size="handleMaxSize"
                          type="drag"
                          name="file"
                          :action="uploadUrl"
                          style="display: inline-block;width:100px;">
                        <div style="width: 100px;height:100px;line-height: 100px;">
                          <img :src="imgPrefix + curCenter.layout" v-if="curCenter.layout" width="100" height="100" style="object-fit: cover;"/>
                          <Icon type="ios-camera" size="20" v-else></Icon>
                        </div>
                      </Upload>
                    </FormItem>
                  </Col>
                  <Col span="12">
                    <FormItem label="外部照片" v-if="centerTypeMap[curCenter.centerType] && centerTypeMap[curCenter.centerType].expandData && centerTypeMap[curCenter.centerType].expandData.img">
                      <Upload
                          ref="upload"
                          :show-upload-list="false"
                          :on-success="handleSuccess1"
                          :headers="{'Authorization': 'Bearer ' + userToken}"
                          :format="['jpg','jpeg','png']"
                          :max-size="2048"
                          :on-format-error="handleFormatError"
                          :on-exceeded-size="handleMaxSize"
                          type="drag"
                          name="file"
                          :action="uploadUrl"
                          style="display: inline-block;width:100px;">
                        <div style="width: 100px;height:100px;line-height: 100px;">
                          <img :src="imgPrefix + curCenter.img" v-if="curCenter.img" width="100" height="100" style="object-fit: cover;"/>
                          <Icon type="ios-camera" size="20" v-else></Icon>
                        </div>
                      </Upload>
                    </FormItem>
                  </Col>
                </Row>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="工作时间" prop="tel">
                  <Input v-model="curCenter.workTime" placeholder="请输入工作时间..." :clearable="true"></Input>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="联系电话" prop="tel" v-if="centerTypeMap[curCenter.centerType] && centerTypeMap[curCenter.centerType].expandData && centerTypeMap[curCenter.centerType].expandData.tel">
                  <Input v-model="curCenter.tel" placeholder="请输入联系电话..." :clearable="true"></Input>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="具体位置" prop="tel">
                  <Input v-model="curCenter.addr">
                    <template #append>
                      <Button @click="querMapFromAddr(curCenter.addr)">反解析</Button>
                    </template>
                  </Input>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="中心坐标" prop="tel">
                  <Input v-if="editLatlng" v-model="curCenter.latlng"></Input>
                  <div v-else><span style="padding-right:10px;font-size: 18px;line-height: 18px;">{{curCenter.latlng}}</span><Button @click="showChangeLatlng">修改</Button></div>
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="中心位置">
<!--                  <p slot="label">-->
<!--                    中心位置-->
<!--                  </p>-->
                  <!--                   -->
                  <Row>
                    <Col span="8"> <div style="color: red; font-size: 12px; font-weight: bold">使用鼠标点击地图选择中心所在位置</div></Col>
                    <Col span="16">
                      <AutoComplete
                          @on-change="queryMapInfo"
                          @on-clear="queryMapInfo"
                          icon="ios-search"
                          placement="top"
                          transfer
                          clearable
                          v-model="queryMapParam"
                          placeholder="请输入关键字搜索">
                        <CellGroup v-if="queryMapInfos.length" @on-click="showInMap">
                          <Cell :name="itemIndex" v-for="(item, itemIndex) in queryMapInfos" :title="item.label" :extra="item.label == item.address ? '' : item.address" >
                          </Cell>
                        </CellGroup>
                        <div v-else style="width: 100%; text-align: center; height: 40px; padding-top: 10px">无数据</div>
                      </AutoComplete>
                    </Col>
                  </Row>
                  <div style="width: 100%; height: 450px; margin-top: 5px" id="map_center_div_id"></div>
                  定位位置：{{mapMarkerAddr}}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="24" align="middle">
                <Button type="primary" @click="showProjectList" :disabled="editType == 0" style="margin-right: 20px"
                        v-if="auth.save && centerTypeMap[curCenter.centerType] && centerTypeMap[curCenter.centerType].expandData && centerTypeMap[curCenter.centerType].expandData.project">事项列表</Button>
                <Button type="primary" @click="saveAdmin" v-if="auth.save" :disabled="editType == 0" >保存</Button>
              </Col>
            </Row>
            <div style="height: 40px"></div>
          </Form>
        </Card>
      </Col>
    </Row>
    <center-project @close="projectModal = false" v-model="projectModal" :centerId="curCenter.id" ></center-project>
  </div>
</template>
<style>
</style>
<script>
    import CenterProject from "_c/center_project/index.vue";
    import {centerService, deptService} from '@/api';
    import town_marker_img from "@/assets/town-marker.svg"
    import LoginUtil from "@/libs/LoginUtil";

    import config from "@/config";
    export default {
        name: 'dept',
        mounted () {
          this.getTypes();
          this.getGroups();
          this.getCenterList();
          this.initMap();
        },
        components: {
          CenterProject
        },
        data () {
            const deptNameValidate = (role,value,callback)=>{
                value=value.trim()?value.trim():value;
                if(value.length<1){
                    callback(new Error("请输入" + this.title + "名称"));
                }
                callback();
            };
            const deptNoValidate = (role,value,callback)=>{
                value=value.trim()?value.trim():value;
                if(value.length<1){
                    callback(new Error(this.title + "编码不允许重复"));
                }
                callback();
            };
            return {
                editLatlng: false,
                queryMapParam: "",
                projectModal: false,
                auth: {
                    save: true,
                    del: true
                },
                title: '服务中心',
                deptTypes: [],
                centerDataList: [],
                curCenter:{},
                deptValidate:{
                    deptName:[
                        { validator: deptNameValidate, trigger: 'change' },
                    ],
                    deptNo:[
                        { validator: deptNoValidate, trigger: 'change' },
                    ],
                },
                editType: 0,
                deptTreeSetting: {
                    callback: {
                        beforeClick: this.treeSelect
                    }
                },
                saveButton: false,
                parentNodeMap: {},
              map: null,
              marker: null,
              mapMarkerAddr: null,
              geocode: null,
              userToken: LoginUtil.getToken(),
              uploadUrl: config.rest.file.upload,
              imgPrefix: "/ngs-upload",
              groups: [],
              centerTypes: [],
              centerTypeMap: {},
              queryMapInfos: [],
            }
        },
        watch: {
          "curCenter.latlng"() {
            this._setcurCenter(this.curCenter)
          }
        },
        methods: {
          showChangeLatlng() {
            this.$Modal.confirm({
              title: '修改中心坐标提示',
              content: '<p style="color: red">开启手动修改，请仔细确认中心坐标值，否则可能会导致定位失败，确认开启手动修改吗？</p>',
              onOk: () => {
                this.editLatlng = true;
              },
              onCancel: () => {
              }
            });
          },
          querMapFromAddr(addr) {
            this.queryMapParam = addr;
            this.queryMapInfo(this.queryMapParam);
          },
          getTypes() {
            centerService.getType().then(res => {
              this.centerTypes = res.data;
              this.centerTypeMap = {}
              for (let i = 0, len = res.data.length; i < len; i++) {
                let _d = res.data[i];
                _d.expandData = JSON.parse(_d.expandData)
                this.centerTypeMap[_d.cod] = _d;
              }
            })
          },
          getGroups() {
            deptService.getDeptTree(1).then(res => {
              this.groups = res.data;
            })
          },
          handleSuccess0 (res, file, fileList) {
            this.handleSuccess(0, res, file, fileList);
          },
          handleSuccess1 (res, file, fileList) {
            this.handleSuccess(1, res, file, fileList);
          },
          handleFormatError (file) {
            console.info(file)
            this.$Message.warning('文件格式【' + file.name + '】不允许上传，只允许上传[jpg,jpeg,png]中的一种');
          },
          handleMaxSize (file) {
            this.$Message.warning( '文件【' + file.name + '】太大，只允许小于2M文件上传.');
          },
          handleSuccess (index, res, file, fileList) {
            console.info({res, file, fileList})
            if (res.success) {
              this.$Message.success("上传成功");
              if (index == 0) {
                this.curCenter.layout = res.data;
              } else {
                this.curCenter.img = res.data;
              }
              this.$forceUpdate();
            }else {
              this.$Message.error("上传失败:" + res.msg)
            }
          },
          initMap(){
            let zoom = 12;
            //初始化地图对象
            this.map = new T.Map("map_center_div_id");
            //设置显示地图的中心点和级别
            this.map.centerAndZoom(new T.LngLat(120.25989,30.18693), zoom);
            this.geocode = new T.Geocoder();
            let cp = new T.CoordinatePickup(this.map, {callback: this.addMarker})
            cp.addEvent(); //创建搜索对象
            let config = {
              pageCapacity: 10,	//每页显示的数量
              onSearchComplete: this.localSearchResult	//接收数据的回调函数
            };
            this.localsearch = new T.LocalSearch(this.map, config);
          },
          showInMap(_item_index) {
            let _item = this.queryMapInfos[_item_index];
            let _latlng = _item.value.split(",")
            console.info({_latlng})
            let lonlat = new T.LngLat(_latlng[0], _latlng[1]);
            this.addMarker(lonlat)
            this.map.panTo(lonlat);
          },
          queryMapInfo(_info) {
            console.info({_info})
            if (!_info) {
              this.queryMapInfos = [];
              return;
            }
            this.localsearch.search(_info)
          },
          localSearchResult(result) {
            console.info({result})
            this.queryMapInfos = [];
            let pois = result.getPois();
            for (let i = 0, len = pois.length; i < len; i++) {
              let _p = pois[i];
              this.queryMapInfos.push({value: _p.lonlat, label: _p.name, address: _p.address})
            }
          },
          addMarker(latlng, onlyMarker) {
            if (!this.curCenter.id) {
              return;
            }
            console.info(latlng)
            if (this.marker) {
              this.map.clearOverLays();
            }
            //创建图片对象
            let icon = new T.Icon({
              iconUrl: town_marker_img,
              iconSize: new T.Point(40, 40),
              iconAnchor: new T.Point(10, 25)
            });
            //向地图上添加自定义标注
            this.marker = new T.Marker(new T.LngLat(latlng.lng, latlng.lat), {icon: icon});
            this.map.addOverLay(this.marker);
            this.curCenter.latlng = latlng.lat + "," + latlng.lng;
            // if (!onlyMarker) {
            this.geocode.getLocation(latlng,(result) => {
              let info = result.getAddressComponent()
              console.info({info})
              this.mapMarkerAddr = info.address;
            });
            // }
          },
            renderContent (h, { root, node, data }) {
                if(this.curCenter.townId && this.curCenter.townId + "_" + this.curCenter.centerType == data.typeId + "_" + data.id) {
                  data.expand = true;
                }
                return h('span', {
                    on: {
                        click: () => {this.treeSelect(data)}
                    }
                }, [
                    h('Icon', {
                        props: {
                            type: data.children ? "" : 'ios-document-outline'
                        },
                        style: {
                            marginRight: '8px'
                        }
                    }),
                    h('span', data.centerName)
                ])
            },
            add: function(){
                let node = this.curCenter.$node;
                if(node){
                    this.editType = 1;
                    this.curCenter = {
                        parentName: node.name,
                        parentId: node.id
                    };
                }else {
                    this.curCenter = {parentName: "请选择树节点"};
                }
            },
            getCenterList: async function () {
                centerService.getOwner().then(res => {
                  if (res.success) {
                    this.centerDataList = res.data;
                  }else {
                    this.$Message.error("获取服务中心列表失败，" + res.code + ":" + res.msg)
                  }
                }).catch(error => {
                  this.$Message.error("获取服务中心列表失败，" + error.code + ":" + error.msg)
                })
            },
            treeSelect: function (node) {
              if (node.children) {
                // node.expand = !node.expand;
                return;
              };
              if(this.curCenter.id == node.id)return;
              this.editType = 2;
              this.editLatlng = false;
              this.queryMapParam = "";
              this._setcurCenter(node);
            },
            _setcurCenter:function(node){
                this.curCenter = {...node};
                delete this.curCenter.adminList
                if (node.latlng) {
                  let _latlng = node.latlng.split(",");
                  let lnglat = new T.LngLat(_latlng[1], _latlng[0])
                  this.addMarker(lnglat, 1);
                  this.map.panTo(lnglat);
                }else {
                  if (this.marker) {
                    this.map.clearOverLays();
                  }
                }
            },
            showProjectList() {
              this.projectModal = true;
            },
            saveAdmin() {
                let _expandData = this.centerTypeMap[this.curCenter.centerType].expandData || {};
                if (!this.curCenter.centerName) {
                  return this.$Message.error("中心名称不能为空！")
                }
                if (!this.curCenter.workTime) {
                  return this.$Message.error("工作时间不能为空！")
                }
                if (_expandData.tel && !this.curCenter.tel) {
                  return this.$Message.error("联系电话不能为空！")
                }
                if (!this.curCenter.addr) {
                  return this.$Message.error("中心位置没有选择！")
                }
                if (_expandData.layout && !this.curCenter.layout) {
                  return this.$Message.error("大厅布局图没有上传！")
                }
                if (_expandData.img && !this.curCenter.img) {
                  return this.$Message.error("外部照片没有上传！")
                }
                let latlngArr = this.curCenter.latlng.split(",");
                if (latlngArr.length != 2) {
                  return this.$Message.error("中心坐标格式不正确！如不清楚，可参考点击地图自动生成的坐标格式")
                }
                this.editLatlng = false;
                centerService.save(this.curCenter).then(res => {
                  if (res.success) {
                    this.$Message.success("保存服务中心数据成功");
                    // this.editType = 0;
                    // this.curCenter = {};
                    this.getCenterList();
                  }else {
                    this.$Message.error("保存服务中心失败，" + res.code + ":" + res.msg)
                  }
                }).catch(error => {
                  this.$Message.error("保存服务中心数据失败，" + error.code + ":" + error.msg)
                })
            }

        }
    }
</script>
<style>
    .ivu-card-body {
        height: 100%;
        overflow-y: auto;
    }
</style>
