<template>
  <div style="height: 100%">
    <Row>
      <Col span="24">
        <Card>
          <p slot="title">
            <Icon type="ios-list-outline"></Icon>
            消息列表
          </p>
          <Row>
            <Col span="24">
              <Button type="primary" @click="openNew">新增</Button>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <Button type="primary" @click="openAudit">审核</Button>
              <span @click="searchAdmin" style="margin: 0 10px;float: right"><Button type="primary" icon="search">搜索</Button></span>
              <Input v-model="searchParams.messageName" clearable placeholder="请输入站点名称" style="margin:0 10px;width: 200px; float: right" />
              <Select v-model="searchParams.state" style="width:200px;margin:0 10px;width: 200px; float: right" placeholder="请选择审核状态" clearable>
                <Option :value="0" :key="0">待审核</Option>
                <Option :value="1" :key="1">审核通过</Option>
                <Option :value="2" :key="2">审核不通过</Option>
              </Select>
            </Col>
          </Row>
          <br>
          <Row>
            <Col span="24" align="middle">
              <Table border ref="selection" :columns="columns" :data="messageData" @on-selection-change="selectAdmin"></Table>
              <br/>
              <Page  @on-change="getAllMessages" @on-page-size-change="pageSizeChange" :total="parseInt(page.total)" :current="page.pageNum > 0 ? page.pageNum : 1" :page-size="page.pageSize"  show-sizer></Page>
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>
    <Modal
        v-model="adminModal"
        :title="curMessage.id > 0 ? '修改' : '新增'"
        justify-content= "Message"
        ref="adminModal"
        width="80">
      <Form ref="adminForm" :model="curMessage" :rules="messageValidate" :label-width="150" >
        <FormItem label="消息主题" prop="title">
          <Input v-model="curMessage.title" placeholder="请输入消息主题..." :clearable="true"></Input>
        </FormItem>
        <FormItem label="首页结束显示日期" prop="mainExpireDate">
          <DatePicker type="date" v-model="curMessage.mainExpireDate" placeholder="请输入首页结束显示日期，为空则不显示..." :clearable="true"></DatePicker>
          <span style="color: red;font-size: 12px; padding-left: 20px" >  说明：日期为空时，不在首页显示</span>
        </FormItem>
        <FormItem label="消息附件">
          <div style="border: 1px solid #ccc;">
            <Toolbar
                style="border-bottom: 1px solid #ccc"
                :editor="editor"
                :defaultConfig="toolbarConfig"
                :mode="mode"
            />
            <Editor
                style="height: 500px; overflow-y: hidden;"
                v-model="html"
                :defaultConfig="editorConfig"
                :mode="mode"
                @onCreated="onCreated"
            />
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="adminModal = false">取消</Button>
        <Button type="primary" @click="saveAdmin">保存</Button>
      </div>
    </Modal>
    <Modal
        v-model="messageAuditModal"
        title="消息审核"
        :mask-closable="false"
    >
      <List header="待审核列表" border>
        <ListItem v-for="(m, m_index) in auditDataList"><p style="font-size: 16px;font-weight: 500;"><span style="color: red">【{{m_index+1}}】</span> {{m.title}}</p></ListItem>

      </List>
      <div style="padding: 20px 0px 0px 0px">
        <Form>
          <FormItem label="审核结果">
            <RadioGroup v-model="auditResult">
              <Radio :label="1" border>通过</Radio>
              <Radio :label="2" border>不通过</Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </div>
      <template slot="footer">
        <Button @click="cancelAudit">取 消</Button>
        <Button type="primary" @click="audit">审 核</Button>
      </template>
    </Modal>
  </div>
</template>
<style>
</style>
<script>
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import * as dayjs from 'dayjs'
import { messageService} from '@/api';
import LoginUtil from "@/libs/LoginUtil";
import config from "@/config";
import pdf from "@/assets/pdf.svg"
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
export default {
  name: 'Message',
  mounted() {
    this.getAllMessages(this.page.pageNum);
  },
  components: {
    Editor,
    Toolbar
  },

  data () {
    return {
      auditResult: 0,
      messageAuditModal: false,
      auditDataList: [],
      editor: null,
      html: '<p>hello</p>',
      toolbarConfig: {
      },
      editorConfig: {
        placeholder: '请输入消息内容...' ,
        MENU_CONF: {
          'uploadImage': {
            // 其他配置...

            // 小于该值就插入 base64 格式（而不上传），默认为 0
            base64LimitSize: 5 * 1024 // 5kb
          }
        }
      },
      mode: 'default', // or 'simple'
      icon_pdf: pdf,
      uploadUrl: config.rest.file.upload,
      imgPrefix: "/upload-file",
      userToken: LoginUtil.getToken(),
      MessageId: null,
      projectModal: false,
      groupMap: {},
      value1: '1',
      value2: '2',
      indeterminateDept:true,
      checkAllDept:true,
      indeterminateRole:true,
      checkAllRole:true,
      deptList:[],
      adminList: [],
      MessageAdmins:[],
      deptGroup:[],
      curMessage: {},
      searchParams: {
        messageName: '',
        state: null
      },
      selectedAdmin: {},
      selectedAdmins:[],

      messageValidate: {
        title:[
          {required: true,message:"请输入消息名称", trigger: 'blur'},
        ],
      },
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '标题',
          key: 'title'

        },
        {
          title: '首页结束显示日期',
          key: 'mainExpireDate',
          width: 160,
        },
        {
          title: '创建人',
          key: 'createUserName',
          width: 120,
        },
        {
          title: '审核状态',
          key: 'state',
          width: 120,
          render: (h, params) => {
            let _state;
            switch (params.row.state) {
              case 1:
                _state = "审核通过";
                break;
              case 2:
                _state = "审核不通过";
                break;
              default:
                _state = "待审核";
            }
            return h("span", _state);
          }
        },
        {
          title: '操作',
          align:'Message',
          width: 140,
          render: (h, params) => {
            let buttons = [];
              buttons.push(h('Button', {
                props: {
                  type: 'success',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.openEdit(params.row)
                  }
                }
              }, '修改'))
              buttons.push(h('Button', {
                props: {
                  type: 'success',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.show_delete_confirm(params.row.id)
                  }
                }
              }, '删除'))
            return h('div', buttons);
          }
        }

      ],
      // adminTypeName:[],
      messageData: [],
      page : {
        pageNum: 0,
        pageSize: 10
      },
      adminModal: false,
    }
  },
  methods: {
    audit() {
      if (!this.auditResult) {
        return this.$Message.error("请先选择审核结果")
      }
      let ids = this.auditDataList.map(item => item.id);
      console.info({auditResult: this.auditDataList, ids})
      messageService.audit(ids.join(","), this.auditResult).then(res => {
        console.info({res})
        if (res.success) {
          this.$Message.success("审核成功");
          this.messageAuditModal = false;
          this.getAllMessages(this.page.pageNum)
        }else {
          return this.$Message.error("审核失败," + res.code + ":" + res.msg)
        }
      })
    },
    cancelAudit() {
      this.auditResult = 0;
      this.messageAuditModal = false;
    },
    openAudit() {
      if (this.auditDataList.length == 0) {
        return this.$Message.error("请先选择要审核的消息列表")
      }
      this.messageAuditModal = true;
    },
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    openNew() {
      this.curMessage = {};
      this.editor.setHtml("")
      this.MessageAdmins = [];
      this.adminModal = true
    },
    handleFormatError (file) {
      console.info(file)
      this.$Message.warning('文件格式【' + file.name + '】不允许上传，只允许上传pdf格式文件');
    },
    handleMaxSize (file) {
      this.$Message.warning( '文件【' + file.name + '】太大，只允许小于2M文件上传.');
    },
    handleSuccess (res, file, fileList) {
      console.info({res, file, fileList})
      if (res.success) {
        this.$Message.success("上传成功");
          this.curMessage.filePath = res.data;
          this.$forceUpdate();
      }else {
        this.$Message.error("上传失败:" + res.msg)
      }
    },
    pageSizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.getAllMessages(this.page.pageNum)
    },
    getAllMessages(pageNum) {
      messageService.getPage(this.searchParams.messageName, this.searchParams.state, pageNum, this.page.pageSize).then(res => {
        console.info({res})
        let _data = res.data;
        _data.list.forEach(item => {
          if (item.state != 0) {
            item._disabled = true;
          }
        })
        this.messageData = _data.list;
        console.info({_data: this.messageData})
        this.page = _data;
      })
    },
    searchAdmin() {
      this.getAllMessages(this.page.pageNum)
    },
    show_delete_confirm(id) {
      this.$Modal.confirm({
        title: "确认删除吗？",
        onOk: () => {
          messageService.delete(id).then(res => {
            if (res.success) {
              this.$Message.success("删除成功");
              this.getAllMessages(this.page.pageNum);
            }else {
              this.$Message.error("删除消息数据失败，" + res.code + ":" + res.msg)
            }
          }).catch(error => {
            this.$Message.error("删除消息数据失败，" + error.code + ":" + error.msg)
          })
        }
      })
    },
    openEdit(_data) {
      messageService.getMessage(_data.id).then(res => {
        if (res.success && res.data) {
          this.curMessage = res.data;
          this.editor.setHtml(this.curMessage.content||"")
          this.adminModal = true
        }else {
          this.$Message.error("获取消息数据失败，" + res.code + ":" + res.msg)
        }
      }).catch(error => {
        this.$Message.error("获取消息数据失败，" + error.code + ":" + error.msg)
      })
      // this.$refs.adminForm.reset();
    },
    selectAdmin(selections) {
      console.info({selections})
      this.auditDataList = [...selections]
    },
    saveAdmin() {
      let _info = this.editor.getHtml().trim();
      if (!_info) {
        return this.$Message.error("消息内容不能为空，")
      }
      // console.info("mainExpireDate" ,dayjs(this.curMessage.mainExpireDate).format("YYYY-MM-DD"))
      this.curMessage.mainExpireDate = this.curMessage.mainExpireDate?dayjs(this.curMessage.mainExpireDate).format("YYYY-MM-DD") : "";
      this.curMessage.content = _info;
      this.$refs.adminForm.validate((valid) => {
        if (valid) {
          messageService.save(this.curMessage).then(res => {
            this.adminModal = false
            this.getAllMessages(this.page.pageNum);
          }).catch(error => {
            this.$Message.error("保存消息数据失败，" + error.code + ":" + error.msg)
          })
        }
      })
    }
  }
}
</script>
