<template>
  <div style="height: 100%">
    <Row>
      <Col span="24">
        <Card>
          <p slot="title">
            <Icon type="ios-list-outline"></Icon>
            消息列表
          </p>
          <Row>
            <Col span="24">
              <Button type="primary" @click="openNew">新增</Button>
              <span @click="searchAdmin" style="margin: 0 10px;float: right"><Button type="primary" icon="search">搜索</Button></span>
              <Input v-model="searchParams.messageName" clearable placeholder="请输入站点名称" style="margin:0 10px;width: 200px; float: right" />
            </Col>
          </Row>
          <br>
          <Row>
            <Col span="24" align="middle">
              <Table border ref="selection" :columns="columns" :data="messageData" @on-selection-change="selectAdmin"></Table>
              <br/>
              <Page  @on-change="getAllMessages" :total="parseInt(page.total)" :current="page.pageNum > 0 ? page.pageNum : 1" :page-size="page.pageSize"></Page>
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>
    <Modal
        v-model="adminModal"
        :title="curMessage.id > 0 ? '修改' : '新增'"
        justify-content= "Message"
        ref="adminModal"
        width="500">
      <Form ref="adminForm" :model="curMessage" :rules="messageValidate" :label-width="110" >
        <FormItem label="消息主题" prop="title">
          <Input v-model="curMessage.title" placeholder="请输入消息主题..." :clearable="true"></Input>
        </FormItem>
        <FormItem label="消息附件">
          <Upload
              ref="upload"
              :show-upload-list="false"
              :on-success="handleSuccess"
              :headers="{'Authorization': 'Bearer ' + userToken}"
              :format="['pdf']"
              :max-size="2048"
              :on-format-error="handleFormatError"
              :on-exceeded-size="handleMaxSize"
              type="drag"
              name="file"
              :action="uploadUrl"
              style="display: inline-block;width:100px;">
            <div style="width: 100px;height:100px;line-height: 100px;">
              <div v-if="curMessage.filePath">
                <img :src="icon_pdf" width="100" height="100" style="object-fit: cover;"/>
              </div>
              <Icon type="ios-camera" size="20" v-else></Icon>

            </div>
          </Upload>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="adminModal = false">取消</Button>
        <Button type="primary" @click="saveAdmin">保存</Button>
      </div>
    </Modal>
  </div>
</template>
<style>
</style>
<script>
import { messageService} from '@/api';
import LoginUtil from "@/libs/LoginUtil";
import config from "@/config";
import pdf from "@/assets/pdf.svg"
export default {
  name: 'Message',
  mounted() {
    this.getAllMessages(this.page.pageNum);
  },
  components: {

  },

  data () {
    return {
      icon_pdf: pdf,
      uploadUrl: config.rest.file.upload,
      imgPrefix: "/upload-file",
      userToken: LoginUtil.getToken(),
      MessageId: null,
      projectModal: false,
      groupMap: {},
      value1: '1',
      value2: '2',
      indeterminateDept:true,
      checkAllDept:true,
      indeterminateRole:true,
      checkAllRole:true,
      deptList:[],
      adminList: [],
      MessageAdmins:[],
      deptGroup:[],
      curMessage: {},
      searchParams: {
        messageName: '',
      },
      selectedAdmin: {},
      selectedAdmins:[],

      messageValidate: {
        title:[
          {required: true,message:"请输入消息名称", trigger: 'blur'},
        ],
      },
      columns: [
        {
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '标题',
          key: 'title'

        },
        {
          title: '操作',
          align:'Message',
          width: 200,
          render: (h, params) => {
            let buttons = [];
              buttons.push(h('Button', {
                props: {
                  type: 'success',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.openEdit(params.row)
                  }
                }
              }, '修改'))
              buttons.push(h('Button', {
                props: {
                  type: 'success',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.show_delete_confirm(params.row.id)
                  }
                }
              }, '删除'))
              buttons.push(h('Button', {
                props: {
                  type: 'success',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    window.open(this.imgPrefix + params.row.filePath)
                  }
                }
              }, '预览'))
            return h('div', buttons);
          }
        }

      ],
      // adminTypeName:[],
      messageData: [],
      page : {
        pageNum: 0,
        pageSize: 10
      },
      adminModal: false,
    }
  },
  methods: {
    openNew() {
      this.curMessage = {};
      this.MessageAdmins = [];
      this.adminModal = true
    },
    handleFormatError (file) {
      console.info(file)
      this.$Message.warning('文件格式【' + file.name + '】不允许上传，只允许上传pdf格式文件');
    },
    handleMaxSize (file) {
      this.$Message.warning( '文件【' + file.name + '】太大，只允许小于2M文件上传.');
    },
    handleSuccess (res, file, fileList) {
      console.info({res, file, fileList})
      if (res.success) {
        this.$Message.success("上传成功");
          this.curMessage.filePath = res.data;
          this.$forceUpdate();
      }else {
        this.$Message.error("上传失败:" + res.msg)
      }
    },
    getAllMessages(pageNum) {
      messageService.getPage(this.searchParams.messageName, pageNum, this.page.pageSize).then(res => {
        console.info({res})
        let _data = res.data;
        this.messageData = _data.list;
        this.page = _data;
      })
    },
    searchAdmin() {
      this.getAllMessages(this.page.pageNum)
    },
    show_delete_confirm(id) {
      this.$Modal.confirm({
        title: "确认删除吗？",
        onOk: () => {
          messageService.delete(id).then(res => {
            if (res.success) {
              this.$Message.success("删除成功");
              this.getAllMessages(this.page.pageNum);
            }else {
              this.$Message.error("删除消息数据失败，" + res.code + ":" + res.msg)
            }
          }).catch(error => {
            this.$Message.error("删除消息数据失败，" + error.code + ":" + error.msg)
          })
        }
      })
    },
    openEdit(_data) {
      this.curMessage = {..._data};
      this.adminModal = true
      // this.$refs.adminForm.reset();
    },
    selectAdmin() {

    },
    saveAdmin() {
      this.$refs.adminForm.validate((valid) => {
        if (valid) {
          messageService.save(this.curMessage).then(res => {
            this.adminModal = false
            this.getAllMessages(this.page.pageNum);
          }).catch(error => {
            this.$Message.error("保存消息数据失败，" + error.code + ":" + error.msg)
          })
        }
      })
    }
  }
}
</script>
