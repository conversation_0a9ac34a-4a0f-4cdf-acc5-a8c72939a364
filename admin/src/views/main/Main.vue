<template>
    <Layout style="height: 100%; background-color: unset" class="main" >
        <Sider v-model="quickMenu.show"  hide-trigger collapsible :width="345" :collapsed-width="0">
            <Card style="width:100%;height: 100%; " theme="light" title="操作菜单">
                <Menu theme="light" accordion :open-names="['1']" style="width: 100%">
                    <MenuGroup :name="(m_index + 1) + ''"  v-for="(menu, m_index) in menus" style="width: 100%" :title="menu.title">
                          <MenuItem  :name="(m_index + 1) + '_' + (m_s_index + 1)" :to="smenu.url" v-for="(smenu, m_s_index) in menu.children">
                              <Icon type="ios-document-outline" />
                              {{smenu.title}}
                          </MenuItem>
                    </MenuGroup>
                </Menu>
            </Card>
        </Sider>
        <Layout>
            <Header class="header-con">
                <Row justify="center">
                    <Col span="1" style="text-align: center;">
                        <Icon type="md-menu" size="26" :style="quickMenu.show ? {cursor: 'pointer'} : {cursor: 'pointer',transform: 'rotate(-90deg)'}" @click="changeShow" />
                    </Col>
                    <Col span="21">
                        <span style="font-size: 20px">“就近办”地图后台管理系统</span>
                    </Col>
                    <Col span="2" style="text-align: center;">
                        <Tooltip max-width="400" theme="light">
                            <template slot="content">
                                <CellGroup @on-click="rightClick">
                                    <Cell name="username">
                                        <Icon type="md-person" />
                                        {{username}}
                                    </Cell>
                                    <Cell name="updatePassword">
                                        <Icon type="md-person" />
                                        修改密码
                                    </Cell>
                                    <Cell name="logout">
                                        <Icon type="md-log-out" />
                                        登出
                                    </Cell>
                                </CellGroup>
                            </template>
                            <Avatar style="background-color: #87d068" icon="md-person" />
                            <Icon type="md-arrow-dropdown" size="20" />
                        </Tooltip>
                    </Col>
                </Row>
            </Header>

            <Content class="main-content-con">
                <transition name="fade" mode="out-in">
                    <router-view class="view" @confirm="showConfirm"  @query="showQuery"></router-view>
                </transition>
                <Modal
                        v-model="confirm.show"
                        :title="confirm.title"
                        @on-ok="confirm.ok"
                        @on-cancel="confirm.cancel">
                    {{confirm.content}}
                </Modal>
                <Modal v-model="updatePasswordModal" width="360">
                    <p slot="header" style="text-align:center">
                        <span>修改密码</span>
                    </p>
                    <Form ref="formData" label-colon :model="formData" :rules="formValidate">
                        <Row>
                            <Col span="24">
                                <FormItem label="用户姓名" label-position="top">
                                    <span>{{formData.userName}}</span>
                                </FormItem>
                            </Col>
                        </Row>
                        <Row >
                            <Col span="24">
                                <FormItem label="原始密码" label-position="top" prop="oldPass">
                                    <Input v-model="formData.oldPass" type="password" clearable style="width: 200px" />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row >
                            <Col span="24">
                                <FormItem label="新密码　" label-position="top" prop="newPass">
                                    <Input v-model="formData.newPass" type="password" clearable style="width: 200px" />
                                </FormItem>
                            </Col>
                        </Row>
                        <Row >
                            <Col span="24">
                                <FormItem label="确认密码" label-position="top" prop="rePass">
                                    <Input v-model="formData.rePass" type="password" clearable style="width: 200px" />
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                    <div slot="footer">
                        <Button type="primary" size="large" long :loading="buttonLoading" @click="savePass()">提交</Button>
                    </div>
                </Modal>
                <Drawer
                        title="查询窗口"
                        v-model="queryShow"
                        :width="25"
                >
                    <Form v-model="queryData" label-colon>
                        <Row :gutter="24">
                            <Col span="24" v-for="item in queryInfo.queryItems">
                                <FormItem :label="item.title" :label-width="80">
                                    <Input type="text" clearable v-model="queryData[item.key]" clearable v-if="item.type != 'select'&&item.type!='date'"/>
                                    <RadioGroup clearable v-model="queryData[item.key]" v-if="item.type == 'select' && item.options.length <= 2">
                                        <Radio :label="option.value" v-for="option in item.options">
                                            <span>{{option.label}}</span>
                                        </Radio>
                                        <Radio label="">
                                            <span>全部</span>
                                        </Radio>
                                    </RadioGroup>
                                    <Select v-model="queryData[item.key]" clearable v-if="item.type == 'select' && item.options.length > 2">
                                        <Option :value="option.value" :key="option.value" v-for="option in item.options">{{option.label}}</Option>
                                    </Select>
                                    <DatePicker type="date" style="width: 200px" v-model="queryData[item.key]" v-if="item.type == 'date'"></DatePicker>
                                </FormItem>
                            </Col>
                            <Col span="24" style="text-align: center">
                                <Button @click="resetQuery" size="large">重置参数</Button>
                                <Button type="primary" size="large" @click="toQuery" style="margin-left: 30px" >确认查询</Button>
                            </Col>
                        </Row>
                    </Form>
                </Drawer>
            </Content>
        </Layout>
    </Layout>
</template>

<script>
    import './main.less'
    import LoginUtil from "@/libs/LoginUtil";
    import {menuService,userService} from "@/api";
    import sysRoutes from '@/router/system'
    import Cookies from 'js-cookie'

    export default {
        name: "Main",
        data() {

            const valideOldPass = async (rule, value, callback) => {
                if (!value) {
                    callback();
                    return;
                }
                // this.formData.id=1
                // callback();
                // return;
                //后台查询
                // let res = await userService.validate(value);
                console.log(res);
                console.log("=======================")
                if(res.success){
                    this.formData.id=res.message
                    callback();
                }else{
                    callback(new Error('密码错误'));
                }
            };
            const validatePass = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error('非空，必填项'));
                } else {
                    if (this.formData.rePass !== '') {
                        // 对第二个密码框单独验证
                        this.$refs.formData.validateField('rePass');
                    }
                    callback();
                }
            };
            const validatePassCheck = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error('请再次输入密码'));
                } else if (value !== this.formData.newPass) {
                    callback(new Error('确认密码与新密码需保持一致！'));
                } else {
                    callback();
                }
            };
            return {
                updatePasswordModal:false,
                userInfo:{},
                buttonLoading:false,
                formData:{
                    userName:null,
                    idCard:null,
                    rePass:null,
                    oldPass:null,
                    newPass:null
                },

                formValidate:{
                    oldPass: [
                        {required: true, message: '非空，必填项', trigger: 'blur'},
                        // {validator:valideOldPass, trigger: 'blur'}
                    ],
                    newPass: [

                        {required: true, message: '非空，必填项', trigger: 'blur'},
                        // { validator: validatePass, trigger: 'blur' },
                        { message: '需6-20位', trigger: 'blur',min: 6, max:20}
                    ],
                    rePass: [
                        {required: true, message: '非空，必填项', trigger: 'blur'},
                        // { validator: validatePassCheck, trigger: 'blur' }
                    ],
                },
                queryShow: false,
                queryData: {},
                /**
                 * 查询对象
                 */
                queryInfo: {
                    //选择项配置
                    queryItems: [],
                    queryBack: null
                },
                username: LoginUtil.getUsername(),
                quickMenu: {
                    show: false,
                    items: [{
                        title: '文章管理',
                        subs: [
                            {title: '评论管理', icon: 'ios-paper'},
                        ]
                    }]
                },
                menus: [],
                confirm: {
                    show: false,
                    title: '消息提示',
                    content: '',
                    ok(){},
                    cancel(){},
                },
                timeTask:{},
            }
        },
        async created() {
            let allRouters = [...sysRoutes];
            let allRouterMap = {};
            allRouters.forEach(router => {
                if (router.code) {
                    allRouterMap[router.code] = router;
                }
                if (router.children) {
                    router.children.forEach(c_router => {
                        if (c_router.code) {
                            allRouterMap[c_router.code] = c_router;
                        }
                    })
                }
            })
            let rel = await menuService.getMenuTree(-1, 1);
            if (rel.success) {
                let index = 0;
                rel.data.forEach(menu => {
                    let _router = allRouterMap[menu.menuNo];
                    // if (_router) {
                    //     menu.url = _router.path;
                    //     menu.name = _router.name;
                    //     menu.show = true;
                    // }
                    if (index == 0) {
                      menu.expand = true;
                    }
                    index++;
                    if (menu.children) {
                        menu.children.forEach(_c_menu => {
                            let _c_router = allRouterMap[_c_menu.menuNo];
                            if (_c_router) {
                                _c_menu.url = /*(_router ? _router.path : "") + */(_c_router ? _c_router.path : "");
                                _c_menu.name = _c_router.name;
                                _c_menu.icon = _c_router.icon || 'ios-people';
                                _c_menu.show = true;
                            }
                        })
                    }
                })
                console.info({menus: rel})
                this.menus = rel.data;
                console.info({m: this.menus})
            }
        },
        beforeDestroy(){
            clearInterval(this.timeTask);
        },

        mounted: function () {
            // this.userInfo = JSON.parse(Cookies.get('userInfo').toString())
            //todo 处理报警提醒
            // this.timeTask = setInterval(()=>{
            //     this.getMonitorAlarmList();
            // },30*1000);
        },
        methods: {
            updatePassword () {
                this.formData={
                    userName:this.username,
                    idCard:null,
                    rePass:null,
                    oldPass:null,
                    newPass:null
                };
                this.buttonLoading = false;
                this.$refs.formData.resetFields();
                this.updatePasswordModal=true
            },
            //保存密码
            savePass() {
                this.buttonLoading = true;
                if (this.formData.oldPass) {
                    if (this.formData.newPass != this.formData.rePass) {
                      return this.$Message.error("两次输入密码不一样", {duration: 5000});
                    }
                    userService.updatePwd(this.formData.oldPass,this.formData.newPass).then(res=>{
                      debugger
                        if (res.success) {
                            this.$Message.success("修改成功");
                            this.updatePasswordModal = false;
                            this.formData={}
                        } else {
                          debugger
                            this.$Message.error(res.message, {duration: 5000});
                            this.buttonLoading = false;
                        }
                    }).catch(error => {
                      this.$Message.error("密码不正确，修改密码失败", {duration: 5000});
                      this.buttonLoading = false;
                    })
                }else{
                    this.buttonLoading = false;
                }
            },
            resetQuery() {
                this.queryData = {}
                this.toQuery()
            },
            toQuery() {
                if (this.queryInfo.queryBack) {
                    this.queryInfo.queryBack(this.queryData);
                }
                this.queryShow = false;
            },
            showQuery(queryItems, queryBack) {
                console.info({
                    queryItems,
                    queryBack
                })
                this.queryInfo = {
                    queryItems,
                    queryBack
                }
                this.queryShow = true;
            },
            changeShow() {
                this.quickMenu.show = !this.quickMenu.show;
            },
            showConfirm({title, content, callback}) {
                this.confirm = {
                    show: true,
                    title: title || '消息提示',
                    content: content || '',
                    ok(){ (callback || this.confirm.ok)(true)},
                    cancel(){ (callback || this.confirm.cancel)(false)},
                }
            },
            rightClick(name) {
                switch (name) {
                    case "logout":
                        LoginUtil.logout();
                        // this.$router.push({path: '/login'})
                        location.href = location.href;
                        break;
                    case "updatePassword":
                        this.updatePassword();
                        break;
                }
            }
        }
    }
</script>

<style scoped>
    .ivu-layout-header{
        padding: unset !important;
    }
    .ivu-menu-vertical.ivu-menu-light:after {
        width: 0px;
    }
    .ivu-card {
        border-radius: 0px;
    }
    .q_title  {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }
    .ivu-menu-horizontal.ivu-menu-light:after {
        bottom: unset !important;
    }
    .ivu-menu-light.ivu-menu-horizontal .ivu-menu-item-active {
        color: #2d8cf0;
        border-bottom: unset !important;
    }
</style>
