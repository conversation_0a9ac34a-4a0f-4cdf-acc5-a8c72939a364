<template>
    <div>
        <Card>
            <Button @click="editRole" type="primary" v-if="auth.add">新增角色</Button>
            <br><br>
            <Table :loading="loading" border :columns="columns" :data="roleData"></Table>
            <Drawer :title="'权限配置 - ' + formData.roleName" :closable="false" v-model="editAuth" :inner="true" width="20">
                <div slot="header" >
                    <p>权限配置 - {{formData.roleName}}</p>
                    <checkbox @on-change="checkAllMenu" v-model="checkAllMenuFlag" style="position: absolute;right: 50px;top: 18px;">全选</checkbox>
                    <Button style="z-index: 100;position: absolute;right: 9px;top: 12px;" size="small" type="primary" @click="saveRoleAuth" v-if="auth.auth">保存</Button>
                </div>
                <Tree :data="menuData" ref="authTree" :render="renderContent" show-checkbox :multiple="true" empty-text="没有数据"></Tree>
            </Drawer>
        </Card>
        <Modal v-if="auth.change || auth.add"
               :title="'编辑角色'"
               v-model="roleModel"
               ref="roleModal"
               @on-ok="save"
               :loading="true"
               width="720"
               justify-content="center"

        >
            <Form ref="roleForm" :model="formData" :rules="roleValidate"  :label-width="100" style="width: 95%">
                <FormItem label="角色名称" prop="roleName" label-position="top">
                    <Input  v-model="formData.roleName" :maxlength="20"   placeholder="请输入角色名称..." :clearable="true" />
                </FormItem>
            </Form>
        </Modal>

        <Modal
                title="人员分配"
                v-model="userAllotModel"
                @on-ok="saveRoleUser"
                :loading="true"
                width="720"
                justify-content="center"
                ref="userAllotModel"
        >
            <Form :model="formData" ref="userAllotForm">
                <Row :gutter="32">
                    <Col span="32">
                        <FormItem label="角色名称" label-position="top">
                            {{formData.roleName}}
                        </FormItem>
                    </Col>
                </Row>
                <Row :gutter="32">
                    <Col span="32">
                        人员分配 &nbsp;
                        <br>
                        <Collapse v-model="value1">
                            <Panel name="1">
                                <p slot="content">
                                    <Checkbox
                                            :indeterminate="indeterminate"
                                            :value="checkAll"
                                            @click.prevent.native="handleCheckAll">全选
                                    </Checkbox>
                                    <br><br>
                                    <CheckboxGroup v-model="userGroup"   @on-change="checkAllChange" :clearable="true">
                                        <Checkbox v-for="user in userList" :label="user.id">{{user.nickName}}</Checkbox>
                                    </CheckboxGroup>
                                </p>
                            </Panel>
                        </Collapse>
                    </Col>
                </Row>
            </Form>
        </Modal>
    </div>

</template>

<script>
    import {roleService,userService,roleUserService,menuService} from '@/api';

    export default {
        mounted(){
            this.getAuth();
            this.getAllRoles();
            this.initFormatter();
            this.getUserList();
        },
        name: 'auth-role',
        data () {
            const validateRequired = (role,value,callback)=>{
                value=value.trim()?value.trim():value;
                if(value.length<1){
                    callback(new Error("非空必填"));
                }
                callback();
            };
            return {
                auth: {
                    add: true,
                    change: true,
                    auth: true,
                    del: true
                },
                loading: false,
                menuTreeSetting: {
                    callback: {

                    },
                    check: {
                        enable: true,
                        chkboxType: { "Y" : "ps", "N" : "ps" }
                    }
                },
                roleOldAuth: {},
                roleUserIds:[],
                userGroup:[],
                userList:[],
                indeterminate:true,
                checkAll:false,
                page:{
                    pageNum:1,
                    pageSize:0
                },
                infs:[],
                model13: '',
                roleModel:false,
                loading1: false,
                /**折叠*/
                value1: '1',
                /*配置人员抽屉*/
                userAllotModel: false,
                styles: {
                    height: 'calc(100% - 55px)',
                    overflow: 'auto',
                    paddingBottom: '53px',
                    position: 'static'
                },
                formData: {},
                roleValidate: {
                    roleName: [
                        { validator: validateRequired, trigger: 'change' },
                        // {required: true,min:1, message: "请输入非空的角色名称", trigger: 'change'},
                    ],
                    roleNo: [
                        { validator: validateRequired, trigger: 'change' },
                        // {required: true,min:1, message: "请输入非空且不重复的角色编码", trigger: 'change'},
                    ]
                },
                columns: [
                    {
                        title: '角色名称',
                        key: 'roleName',
                        render: (h,params) => {
                            return h('div',{
                                style: {
                                    cursor: 'pointer'
                                },
                                attrs: {
                                    title: '点击查看权限详情'
                                },
                                on: {
                                    click: () => {
                                        this.editRoleAuth(params.row)
                                    }
                                }
                            },[
                                h('Icon', {
                                    props: {
                                        type:'md-eye'
                                    }
                                }),
                                h('span', params.row.roleName)
                            ]);
                        }
                    },
                    {
                        title: '创建时间',
                        key: 'createDate',
                        align:'center',
                        render: (h, params) => {
                            const row = params.row;
                            return h('div', this.formatDate(row.createDate));
                        }
                    },
                    {
                        title: '创建人',
                        key: 'creator',
                        align:'center',
                    },
                    {
                        title: '修改时间',
                        key: 'updateDate',
                        align:'center',
                        render: (h, params) => {
                            const row = params.row;
                            return h('div', this.formatDate(row.updateDate));
                        }
                    },
                    {
                        title: '修改人',
                        key: 'updator',
                        align:'center'
                    },
                    {
                        title: '操作',
                        align:'center',
                        width: 350,
                        render: (h, params) => {
                            let buttons = [];
                            if (this.auth.change) {
                                buttons.push(h('Button', {
                                    props: {
                                        type: 'primary',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.updateRole(params.index)
                                        }
                                    }
                                }, '修改'))
                            }
                            if (this.auth.auth) {
                                buttons.push(h('Button', {
                                    props: {
                                        type: 'success',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.editRoleAuth(params.row)
                                        }
                                    }
                                }, '权限分配'))
                                buttons.push(h('Button', {
                                    props: {
                                        type: 'success',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.show4(params.row)
                                        }
                                    }
                                }, '人员分配'))
                            }
                            if (this.auth.del) {
                                buttons.push(h('Button', {
                                    props: {
                                        type: 'error',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.remove(params.index)
                                        }
                                    }
                                }, '删除'))
                            }
                            return h('div', buttons);
                        }
                    }
                ],
                roleData: [],
                editAuth: false,
                menuData: [],
                checkAllMenuFlag: false,
                parentNodeMap: {},
                roleMenus: {}
            }
        },
        methods: {
            renderContent (h, { root, node, data }) {
                this.parentNodeMap['node_id' + data.id] = data.title;
                return h('span', [
                    h('Icon', {
                        props: {
                            type: data.children ? 'ios-folder-outline' : 'ios-document-outline',
                            checked: true
                        },
                        style: {
                            marginRight: '8px'
                        }
                    }),
                    h('span', data.title)
                ])
            },
            saveRoleAuth: function() {
                let selectAuth = this.$refs.authTree.getCheckedAndIndeterminateNodes()
                let menuIds = [];
                for (let i = 0, len = selectAuth.length; i < len; i++) {
                    let _m = selectAuth[i];
                    menuIds.push(_m.id);
                }
                console.info(menuIds)
                roleService.saveRoleAuth({roleId: this.formData.id, menuIds: menuIds.join(",")})
                    .then(response => {
                        if (response.success) {
                            this.$Message.success("保存成功")
                        } else {
                            this.$Message.error("保存失败。" + response.message)
                        }
                    })
            },
            getAuth: async function () {
                this.loading = true;
                let menus = await menuService.getMenuTree(-1);
                this.menuData = this._eachMenus(menus);
                this.loading = false;
            },
            _eachMenus(menus) {
                let _menus = [];
                for (let i = 0, len = menus.length; i < len; i++) {
                    let m = menus[i];
                    if (m.id == 36) {
                        console.info(m)
                    }
                    if (m.menuType != 3) {
                        m.checked = false
                        let childMenus = m.children;
                        if (childMenus && childMenus.length > 0) {
                            let realChild = this._eachMenus(childMenus);
                            if (realChild && realChild.length > 0) {
                                m.children = realChild
                            } else {
                                delete m.children
                            }
                        }else {
                            if (this.roleMenus[`id_${m.id}`]) {
                                m.checked = true
                            }
                        }
                        _menus.push(m);
                    }
                }
                return _menus;
            },
            //多选框方法
            handleCheckAll() {
                if (this.indeterminate) {
                    this.checkAll = false;
                } else {
                    this.checkAll = !this.checkAll;
                }
                this.indeterminate = false;
                if (this.checkAll) {
                    var that = this;
                    this.userList.forEach(function (user, index) {
                        that.userGroup.push(user.id);
                    });
                } else {
                    this.userGroup = [];
                }
            },
            checkAllChange(data) {
                if (data.length === this.userList.length) {
                    this.indeterminate = false;
                    this.checkAll = true;
                } else if (data.length > 0) {
                    this.indeterminate = true;
                    this.checkAll = false;
                } else {
                    this.indeterminate = false;
                    this.checkAll = false;
                }
            },
            formatDate: function (date) {
                return new Date(date).Format('yyyy-MM-dd hh:mm:ss');
            },
            initFormatter: function () {
                Date.prototype.Format = function (fmt) {
                    let o = {
                        "M+": this.getMonth() + 1,                    //月份
                        "d+": this.getDate(),                         //日
                        "h+": this.getHours(),                        //小时
                        "m+": this.getMinutes(),                      //分
                        "s+": this.getSeconds(),                      //秒
                        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                        "S": this.getMilliseconds()                   //毫秒
                    };
                    if (/(y+)/.test(fmt))
                        fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
                    for (var k in o)
                        if (new RegExp("(" + k + ")").test(fmt))
                            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                    return fmt;
                }
            },
            save() {
                this.$refs.roleForm.validate((valid) => {
                    if (valid) {
                        this.$emit("confirm",{content: "确定提交吗?", callback: (flag) => {
                                console.log(flag);
                                if(!flag){
                                    this.roleModel = false;
                                    return;
                                }
                                let send = this.formData.id ? roleService.updateRole(this.formData) : roleService.addRole(this.formData);
                                send.then(response => {
                                    if (response.success) {
                                        this.$Message.success("保存成功");
                                        if (!this.formData.id) {
                                            this.formData.id = response.message.id;
                                        }
                                        this.roleModel = false;
                                        this.getAllRoles();
                                    } else {
                                        this.$Message.error(response.message);
                                    }
                                    this.$refs.roleModal.buttonLoading = false;
                                })
                            }});
                    }else {
                        this.$Message.info('验证失败');
                        this.$refs.roleModal.buttonLoading = false;
                    }
                });
            },
            getAllRoles: async function (index) {
                let rel = await roleService.getAllRoles(index ? index - 1 : 0, this.page.pageSize);
                if (rel.success) {
                    let roles = rel.data;
                    this.roleData = roles.list;
                    console.info(this.roleData);
                    this.page = roles
                }else  {
                    this.$Message.error("加载角色数据失败!");
                }
            },
            getUserList: async function(){
                let userList=await userService.getAllAdmins(0,0);
                this.userList = userList.list;
            },
            updateRole(index) {
                this.$refs.roleForm.resetFields();
                this.roleModel = true;
                //提前初始化，解决页面打开后直接点击修改出错情况
                this.formData = {
                    roleName: '',
                    roleNo: ''
                };
                this.formData.roleName = `${this.roleData[index].roleName}`;
                this.formData.roleNo = `${this.roleData[index].roleNo}`;
                this.formData.createDate = `${this.roleData[index].createDate}`;
                this.formData.id = `${this.roleData[index].id}`;

            },
            show4:async function(data) {
                this.userAllotModel = true;
                this.formData = data;
                let roleId = data.id;
                let  roleUserIds = await roleUserService.getRoleUser(roleId);
                this.roleUserIds=roleUserIds;
                console.info(roleUserIds)
                let that =this;
                this.userGroup = [];
                if (this.roleUserIds) {
                    this.roleUserIds.forEach(r=>{
                        that.userGroup.push(r.userId);
                    })
                }
            },
            async editRoleAuth(row) {
                this.formData = this.formData || {}
                if (this.formData.id == row.id) {
                    this.editAuth = true;
                    return;
                }
                this.formData = row;
                // this.$refs.authTree.checkAllNodes(false)
                this.roleMenus = {}
                let menus = await roleService.getAuthByRoleId(row.id);
                for (let i = 0, len = menus.length; i < len; i++) {
                    this.roleMenus[`id_${menus[i].menuId}`] = 1;
                }
                this._eachMenus(this.menuData)
                this.editAuth = true;
            },
            saveRoleUser(){
                let ids = this.userGroup;
                console.info(ids);
                roleUserService.addRoleUsers(this.formData.id,ids)
                    .then(response => {
                        if (response.success) {
                            this.$Message.success("保存成功");
                            this.userAllotModel=false;
                        } else {
                            this.$Message.error(response.message);
                        }
                        this.$refs.userAllotForm.buttonLoading = false;
                    })

            },
            remove(index) {
                this.$emit("confirm",{content: "删除后将无法退回，确认删除此角色吗?", callback: (flag) => {
                        if (!flag) {
                            return
                        }
                        let id = this.roleData[index].id;
                        roleService.deleteRole(id)
                            .then(res => {
                                if (res.success) {
                                    this.$Message.success("删除成功");
                                } else {
                                    this.$Message.error(res.message);
                                }
                                this.getAllRoles(this.page.pageNum);
                            })
                    }})
            },
            editRole() {
                this.$refs.roleForm.resetFields();
                this.userGroup = [];
                this.roleModel = true;
                this.formData = {
                    roleName: '',
                    roleNo: ''
                }
            },
            selectRoleNo:async  function (){
                let count= await roleService.selectRoleNo(this.formData.roleNo)
                if(count!==null && count!==0){
                    this.$Message.error("该角色编码已存在",5)
                }
            },
            checkAllMenu() {
                this.$refs.authTree.checkAllNodes(this.checkAllMenuFlag)
            }

        },
        computed: {
        }
    }
</script>
