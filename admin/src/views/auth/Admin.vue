<template>
    <div style="height: 100%">
        <Row>
            <Col span="24">
                <Card>
                    <p slot="title">
                        <Icon type="ios-list-outline"></Icon>
                        管理员列表
                    </p>
                    <Row>
                        <Col span="24">
                            <Button type="primary" @click="openNew">新增</Button>
                            &nbsp;
                            <Button type="primary" @click="openEdit" :disabled="selectedAdmins.length!=1">修改</Button>
                            &nbsp;
                            <Button type="primary" @click="show_delete_confirm" :disabled="selectedAdmins.length==0">删除</Button>
                            &nbsp;
                            <Button type="primary" @click="resetPwd" :disabled="selectedAdmins.length==0">重置密码</Button>
                            &nbsp;
                            <span @click="searchAdmin" style="margin: 0 10px;float: right"><Button type="primary" icon="search">搜索</Button></span>
                            <Input v-model="searchParams.userNo" clearable placeholder="请输入登录名" style="margin:0 10px;width: 200px; float: right" />
                            <Input v-model="searchParams.nickName" clearable placeholder="请输入用户名" style="width: 200px; float: right" />
                        </Col>
                    </Row>
                    <br>
                    <Row>
                        <Col span="24" align="middle">
                            <Table border ref="selection" :columns="columns" :data="adminData" @on-selection-change="selectAdmin"></Table>
                            <br/>
                            <Page  @on-change="getAllAdmins" :total="parseInt(page.total)" :current="page.pageNum > 0 ? page.pageNum : 1" :page-size="page.pageSize"></Page>
                        </Col>
                    </Row>
                </Card>
            </Col>
        </Row>
        <Modal
                v-model="adminModal"
                :title="curAdmin.id > 0 ? '修改' : '新增'"
                justify-content= "center"
                ref="adminModal"
                @on-ok="saveAdmin"
                :loading="true"
                width="500">
            <Form ref="adminForm" :model="curAdmin" :rules="adminValidate" :label-width="110" >
                <FormItem label="登录名" prop="userNo">
                    <Input v-model="curAdmin.userNo" placeholder="请输入登录名..." :clearable="true" :disabled="curAdmin.id"></Input>
                </FormItem>
                <FormItem label="用户名" prop="nickName">
                    <Input v-model="curAdmin.nickName" placeholder="请输入用户名..." :clearable="true"></Input>
                </FormItem>
                <FormItem label="角色" prop="roleName">
                    <Select v-model="roleGroup" filterable multiple>
                        <Option  v-for="role in roleList" :value="parseInt(role.id)" :key="parseInt(role.id)">{{role.roleName}}</Option>
                    </Select>
                </FormItem>
                <FormItem label="部门" prop="dept">
                    <treeselect v-model="curAdmin.groupId" searchable clearable :multiple="false" :options="deptList" />
                </FormItem>
            </Form>
        </Modal>

    </div>
</template>
<style>
</style>
<script>
    import Treeselect from '@riophae/vue-treeselect'
    // import the styles
    import '@riophae/vue-treeselect/dist/vue-treeselect.css'

    import { userService,deptService,roleService,roleUserService} from '@/api';
    export default {
        name: 'auth-admin',
        async mounted() {
            await this.getRoleList();
            await this.getDeptList()
            // // this.getAdminTypes();
        },
        components: {
            Treeselect,
        },
        data () {
            return {
                groupMap: {},
                value1: '1',
                value2: '2',
                indeterminateDept:true,
                checkAllDept:true,
                indeterminateRole:true,
                checkAllRole:true,
                deptList:[],
                roleList: [],
                roleGroup:[],
                deptGroup:[],
                curAdmin: {},
                searchParams: {
                    username: '',
                    nickname:'',
                },
                selectedAdmin: {},
                selectedAdmins:[],

                adminValidate: {
                    nickname:[
                        {required: true,message:"请输入登录名称", trigger: 'change'}
                    ],
                    username:[
                        {required: true,message:"请输入用户名", trigger: 'change'}
                    ]
                },
                columns: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    {
                        title: '用户名称',
                        key: 'nickName'

                    },

                    {
                        title:'登录名名称',
                        key:'userNo'
                    },
                    {
                        title:'角色',
                        key:'roleId',
                        render: (h, params) => {
                            let txt = '';
                            let that =this;
                            let roleIds = JSON.parse(params.row.roleIds);
                            if (roleIds) {
                                let item=''
                                roleIds.forEach(id=>{
                                    that.roleList.forEach(r=>{
                                        if (r.id==id){
                                            item=r.roleName+"|";
                                        }
                                    });
                                    txt += item
                                })
                                txt = txt.substring(0,txt.length-1)
                            }
                            return h('div', {props: {},}, txt);
                        }
                    },
                    {
                        title:'部门',
                        key:'deptId',
                        render: (h, params) => {
                            return h('div', {props: {},},  (this.groupMap[params.row.groupId]||{}).groupName||"");
                        }
                    },

                ],
                // adminTypeName:[],
                adminData: [],
                page : {
                    pageNum: 1,
                    pageSize: 10
                },
                adminModal: false,
            }
        },
        methods: {
            //角色折叠复选框
            handleCheckAllRole() {
                if (this.indeterminateRole) {
                    this.checkAllRole = false;
                } else {
                    this.checkAllRole = !this.checkAllRole;
                }
                this.indeterminateRole = false;
                if (this.checkAllRole) {
                    var that = this;
                    this.roleList.forEach(function (roleName, index) {
                        that.roleGroup.push(roleName.id);
                    });
                } else {
                    this.roleGroup = [];
                }
            },
            checkAllRoleChange(data) {
                if (data.length === this.roleList.length) {
                    this.indeterminateRole = false;
                    this.checkAllRole = true;
                } else if (data.length > 0) {
                    this.indeterminateRole = true;
                    this.checkAllRole = false;
                } else {
                    this.indeterminateRole = false;
                    this.checkAllRole = false;
                }
            },
            //部门折叠复选框
            handleCheckAllDept() {
                if (this.indeterminateDept) {
                    this.checkAllDept = false;
                } else {
                    this.checkAllDept = !this.checkAllDept;
                }
                this.indeterminateDept = false;
                if (this.checkAllDept) {
                    var that = this;
                    this.deptList.forEach(function (deptName, index) {
                        that.deptGroup.push(deptName.id);
                    });
                } else {
                    this.deptGroup = [];
                }
            },
            checkAllDeptChange(data) {
                if (data.length === this.deptList.length) {
                    this.indeterminateDept = false;
                    this.checkAllDept = true;
                } else if (data.length > 0) {
                    this.indeterminateDept = true;
                    this.checkAllDept = false;
                } else {
                    this.indeterminateDept = false;
                    this.checkAllDept = false;
                }
            },
            searchAdmin: function(){
                this.getAllAdmins(this.page.pageNum);
            },
            getAllAdmins: async function(index){
                let rel = await userService.getAllAdmins(index?index:0, this.page.pageSize,this.searchParams);
                if (rel.success) {
                    let allAdmins = rel.data;
                    this.adminData =allAdmins.list;
                    this.page =allAdmins;
                }else {
                    this.$Message.error("加载用户数据失败")
                }
            },
            getDeptList: async function () {
                let rel =await deptService.getDeptTree(0)
                if (rel.success) {
                    let list = rel.data;
                    this.deptList = list
                    this._foreachDept(list);
                    await this.getAllAdmins();
                }else {
                    this.$Message.error("加载部门数据失败")
                }
            },
            _foreachDept: function (dList) {
                dList.forEach((_d) => {
                    this.groupMap[_d.id] = _d;
                    if (_d.children) {
                        this._foreachDept(_d.children);
                    }
                })
            },
            getRoleList: async function () {
                let rel = await roleService.getAllRoles(0,0);
                if (rel.success) {
                    let roleList = rel.data;
                    this.roleList =roleList.list;
                }else {
                    this.$Message.error("加载角色数据失败")
                }
            },
            handleSelectAll (status) {
                this.selectedAdmins = [];
                this.$refs.selection.selectAll(status);
            },
            saveRoleUser(){
                let roleIds = this.roleGroup;
                let send =roleUserService.addRoleUsers(null,roleIds,this.curAdmin.id)
                    .then(response => {
                        this.$Message.info("保存成功");
                        this.handleSelectAll(false);
                        this.getAllAdmins(this.page.pageNum);
                    })
            },
            saveAdmin: function(){
                this.$refs.adminForm.validate((valid) => {
                    if (valid) {
                        this.curAdmin.roleIds = this.roleGroup.join(",");
                        // this.curAdmin.deptId = this.deptGroup.join(",");
                        // this.saveRoleUser();
                        delete this.curAdmin.createDate;
                        delete this.curAdmin.updateDate;
                        let send = this.curAdmin.id ? userService.update(this.curAdmin) : userService.add(this.curAdmin);
                        send.then(res => {
                            if (res.success){
                                this.$Message.info("保存成功");
                                this.adminModal = false;
                                this.handleSelectAll(false);
                                this.getAllAdmins(this.page.pageNum);
                            }else{
                                this.$Message.error(res.message);
                            }
                            this.$refs.adminModal.buttonLoading = false;
                        })
                    }else {
                        this.$Message.info('验证失败');
                        this.$refs.adminModal.buttonLoading = false;
                    }
                });
            },
            selectAdmin: function (selection) {
                console.info(selection);
                this.selectedAdmins = selection;
                if (selection.length == 1) {
                    this.selectedAdmin = selection[0];
                }
            },
            openNew: function () {
                this.curAdmin = {};
                this.roleGroup = [];
                this.adminModal = true
            },
            openEdit: function (index) {
                this.curAdmin = this.selectedAdmin;
                this.adminModal = true;
                this.roleGroup = JSON.parse(this.curAdmin.roleIds);
                console.info({roleGroup: this.roleGroup})
            },
            resetPwd: function () {
                let names = this.selectedAdmins.map(({nickName}) => nickName);
                this.$emit("confirm", {content: "是否要重置用户[" + names.join(",") + "]的密码，重置后需之前密码将自动失效，请谨慎操作！", callback: (flag) => {
                        if (flag) {
                            let ids = this.selectedAdmins.map(({id}) => id);
                            userService.resetPwd(ids)
                                .then(res => {
                                    if (res.success){
                                        this.$Message.success("重置成功");
                                        this.handleSelectAll(false);
                                        this.getAllAdmins(this.page.pageNum);
                                    }else{
                                        this.$Message.error(res.message);
                                    }
                                })
                        }
                    }});

            },
            show_delete_confirm: function () {
                console.info(this.$emit)
                this.$emit("confirm", {content: "是否要删除此用户？此操作不可恢复，请谨慎操作！", callback: (flag) => {
                        if (flag) {
                            let ids = this.selectedAdmins.map(({id}) => id);
                            userService.delete(ids.join(",")).then((data) => {
                                if (data.success) {
                                    this.$Message.success("删除成功")
                                    this.handleSelectAll(false);
                                    this.getAllAdmins(this.page.pageNum);
                                }else {
                                    this.$Message.error("删除失败," + data.message)
                                }
                            }).catch((error) => {
                                this.$Message.error("删除失败," + error)
                            });
                        }
                    }});
            }

        }
    }
</script>
