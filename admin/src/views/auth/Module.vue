<template>
        <Row style="height: 100%" >
            <Col span="6" style="height: 100%" >
                <Card style="padding-bottom: 70px; height: 100%">
                    <p slot="title">
                        <Icon type="android-options"></Icon>
                        菜单列表
                    </p>
                    <a href="#" slot="extra" @click.prevent="add">
                        <Icon type="md-add-circle" />
                        新增
                    </a>
                    <Tree :data="menuData" :render="renderContent" @on-check-change="treeSelect" :multiple="false" empty-text="没有数据"></Tree>
                </Card>
            </Col>
            <Col span="18" style="height: 100%"  >
                <Card style="height: 100%" >
                    <p slot="title">
                        <Icon type="ios-list-outline"></Icon>
                        菜单详情{{editType == 1 ? " - 新增" : (editType == 2 ? " - 修改" : "")}}
                    </p>
                    <Form :modal="curMenu" ref="curMenu"  :label-width="120" style="width: 500px">
                        <FormItem prop="menuName" label="菜单名称">
                            <Input  placeholder="菜单名称，必填项..." clearable
                                    v-model="curMenu.menuName" align="middle">
                            </Input>
                        </FormItem>
                        <FormItem prop="menuNo" label="菜单编码" style="width: 500px">
                            <Input placeholder="菜单编码，必填项..." clearable  v-model="curMenu.menuNo" :disabled="editType == 2"></Input>
                        </FormItem>
                        <FormItem prop="menuUrl" label="URL" style="width: 500px">
                            <Input  placeholder="菜单路径，可以为空..." clearable  v-model="curMenu.menuUrl" >
                            </Input>
                        </FormItem>
                        <FormItem prop="method" label="请求方式" style="width: 500px">
                            <Select placeholder="请选择请求方式..." v-model="curMenu.method">
                                <Option v-for="item in requestMethod" :value="item" :key="item">{{item}}</Option>
                            </Select>
                        </FormItem>
                        <FormItem prop="orderNum" label="排序号" style="width: 500px">
                            <Input  placeholder="菜单排序号，必填项..." clearable  v-model="curMenu.orderNum" >
                            </Input>
                        </FormItem>
                        <FormItem prop="parentId" label="上级菜单"  style="width: 500px">
                            <label>{{curMenu.parentName}}</label>
                        </FormItem>
                        <FormItem prop="menuType" label="菜单类型" style="width: 500px">
                            <Select placeholder="请选择菜单类型..." v-model="curMenu.menuType" :disabled="curMenu.id!=null">
                                <Option v-for="item in menuTypes" :value="item.value" :key="item.value">{{item.label}}</Option>
                            </Select>
                        </FormItem>
                        <FormItem prop="hasLock" label="是否加锁" style="width: 500px; display: none">
                            <input type="checkbox"  v-model="curMenu.hasLock" > &nbsp;&nbsp;<span style="font-size: 12px">(加锁后，只能进行单人操作)</span>
                        </FormItem>

                        <Row>
                            <Col span="24" align="middle" style="width: 500px">
                                <Button type="primary" @click="saveMenu" v-if="auth.save" :disabled="editType == 0" >保存</Button>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <Button type="primary" @click="deleteMenu" v-if="auth.del" :disabled="editType != 2" >删除</Button>
                            </Col>
                        </Row>
                    </Form>
                </Card>
            </Col>
        </Row>
</template>
<style>
</style>
<script>
    import {menuService} from '@/api';

    export default {
        name: 'auth-module',
        mounted () {
            this.getMenu();
        },
        components: {
        },
        data () {
            return {
                auth: {
                    save: true,
                    del: true
                },
                menuData: [],
                curMenu:{},
                menuTypes: [
                    {
                        value: '0',
                        label: '目录'
                    },
                    {
                        value: '1',
                        label: '菜单'
                    },
                    {
                        value: '2',
                        label: '按钮'
                    },
                    {
                        value: '3',
                        label: '内部资源'
                    },
                ],

                editType: 0,
                menuTreeSetting: {
                    callback: {
                        beforeClick: this.treeSelect
                    }
                },
                saveButton:false,
                requestMethod: ["GET", "HEAD", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "TRACE"],
                parentNodeMap: {}
            }
        },
        methods: {
            renderContent (h, { root, node, data }) {
                this.parentNodeMap['node_id' + data.id] = data.title;
                return h('span', {
                    on: {
                        click: () => {this.treeSelect(data)}
                    }
                }, [
                    h('Icon', {
                        props: {
                            type: data.children ? 'ios-folder-outline' : 'ios-document-outline'
                        },
                        style: {
                            marginRight: '8px'
                        }
                    }),
                    h('span', data.title)
                ])
            },
            initMenuTypes(node) {
                if (node) {
                    if (node.id == -1) {
                        this.menuTypes = [
                            {
                                value: '0',
                                label: '目录'
                            }
                        ]
                    }else if (node.menuType == 0) {
                        this.menuTypes = [
                            {
                                value: '1',
                                label: '菜单'
                            }
                        ]
                    }else if (node.menuType == 1) {
                        this.menuTypes = [
                            {
                                value: '1',
                                label: '菜单'
                            },
                            {
                                value: '2',
                                label: '按钮'
                            },
                            {
                                value: '3',
                                label: '内部资源'
                            },
                        ]
                    }else if (node.menuType == 2) {
                        this.menuTypes = [
                            {
                                value: '2',
                                label: '按钮'
                            },
                            {
                                value: '3',
                                label: '内部资源'
                            },
                        ]
                    } else if (node.menuType ==3) {
                        this.menuTypes = [
                            {
                                value: '3',
                                label: '内部资源'
                            }
                        ]
                    }
                } else {
                    this.menuTypes = [
                        {
                            value: '0',
                            label: '目录'
                        },
                        {
                            value: '1',
                            label: '菜单'
                        },
                        {
                            value: '2',
                            label: '按钮'
                        },
                        {
                            value: '3',
                            label: '内部资源'
                        },
                    ]
                }
            },
            add: function(){
                if (this.editType == 1) {
                    return;
                }
                let node = this.curMenu.$node;
                if(node){
                    this.editType = 1;
                    this.initMenuTypes(node)
                    this.saveButton=true;
                    this.curMenu = {
                        parentName: node.name,
                        parentId: node.id,
                        menuNo: node.menuNo,
                        menuType: "1",
                        method: this.requestMethod[0],
                        orderNum: (node.children || []).length + 1
                    };
                }else {
                    this.$Message.error("请选择树节点")
                    this.curMenu = {parentName: "请选择树节点"};
                }
            },
            getMenu: async function () {
                let rel = await menuService.getMenuTree(-1, 1);
                if (rel.success) {
                    let menus = rel.data;
                    this.menuData = [{id: -1, title: '模块管理', name: '模块管理', children: menus||[],expand: true, render: (h, { root, node, data }) => {
                            this.parentNodeMap['node_id' + data.id] = data.title;
                            return h('span',{
                                on: {
                                    click: () => {this.treeSelect(data)}
                                }
                            }, [
                                h('Icon', {
                                    props: {
                                        type: 'md-menu'
                                    },
                                    style: {
                                        marginRight: '8px'
                                    }
                                }),
                                h('span', data.title)
                            ])
                        }}];
                }else {
                    this.$Message.error("加载菜单数据失败")
                }
            },
            treeSelect: function (node) {
                if(this.curMenu.id == node.id)return;
                this.saveButton=true;
                this._setCurMenu(node);
                this.editType = 2;
                if (node.id == -1) {
                    this.add();
                }else {
                    this.initMenuTypes()
                }
            },
            _setCurMenu:function(node){
                console.info(this.parentNodeMap['node_id' + node.parentId] || "模块管理")
                this.curMenu = {
                    "id": node.id,
                    "menuName": node.menuName,
                    "parentId": node.parentId,
                    "menuNo": node.menuNo,
                    "menuUrl":node.menuUrl,
                    "orderNum": node.orderNum,
                    "menuType": node.menuType,
                    "hasLock": node.hasLock,
                    method: node.method,
                    parentName: this.parentNodeMap['node_id' + node.parentId] || "模块管理",
                    $node: node
                };
            },
            saveMenu: function () {
                let send = this.curMenu.id ? menuService.update(this.curMenu) : menuService.add(this.curMenu);
                send.then(response => {
                    if(response.success){
                        this.$Message.success("保存成功");
                        if(!this.curMenu.id){
                            this.curMenu.id = response.data.id;
                        }
                        this.getMenu();
                    }else {
                        this.$Message.error(response.msg);
                    }
                });
            },
            deleteMenu: function () {
                if(!this.curMenu.id){
                    return alert("请选择菜单！")
                }
                this.$emit("confirm",{title: '删除提示', content: "删除后将无法退回，确认删除此菜单吗?", callback: (flag) => {
                    if (!flag) {
                        return
                    }
                    menuService.delete(this.curMenu.id).then(response => {
                        if(response.success){
                            this.$Message.success("删除成功");
                            this._setCurMenu({});
                            // console.info(this.curMenu.$node.getParentNode())
                            this.getMenu();
                        }else {
                            this.$Message.error(response.message);
                        }
                    });
                }})
            }

        }
    }
</script>
<style>
    .ivu-card-body {
        height: 100%;
        overflow-y: auto;
    }
</style>
