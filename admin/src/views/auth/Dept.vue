<template>
    <Row style="height: 100%" >
        <Col span="6" style="height: 100%" >
            <Card style="padding-bottom: 70px;height: 100%" >
                <p slot="title">
                    <Icon type="android-options"></Icon>
                    {{title}}列表
                </p>
                <a href="#" slot="extra" @click.prevent="add">
                    <Icon type="md-add-circle" />
                    新增
                </a>
                <Tree :data="deptData" :render="renderContent" :multiple="false" empty-text="没有数据" ></Tree>
            </Card>
        </Col>
        <Col span="18" style="height: 100%">
            <Card  style="height: 100%" >
                <p slot="title">
                    <Icon type="ios-list-outline"></Icon>
                    {{title}}详情
                </p>
                <Form :modal="curDept" ref="curDept"  :label-width="120">
                    <FormItem prop="deptName" :label="title + '名称'">
                        <Input  :placeholder="title + '名称...'" clearable  v-model="curDept.deptName" :maxlength="20">
                        </Input>
                    </FormItem>

                    <FormItem prop="deptNo" :label="title + '编码'">
                        <Input :placeholder="title + '编码...'" clearable  v-model="curDept.deptNo" :disabled="curDept.id!=null" :maxlength="20"></Input>
                    </FormItem>
                    <FormItem prop="type" :label="title + '类型'" v-if="deptTypes.length > 0">
                        <Select :placeholder="'请选择' + title + '类型...'" v-model="curDept.type">
                            <Option v-for="item in deptTypes" :selected="curDept.type==item.value" :value="item.value" :key="item.value" >{{ item.label }}</Option>
                        </Select>
                    </FormItem>

                    <FormItem prop="deptNo" :label="'上级' + title">
                        <div>{{curDept.parentName}}</div>
                    </FormItem>
                    <Row>
                        <Col span="24" align="middle">
                            <Button type="primary" @click="saveDept" v-if="auth.save" :disabled="editType == 0||curDept.parentId==undefined" >保存</Button>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <Button type="primary" @click="deleteDept" v-if="auth.del" :disabled="editType != 2||curDept.parentId==undefined"  >删除</Button>
                        </Col>
                    </Row>
                </Form>
            </Card>
        </Col>
    </Row>
</template>
<style>
</style>
<script>
    import {deptService} from '@/api';

    export default {
        name: 'dept',
        mounted () {
            this.getDept();
        },
        components: {
        },
        data () {
            const deptNameValidate = (role,value,callback)=>{
                value=value.trim()?value.trim():value;
                if(value.length<1){
                    callback(new Error("请输入" + this.title + "名称"));
                }
                callback();
            };
            const deptNoValidate = (role,value,callback)=>{
                value=value.trim()?value.trim():value;
                if(value.length<1){
                    callback(new Error(this.title + "编码不允许重复"));
                }
                callback();
            };
            return {
                auth: {
                    save: true,
                    del: true
                },
                title: '镇街',
                deptTypes: [],
                deptData: [],
                curDept:{},
                deptValidate:{
                    deptName:[
                        { validator: deptNameValidate, trigger: 'change' },
                    ],
                    deptNo:[
                        { validator: deptNoValidate, trigger: 'change' },
                    ],
                },
                editType: 0,
                deptTreeSetting: {
                    callback: {
                        beforeClick: this.treeSelect
                    }
                },
                saveButton: false,
                parentNodeMap: {}
            }
        },
        methods: {
            renderContent (h, { root, node, data }) {
                this.parentNodeMap['node_id' + data.id] = data;
                return h('span', {
                    on: {
                        click: () => {this.treeSelect(data)}
                    }
                }, [
                    h('Icon', {
                        props: {
                            type: data.children ? 'ios-folder-outline' : 'ios-document-outline'
                        },
                        style: {
                            marginRight: '8px'
                        }
                    }),
                    h('span', data.title)
                ])
            },
            add: function(){
                let node = this.curDept.$node;
                if(node){
                    this.editType = 1;
                    this.curDept = {
                        parentName: node.name,
                        parentId: node.id
                    };
                }else {
                    this.curDept = {parentName: "请选择树节点"};
                }
            },
            getDept: async function () {
                let depts = await deptService.getDeptTree(0);
                if (depts.success) {
                    this.deptData = depts.data;
                }else {
                    this.$Message.error("加载部门数据失败")
                }
            },
            treeSelect: function (node) {
                this.editType = 2;
                if(this.curDept.id == node.id)return;
                this._setCurDept(node);
            },
            _setCurDept:function(node){
                let pNode = this.parentNodeMap['node_id' + node.parentId];
                this.curDept = {
                    "id": node.id,
                    "deptName": node.deptName,
                    "parentId": node.parentId,
                    "deptNo": node.deptNo,
                    "sortNum": node.sortNum,
                    "type": node.type,
                    parentName:  pNode ? pNode.title : "顶级" + this.title,
                    $node: node
                };
            },
            saveDept: function () {
                let send = this.curDept.id ? deptService.updateDept(this.curDept) : deptService.addDept(this.curDept);
                send.then(response => {
                    if(response.success){
                        this.$Message.success("保存成功");
                        if(!this.curDept.id){
                            this.curDept.id = response.data.id;
                        }
                        this.getDept();
                    }else {
                        this.$Message.error(this.title + "名称已存在！");
                    }
                });
            },
            deleteDept: function () {
                if(!this.curDept.id){
                    return alert("请选择上级" + this.title + "！")
                }
                this.$emit("confirm",{title: '删除提示', content: "删除后将无法退回，确认删除此" + this.title + "吗?", callback: (flag) => {
                    if (!flag) {
                        return
                    }
                    deptService.deleteDept(this.curDept.id).then(response => {
                        if(response.success){
                            this.$Message.success("删除成功");
                            this._setCurDept(this.parentNodeMap['node_id' + this.curDept.parentId]);
                            // console.info(this.curDept.$node.getParentNode())
                            this.getDept();
                        }else {
                            this.$Message.error(response.message);
                        }
                    })
                }})
            }

        }
    }
</script>
<style>
    .ivu-card-body {
        height: 100%;
        overflow-y: auto;
    }
</style>
