<template>
  <div style="height: 100%">
    <Row>
      <Col span="24">
        <Card>
          <p slot="title">
            <Icon type="ios-list-outline"></Icon>
            服务中心列表
          </p>
          <Row>
            <Col span="24">
              <Button type="primary" @click="openNew">新增</Button>
              <span @click="searchAdmin" style="margin: 0 10px;float: right"><Button type="primary" icon="search">搜索</Button></span>
              <Input v-model="searchParams.centerName" clearable placeholder="请输入站点名称" style="margin:0 10px;width: 200px; float: right" />
            </Col>
          </Row>
          <br>
          <Row>
            <Col span="24" align="middle">
              <Tabs v-model="page.centerType" @on-click="changeType">
                <TabPane :label="ct.title" :name="ct.cod" v-for="ct in centerTypes"></TabPane>
              </Tabs>
              <Table border ref="selection" :columns="columns" :data="centerData" @on-selection-change="selectAdmin"></Table>
              <br/>
              <Page  @on-change="getAllCenters" :total="parseInt(page.total)" :current="page.pageNum > 0 ? page.pageNum : 1" :page-size="page.pageSize"></Page>
            </Col>
          </Row>
        </Card>
      </Col>
    </Row>
    <Modal
        v-model="adminModal"
        :title="curCenter.id > 0 ? '修改' : '新增'"
        justify-content= "center"
        ref="adminModal"
        width="500">
      <Form ref="adminForm" :model="curCenter" :rules="centerValidate" :label-width="110" >
        <FormItem label="中心类型" prop="centerType">
          <Select v-model="curCenter.centerType" placeholder="请选择服务中心类型...">
            <Option v-for="item in centerTypes" :value="item.cod" :key="item.cod">{{ item.title }}</Option>
          </Select>
        </FormItem>
        <FormItem label="所属镇街" prop="centerType">
          <Select v-model="curCenter.townId" placeholder="请选择服务所属镇街...">
            <Option v-for="item in groups" :value="item.id" :key="item.id">{{ item.title }}</Option>
          </Select>
        </FormItem>
        <FormItem label="中心名称" prop="centerName">
          <Input v-model="curCenter.centerName" placeholder="请输入服务中心名称..." :clearable="true"></Input>
        </FormItem>
        <FormItem label="联系电话" prop="tel" v-if="centerTypeMap[curCenter.centerType] && centerTypeMap[curCenter.centerType].expandData && centerTypeMap[curCenter.centerType].expandData.tel">
          <Input v-model="curCenter.tel" placeholder="请输入联系电话..." :clearable="true"></Input>
        </FormItem>
        <FormItem label="工作时间" prop="tel">
          <Input v-model="curCenter.workTime" placeholder="请输入工作时间..." :clearable="true"></Input>
        </FormItem>
<!--        <FormItem label="负责人" prop="centerAdmins">-->
<!--          <Select v-model="centerAdmins" filterable multiple>-->
<!--            <Option  v-for="admin in adminList" :value="admin.userNo" :key="parseInt(admin.id)">{{admin.nickName}}</Option>-->
<!--          </Select>-->
<!--        </FormItem>-->
      </Form>
      <div slot="footer">
        <Button @click="adminModal = false">取消</Button>
        <Button type="primary" @click="saveAdmin">保存</Button>
      </div>
    </Modal>
    <center-project @close="projectModal = false" v-model="projectModal" :centerId="centerId" ></center-project>
  </div>
</template>
<style>
</style>
<script>
import CenterProject from "_c/center_project/index.vue";
import {userService, centerService, deptService} from '@/api';
export default {
  name: 'center',
  mounted() {
    this.getTypes();
    this.getGroups();
    // this.getAllAdmins();
  },
  components: {
    CenterProject
  },

  data () {
    return {
      centerId: null,
      projectModal: false,
      groupMap: {},
      value1: '1',
      value2: '2',
      indeterminateDept:true,
      checkAllDept:true,
      indeterminateRole:true,
      checkAllRole:true,
      deptList:[],
      adminList: [],
      centerAdmins:[],
      deptGroup:[],
      curCenter: {},
      searchParams: {
        centerName: '',
      },
      selectedAdmin: {},
      selectedAdmins:[],

      centerValidate: {
        centerName:[
          {required: true,message:"请输入服务中心名称", trigger: 'blur'},
        ],
      },
      columns: [
        {
          type: 'index',
          width: 60,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '所属镇街',
          width: 160,
          key: 'townName'
        },
        {
          title: '服务中心',
          width: 220,
          key: 'centerName'
        },
        {
          title: '类型',
          width: 140,
          render: (h, param) => {
            return h("div", this.centerTypeMap[param.row.centerType]?.title || param.row.centerType)
          }

        },
        {
          title:'所在位置',
          key:'addr',
          width: 340,
          ellipsis: true,
          tooltip: true
        },
        {
          title:'联系电话',
          width: 160,
          key:'tel'
        },
        {
          title:'工作时间',
          width: 280,
          key:'workTime'
        },
        {
          title: '操作',
          align:'center',
          width: 200,
          fixed: 'right',
          render: (h, params) => {
            let buttons = [];
              buttons.push(h('Button', {
                props: {
                  type: 'success',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.openEdit(params.row)
                  }
                }
              }, '修改'))
              buttons.push(h('Button', {
                props: {
                  type: 'success',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.show_delete_confirm(params.row.id)
                  }
                }
              }, '删除'))
            if (this.centerTypeMap[params.row.centerType] && this.centerTypeMap[params.row.centerType].expandData && this.centerTypeMap[params.row.centerType].expandData.project) {
              buttons.push(h('Button', {
                props: {
                  type: 'success',
                  size: 'small'
                },
                style: {
                  marginRight: '5px'
                },
                on: {
                  click: () => {
                    this.centerId = params.row.id;
                    this.projectModal = true;
                  }
                }
              }, '事项'))
            }
            return h('div', buttons);
          }
        }

      ],
      // adminTypeName:[],
      centerData: [],
      page : {
        pageNum: 0,
        pageSize: 10,
        centerType: null
      },
      adminModal: false,
      centerTypes: [],
      centerTypeMap: {},
      groups: []
    }
  },
  methods: {
    changeType(_type) {
      this.page.centerType = _type;
      this.getAllCenters(this.page.pageNum);
    },
    getTypes() {
      centerService.getType().then(res => {
        this.centerTypes = res.data;
        this.page.centerType = this.centerTypes[0].cod;
        this.centerTypeMap = {}
        for (let i = 0, len = res.data.length; i < len; i++) {
          let _d = res.data[i];
          _d.expandData = JSON.parse(_d.expandData)
          this.centerTypeMap[_d.cod] = _d;
        }
        this.getAllCenters(this.page.pageNum);
      })
    },
    getGroups() {
      deptService.getDeptTree(1).then(res => {
        console.info(res, "====group-===");
        this.groups = res.data;
      })
    },
    openNew() {
      this.curCenter = {centerType: this.page.centerType};
      this.centerAdmins = [];
      this.adminModal = true
    },
    getAllAdmins: function(){
      userService.getAllAdmins(0, 0).then(res => {
        let allAdmins = res.data;
        this.adminList =allAdmins.list;
      }).catch(error => {
        this.$Message.error("加载用户数据失败")
      })
    },
    getAllCenters(pageNum) {
      centerService.getPage({centerName: this.searchParams.centerName, pageNum, pageSize: this.page.pageSize, centerType: this.page.centerType} ).then(res => {
        console.info({res})
        let _data = res.data;
        this.centerData = _data.list || [];
        this.centerData.forEach(_center => {
            let _usernos = [];
            let _usernames = [];
            _center.adminList?.forEach(_admin => {
              _usernames.push(_admin.userName);
              _usernos.push(_admin.userNo)
            })
          _center.usernames = _usernames.join(",");
          _center.usernos = _usernos.join(",");
        })
        this.page = {..._data, centerType: this.page.centerType};
      })
    },
    searchAdmin() {
      this.getAllCenters(1)
    },
    show_delete_confirm(id) {
      this.$Modal.confirm({
        title: "确认删除吗？",
        onOk: () => {
          centerService.delete(id).then(res => {
            if (res.success) {
              this.$Message.success("删除成功");
              this.getAllCenters(this.page.pageNum);
            }else {
              this.$Message.error("删除服务中心数据失败，" + res.code + ":" + res.msg)
            }
          }).catch(error => {
            this.$Message.error("删除服务中心数据失败，" + error.code + ":" + error.msg)
          })
        }
      })
    },
    openEdit(_data) {
      this.curCenter = {..._data};
      delete this.curCenter.adminList
      this.centerAdmins = this.curCenter.usernos ? this.curCenter.usernos.trim().split(",") : [];
      console.info({ab: this.centerAdmins})
      this.adminModal = true
      // this.$refs.adminForm.reset();
    },
    selectAdmin() {

    },
    saveAdmin() {
      console.info({aaa:this.centerAdmins, d: this.curCenter})
      // if (!this.centerAdmins || this.centerAdmins.length == 0) {
      //   return this.$Message.error("请选择负责人")
      // }
      this.$refs.adminForm.validate((valid) => {
        if (valid) {
          // this.curCenter.usernos = this.centerAdmins.join(",");
          centerService.save(this.curCenter).then(res => {
            this.adminModal = false
            this.getAllCenters(this.page.pageNum);
          }).catch(error => {
            this.$Message.error("保存服务中心数据失败，" + error.code + ":" + error.msg)
          })
        }
      })
    }
  }
}
</script>
