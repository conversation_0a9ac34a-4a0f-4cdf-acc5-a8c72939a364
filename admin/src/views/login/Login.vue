<template>
    <Layout style="height: 100%; background-color: unset" class="main">
        <Content >
            <img src="./back.jpg" width="100%" height="100%" border="0" style="z-index: -999">
            <Card class="login">
                <p slot="title">
                    <Icon type="md-log-in" size="20" />
                    登录系统
                </p>

                <Form ref="login_form" label-colon :rules="loginRules" :model="loginForm">
                    <FormItem  prop="username">
                        <Input prefix="ios-contact" v-model="loginForm.username" placeholder="请输入登录名"></Input>
                    </FormItem>

                    <FormItem prop="password">
                        <Input :type="pwdType" v-model="loginForm.password" prefix="md-lock" placeholder="请输入登录密码">
                            <template slot="suffix">
                                <Icon type="md-eye" v-if="pwdShow" @click="changePwd(false)"></Icon>
                                <Icon type="md-eye-off" v-else @click="changePwd(true)"></Icon>
                            </template>
                        </Input>
                    </FormItem>

                    <Button type="primary" long style="height: 38px; font-size: 18px;" @click="login">登 录</Button>
                    <!--<FormItem>-->
                        <!--<Checkbox v-model="remandme" style="float: right">记住我</Checkbox>-->
                    <!--</FormItem>-->
                </Form>
            </Card>
        </Content>
        <Footer>
        </Footer>
    </Layout>
</template>

<script>
    import LoginUtil from "@/libs/LoginUtil";
    export default {
        name: "Login",
        data() {
            return {
                remandme: true,
                pwdShow: false,
                pwdType: 'password',
                checkNum: 10,
                backgroundImage: "http://127.0.0.1/back1.jpg",
                backgrounds: [
                    'http://127.0.0.1/back1.jpg',
                    'http://127.0.0.1/back2.jpg',
                    'http://127.0.0.1/back3.jpg',
                ],
                backgroundImageIndex: 0,
                powerIsCheck: false,
                powerClickStyle: {},
                loginForm: {},
                loginRules: {
                    username: [
                        { required: true, message: '登录名不能为空', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '登录密码不能为空.', trigger: 'blur' },
                        { type: 'string', min: 6, message: '密码不能少于6位', trigger: 'blur' }
                    ]
                }
            }
        },
        methods: {
            setSetBackground() {
                setInterval(() => {
                    if (this.backgroundImageIndex > 2){
                        this.backgroundImageIndex = 0;
                    }
                    this.backgroundImage =  this.backgrounds[this.backgroundImageIndex];
                    this.backgroundImageIndex++;
                }, 5000);
            },
            check() {
                this.powerIsCheck = true;
            },
            changePwd(flag) {
                this.pwdShow = flag;
                this.pwdType = flag ? "text" : 'password';
            },
            login() {
                this.$refs['login_form'].validate((valid) => {
                    if (valid) {
                        LoginUtil.login({...this.loginForm}, (data)=> {
                            this.$Message.success('登录成功!');
                            this.$router.push("/home");
                        }, (error)=> {
                            this.$Message.error("登录失败，请检查用户名密码后重试");
                        });
                    } else {
                        this.$Message.error('Fail!');
                    }
                })
                // this.$router.push("/home")
            }
        },
        mounted() {
            let num = Math.random()*85;
            let _flag = Math.random() - 0.5 > 0;
            this.powerClickStyle = {
                left: ((_flag ? 205 : 0) + num) + "px"
            }
        }
    }
</script>

<style scoped>
.login {
    width: 300px;
    height: 260px;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto 60%;
}
.check-power {
    position: absolute;
    top: 3px;
    cursor: pointer;
}
    .check-power-parent-no {
        border: 1px solid #dcdee2;
        color: rgb(197, 197, 197);
        position: relative;
        border-radius: 5px;
        font-size: 16px;
        text-align: center
    }
    .check-power-parent-yes {
        border: 1px solid #3c9a15;
        position: relative;
        border-radius: 5px;
        text-align: center;
        color: #3c9a15;
        font-size: 16px;
        font-weight: bold;
        background-color: #a6f1b1;
    }
</style>
