const baseUrl = "/ngs-api"
module.exports = {
    // ...
    rest : {
        sso: {
            LOGIN :  baseUrl + "/oauth/token",
            refreshToken :  baseUrl + "/token/refresh",
        },
        dept:{
            value : baseUrl + '/dept',
            tree: baseUrl + '/dept/tree'
        },
        role:{
            value : baseUrl + '/role',
            roleUser : baseUrl + '/roleUser',
            value2: baseUrl + '/role/selectRoleNo',
            roleReMenu: baseUrl + '/role/menus'
        },
        user:{
            value: baseUrl + '/user',
            resetPwd: baseUrl + '/user/resetPwd',
            updatePwd: baseUrl + '/user/updatePwd',
            validate: baseUrl + '/user/validate'
        },
        roleUser:{
            value: baseUrl + '/roleuser',
            values: baseUrl + '/roleuser/users',
            value2: baseUrl + '/roleuser/roles',
        },
        menu:{
            tree: baseUrl + '/menu/tree',
            value: baseUrl + '/menu'
        },
        consult: {
            value:  baseUrl + "/consult",
            dept: baseUrl + "/bizDept"
        },
        consultDept : {
            value: baseUrl + '/consultDept'
        },
        bizUser : {
            value: baseUrl + '/bizUser'
        },
        center : {
            value: baseUrl + '/center'
        },
        message : {
            value: baseUrl + '/message'
        },
        file: {
            upload: baseUrl + '/file/upload'
        }
    },
    key: {
        cookie: {
            token: 'access_token',
            refreshToken: 'refresh_token',
            expires_in: 'expires_in',
            nickname: 'nickname'
        }
    },
    page: {
        login: 'login',
        home: 'home',
    },

}
