import Vue from 'vue'
import VueRouter from 'vue-router'
import sysRoutes from './system'
import View<PERSON> from 'view-design';
import LoginUtil from "@/libs/LoginUtil";
import config from '@/config'

Vue.use(ViewUI)
Vue.use(VueRouter)

const routes = [...sysRoutes]

let LOGIN_PAGE_NAME = config.page.login;
let HOME_PAGE_NAME = config.page.home;

const router = new VueRouter({
  routes,
  base:'/ngs-boss',
  mode: 'history'
})
router.beforeEach((to, from, next) => {
  ViewUI.LoadingBar.start();
  let token = LoginUtil.getToken();
  console.info(token)
  if (!token && to.name !== LOGIN_PAGE_NAME) {
    // 未登录且要跳转的页面不是登录页
    next({
      name: LOGIN_PAGE_NAME // 跳转到登录页
    })
  } else if (!token && to.name === LOGIN_PAGE_NAME) {
    // 未登陆且要跳转的页面是登录页
    next() // 跳转
  } else if (token && to.name === LOGIN_PAGE_NAME) {
    // 已登录且要跳转的页面是登录页
    next({
      name: HOME_PAGE_NAME // 跳转到homeName页
    })
  } else {
    next()
  }
})

router.afterEach(to => {
  ViewUI.LoadingBar.finish()
  window.scrollTo(0, 0)
  document.title = process.env.NODE_ENV === 'production' ? '数智“就近办”后台管理系统' : 'demo'
})

export default router
