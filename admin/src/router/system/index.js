
import Main from "@/views/main/Main";

const routes = [
  {
    path: '/',
    component: Main,
    redirect:'/home',
    children: [
      {
        path: '/home',
        name: 'home',
        component: () => import('@/views/Welcome.vue')
      },
      {
        path: '/about',
        name: 'About',
        component: () => import('@/views/About.vue')
      },
      // {
      //   path: '/module',
      //   name: 'module',
      //   code: '10011',
      //   component: () => import('@/views/auth/Module.vue')
      // },
      {
        path: '/dept',
        name: 'dept',
        code: '10013',
        component: () => import('@/views/auth/Dept.vue')
      },
      {
        path: '/admin',
        name: 'admin',
        code: '10012',
        component: () => import('@/views/auth/Admin.vue')
      },
      // {
      //   path: '/role',
      //   name: 'role',
      //   code: '100104',
      //   component: () => import('@/views/auth/Role.vue')
      // }
    ]
  },{
    path: '/ngs',
    component: Main,
    code: '1003',
    redirect: '/ngs/center',
    children: [
      {
        path: '/ngs/center',
        name: 'Center',
        code: '100301',
        component: () => import('@/views/center/Center.vue')
      },
      {
        path: '/ngs/center/owner',
        name: 'CenterOwner',
        code: '100302',
        component: () => import('@/views/owner_center/OwnerCenter.vue')
      },
      {
        path: '/ngs/message',
        name: 'Message',
        code: '100303',
        component: () => import('@/views/message/Message.vue')
      },
      {
        path: '/ngs/owner-message',
        name: 'OwnerMessage',
        code: '100304',
        component: () => import('@/views/message/MessageAdd.vue')
      },
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/Login.vue')
  }
]

export default routes
