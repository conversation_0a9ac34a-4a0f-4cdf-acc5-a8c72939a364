
import RestUtil from '@/libs/RestUtil'

import config from '@/config'

class RoleService {

    constructor(){
    }

    async getAllRoles(pageIndex, pageSize,params) {
        let rel = await RestUtil.getInstance().get(config.rest.role.value, {pageIndex: pageIndex, pageSize: pageSize, ...params});
        if (rel) {
            return rel;
        }
    }
    addRole({id, roleName}){
        return RestUtil.getInstance().post(config.rest.role.value,{id, roleName});
    }
    updateRole({id, roleName}){
        return RestUtil.getInstance().put(config.rest.role.value,{id, roleName});
    }
    saveRoleAuth(params){
        return RestUtil.getInstance().post(config.rest.role.roleReMenu,params);
    }
    deleteRole(id){
        return RestUtil.getInstance().delete(config.rest.role.value,{id: id});
    }
    async selectRoleNo(roleNo){
        let role =await RestUtil.getInstance().get(config.rest.role.value2,roleNo)
        return role
    }
    async getAuthByRoleId(roleId) {
        let rel = await RestUtil.getInstance().get(config.rest.role.roleReMenu, {roleId: roleId});
        if (rel) {
            return rel;
        }
    }


}
export default RoleService

