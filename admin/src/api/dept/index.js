
import RestUtil from '@/libs/RestUtil'

import config from '@/config'

class DeptService {

    constructor(){
    }

    async getDeptTree(parentId) {
        // console.log(config.rest.dept.tree+ "/"+parentId)
        let rel = await RestUtil.getInstance().get(config.rest.dept.tree+ `/${parentId}`);
        if(rel){
            return rel;
        }
    }

    addDept(params){
        return RestUtil.getInstance().post(config.rest.dept.value,params);
    }
    updateDept(params){
        return RestUtil.getInstance().put(config.rest.dept.value,params);
    }
    deleteDept(id){
        return RestUtil.getInstance().delete(config.rest.dept.value,{id: id});
    }

}
export default DeptService

