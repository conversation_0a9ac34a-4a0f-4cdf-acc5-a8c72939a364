
import RestUtil from '@/libs/RestUtil'

import config from '@/config'

class UserService {

    constructor(){
    }
    async getAllAdmins(pageIndex, pageSize,params) {
        let rel = await RestUtil.getInstance().get(config.rest.user.value, {pageIndex: pageIndex, pageSize: pageSize, ...params});
        if(rel){
            return rel;
        }
    }
    add(params){
        return RestUtil.getInstance().post(config.rest.user.value,params);
    }
    update(params){
        return RestUtil.getInstance().put(config.rest.user.value,params);
    }
    delete(ids){
        return RestUtil.getInstance().delete(config.rest.user.value,{ids});
    }
    resetPwd(ids){
        return RestUtil.getInstance().put(config.rest.user.resetPwd,{ids: JSON.stringify(ids)});
    }
    updatePwd(oldPass,newPass){
        return RestUtil.getInstance().post(config.rest.user.updatePwd,{oldPass: oldPass,newPass:newPass});
    }
    validate(pass){
        return RestUtil.getInstance().get(config.rest.user.validate,{pass: pass});
    }
}
export default UserService

