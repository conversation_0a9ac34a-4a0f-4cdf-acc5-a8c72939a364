import RestUtil from '@/libs/RestUtil'
import config from "@/config";


class ConsultService {

    constructor(){
    }

    async getConsultPage(consult, pageIndex, pageSize) {
        let rel = await RestUtil.getInstance().get(config.rest.consult.value, {consultHead: consult, pageIndex, pageSize});
        return rel;
    }

    async getConsultById(id) {
        let rel = await RestUtil.getInstance().get(`${config.rest.consult.value}/${id}`);
        return rel;
    }
    async getConsultLines(id) {
        let rel = await RestUtil.getInstance().get(`${config.rest.consult.value}/${id}/lines`);
        return rel;
    }

    async add(consult, lines) {
        let rel = await RestUtil.getInstance().post(config.rest.consult.value, {...consult, linesJson: lines});
        return rel;
    }

    async update(consult, lines) {
        let rel = await RestUtil.getInstance().put(config.rest.consult.value, {...consult, linesJson: lines});
        return rel;
    }

    async publish(consult, lines) {
        let rel = await RestUtil.getInstance().put(`${config.rest.consult.value}/publish`, {...consult, linesJson: lines});
        return rel;
    }

    async getDeptPage(consult) {
        let rel = await RestUtil.getInstance().get(config.rest.consult.dept, {pageIndex: 0, pageSize: 0});
        return rel;
    }
    async getUserPage() {
        let rel = await RestUtil.getInstance().get(config.rest.bizUser.value + '/all', {pageNum: 0, pageSize: 0});
        return rel;
    }

    deleteConsult(ids) {
        let rel =  RestUtil.getInstance().delete(`${config.rest.consult.value}`, {ids});
        return rel;
    }

}
export default ConsultService

