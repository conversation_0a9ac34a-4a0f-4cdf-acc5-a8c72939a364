import RestUtil from '@/libs/RestUtil'
import config from "@/config";


class ConsultDeptService {

    constructor(){
    }

    async getConsultPeriod(consultId) {
        let rel = await RestUtil.getInstance().get(`${config.rest.consultDept.value}/period/${consultId}`);
        return rel;
    }

    async getConsultDept(periodId) {
        let rel = await RestUtil.getInstance().get(`${config.rest.consultDept.value}/dept/${periodId}`);
        return rel;
    }
    async getConsultResult(periodId) {
        let rel = await RestUtil.getInstance().get(`${config.rest.consultDept.value}/result/${periodId}`);
        return rel;
    }
    async getConsultResultLines(resultId) {
        let rel = await RestUtil.getInstance().get(`${config.rest.consultDept.value}/result/line/${resultId}`);
        return rel;
    }
    async getConsultResultCount(period) {
        let rel = await RestUtil.getInstance().get(`${config.rest.consultDept.value}/result/count/${period}`);
        return rel;
    }

    saveChangeResult(result) {
        return RestUtil.getInstance().post(`${config.rest.consultDept.value}/result`, {...result});
    }

    exportDetail(periodId, consultTitle) {
        RestUtil.getInstance().download(`${config.rest.consultDept.value}/exportDetail/${periodId}`, {}, `${consultTitle}.xlsx`)
    }
    exportMsgDetail(periodId, consultTitle) {
        RestUtil.getInstance().download(`${config.rest.consultDept.value}/exportMsgDetail/${periodId}`, {}, `${consultTitle}-意见.xlsx`)
    }

}
export default ConsultDeptService

