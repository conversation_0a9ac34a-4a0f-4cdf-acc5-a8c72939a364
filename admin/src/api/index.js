import SsoService from "./sso";
import UserService from "./admin";
import BaseModuleService from "./baseModule";
import DeptService from "./dept";
import DictService from "./dict";
import MenuService from "./module";
import RoleService from "./role";
import RoleUserService from "./roleUser";
import ConsultService from "./consult";
import ConsultDeptService from "./consultDept";
import BizUserService from "@/api/bizUser";
import CenterService from "@/api/center";
import MessageService from "@/api/message";

const ssoService = new SsoService();
const userService = new UserService();
const baseModuleService = new BaseModuleService();
const deptService = new DeptService();
const dictService = new DictService();
const menuService = new MenuService();
const roleService = new RoleService();
const roleUserService = new RoleUserService();
const consultService = new ConsultService();
const consultDeptService = new ConsultDeptService();
const bizUserService = new BizUserService();

const centerService = new CenterService();
const messageService = new MessageService();

export {
    ssoService,
    userService,
    baseModuleService,
    deptService,
    dictService,
    menuService,
    roleService,
    roleUserService,
    consultService,
    consultDeptService,
    bizUserService,
    centerService,
    messageService,
}
