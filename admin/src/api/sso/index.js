import RestUtil from '@/libs/RestUtil'
import config from "@/config";
import qs from 'qs'
import axios from 'axios'


class SsoService {

    constructor(){
    }

    async login(params) {
        let par = qs.stringify({...params, "grant_type": "password"});
        return await axios.post(config.rest.sso.LOGIN, par, {auth: {
                username: 'zaxq',
                password: 'zaxq'
            }}
        )
    }

    async refreshToken(refreshToken) {
        let rel = await RestUtil.getInstance().post(config.rest.sso.refreshToken, {refreshToken:refreshToken});
        return rel;
    }

    async exit() {
        let rel = await RestUtil.getInstance().post(config.rest.sso.EXIT);
        return rel.success;
    }

    async checkLogin(){
        let rel = await RestUtil.getInstance().get(config.rest.sso.LOGIN);
        return rel.success;
    }
    async getAuthTree(parentId) {
        let rel = await RestUtil.getInstance().get(config.rest.sso.auth+ "/" + (parentId || -1));
        return rel;
    }


}
export default SsoService

