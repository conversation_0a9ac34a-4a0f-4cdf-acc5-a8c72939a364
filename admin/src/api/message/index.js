import RestUtil from '@/libs/RestUtil'
import config from "@/config";


class MessageService {

    constructor(){
    }

    async getPage(title, state, pageNum, pageSize) {
        let rel = await RestUtil.getInstance().get(config.rest.message.value, {title, state, pageNum, pageSize});
        return rel;
    }

    async getOwnerPage(title, state, pageNum, pageSize) {
        let rel = await RestUtil.getInstance().get(`${config.rest.message.value}/owner`, {title, state, pageNum, pageSize});
        return rel;
    }
    async getMessage(id) {
        let rel = await RestUtil.getInstance().get(`${config.rest.message.value}/${id}`);
        return rel;
    }

    async importFile(file) {
        let rel = await RestUtil.getInstance().postFile(`${config.rest.bizUser.value}/import`, file);
        return rel;
    }

    async save(bizUser) {
        let rel = await RestUtil.getInstance().post(config.rest.message.value, bizUser);
        return rel;
    }
    async audit(ids, state) {
        let rel = await RestUtil.getInstance().post(`${config.rest.message.value}/audit`, {ids, state});
        return rel;
    }

    delete(id) {
        let rel =  RestUtil.getInstance().delete(`${config.rest.message.value}`, {id});
        return rel;
    }

}
export default MessageService

