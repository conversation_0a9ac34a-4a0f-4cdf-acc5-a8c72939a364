import RestUtil from '@/libs/RestUtil'
import config from "@/config";


class BizUserService {

    constructor(){
    }

    async getBizUserPage(username, pageNum, pageSize) {
        let rel = await RestUtil.getInstance().get(config.rest.bizUser.value, {username, pageNum, pageSize});
        return rel;
    }

    async importFile(file) {
        let rel = await RestUtil.getInstance().postFile(`${config.rest.bizUser.value}/import`, file);
        return rel;
    }

    async add(bizUser) {
        let rel = await RestUtil.getInstance().post(config.rest.bizUser.value, {...bizUser});
        return rel;
    }

    async update(bizUser) {
        let rel = await RestUtil.getInstance().put(config.rest.bizUser.value, {...bizUser});
        return rel;
    }

    deleteBizUser(ids) {
        let rel =  RestUtil.getInstance().delete(`${config.rest.bizUser.value}`, {ids});
        return rel;
    }

}
export default BizUserService

