import RestUtil from '@/libs/RestUtil'

import config from '@/config'

class DictService {

    constructor(){
    }

    async getAllList(pageIndex, pageSize,params) {
        let rel = await RestUtil.getInstance().get(config.rest.dict.value, {pageIndex: pageIndex, pageSize: pageSize, ...params});
        if(rel){
            return rel;
        }
    }
    getUserType() {
        return  RestUtil.getInstance().get(config.rest.dict.type);
    }
}
export default DictService

