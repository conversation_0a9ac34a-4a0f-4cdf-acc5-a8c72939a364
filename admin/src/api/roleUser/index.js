import config from "../../config";

import RestUtil from '@/libs/RestUtil'

class RoleUserService {

    constructor(){
    }
    async getRoleUser(roleId) {
        let rel = await RestUtil.getInstance().get(config.rest.roleUser.values+`/${roleId}`);
        if (rel) {
            return rel;
        }
    }
    getRoleUserByUserId(userId) {
        return  RestUtil.getInstance().get(config.rest.roleUser.value2+`/${userId}`);

    }

    addRoleUsers(roleId,ids,userId){
       return RestUtil.getInstance().post(config.rest.roleUser.value,{roleId,ids:JSON.stringify(ids),userId})
    }

}
export default RoleUserService
