
import RestUtil from '@/libs/RestUtil'

import config from '@/config'

class MenuService {

    constructor(){
    }

    async getMenuTree(parentId, type) {
        let rel = await RestUtil.getInstance().get(config.rest.menu.tree+ `/${parentId}`, {type: type});
        if(rel){
            return rel;
        }
    }
    add(params){
        return RestUtil.getInstance().post(config.rest.menu.value,params);
    }
    update(params){
        return RestUtil.getInstance().put(config.rest.menu.value,params);
    }
    delete(ids){
        return RestUtil.getInstance().delete(config.rest.menu.value,{ids: JSON.stringify(ids)});
    }


}
export default MenuService
