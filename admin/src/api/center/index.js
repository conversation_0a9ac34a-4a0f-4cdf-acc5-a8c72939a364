import RestUtil from '@/libs/RestUtil'
import config from "@/config";


class CenterService {

    constructor(){
    }

    async getPage(params) {
        let rel = await RestUtil.getInstance().get(config.rest.center.value, params);
        return rel;
    }
    async getType() {
        let rel = await RestUtil.getInstance().get(`${config.rest.center.value}/type`);
        return rel;
    }

    async getOwner() {
        let rel = await RestUtil.getInstance().get(`${config.rest.center.value}/owner`);
        return rel;
    }

    async getPojectList(centerId) {
        let rel = await RestUtil.getInstance().get(`${config.rest.center.value}/project/${centerId}`);
        return rel;
    }

    async importFile(file) {
        let rel = await RestUtil.getInstance().postFile(`${config.rest.bizUser.value}/import`, file);
        return rel;
    }

    async save(bizUser) {
        let rel = await RestUtil.getInstance().post(config.rest.center.value, bizUser);
        return rel;
    }
    async saveProject(centerProject) {
        let rel = await RestUtil.getInstance().post(`${config.rest.center.value}/project`, centerProject);
        return rel;
    }


    delete(id) {
        let rel =  RestUtil.getInstance().delete(`${config.rest.center.value}`, {id});
        return rel;
    }

    deleteProject(id) {
        let rel =  RestUtil.getInstance().delete(`${config.rest.center.value}/project/${id}`);
        return rel;
    }

}
export default CenterService

