import RestUtil from '@/libs/RestUtil'

import config from '@/config'

class BaseModuleService {

    constructor(){
    }

    async getAll(params) {
        let rel = await RestUtil.getInstance().get(config.rest.baseModule.value, params);
        if(rel){
            return rel;
        }
    }
    async getCode(params) {
        let rel = await RestUtil.getInstance().get(config.rest.baseModule.code, params);
        if(rel){
            return rel;
        }
    }

}
export default BaseModuleService

