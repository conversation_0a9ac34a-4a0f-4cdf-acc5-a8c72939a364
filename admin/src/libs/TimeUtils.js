// 时间转化
const timeFormat = function  (time) {
  let d = new Date(time)
  let year = d.getFullYear() // 年
  let month = d.getMonth() + 1 // 月
  let day = d.getDate() // 日
  let hh = d.getHours() // 时
  let mm = d.getMinutes() // 分
  let ss = d.getSeconds() // 秒
  let clock = year + '-'
  if (month < 10) clock += '0'
  clock += month + '-'
  if (day < 10) clock += '0'
  clock += day + ' '
  if (hh < 10) clock += '0'
  clock += hh + ':'
  if (mm < 10) clock += '0'
  clock += mm + ':'
  if (ss < 10) clock += '0'
  clock += ss
  return (clock)
}
// 时间转化
const dateFormat= function  (time) {
  let d = new Date(time)
  let year = d.getFullYear() // 年
  let month = d.getMonth() + 1 // 月
  let day = d.getDate() // 日
  let clock = year + '-'
  if (month < 10) clock += '0'
  clock += month + '-'
  if (day < 10) clock += '0'
  clock += day
  return (clock)
}

// 时间转化
const minuteFormat = function  (time) {
  if (!time) {
    return "";
  }
  let d = new Date(time)
  let year = d.getFullYear() // 年
  let month = d.getMonth() + 1 // 月
  let day = d.getDate() // 日
  let hh = d.getHours() // 时
  let mm = d.getMinutes() // 分
  let ss = d.getSeconds() // 秒
  let clock = year + '-'
  if (month < 10) clock += '0'
  clock += month + '-'
  if (day < 10) clock += '0'
  clock += day + ' '
  if (hh < 10) clock += '0'
  clock += hh + ':'
  if (mm < 10) clock += '0'
  clock += mm
  return (clock)
}
// 时间转化
const dateMonthFormat = function  (time) {
  let d = new Date(time)
  let year = d.getFullYear() // 年
  let month = d.getMonth() + 1 // 月
  let clock = year + '年'
  if (month < 10) clock += '0'
  clock += month +'月'
  return (clock)
}
//两个时间相差天数
const datedifference= function  (sDate1, sDate2) {
  let dateSpan,iDays;
  // sDate1 = Date.parse(sDate1);
  // sDate2 = Date.parse(sDate2);
  dateSpan = sDate2 - sDate1;
  dateSpan = Math.abs(dateSpan);
  iDays = Math.floor(dateSpan / (24 * 3600 * 1000));
  return iDays
}
//获取当天最后一秒时间，23:59:59
const getDayLastTimeStr= function  (date){
  return dateFormat(date)+" 23:59:59"
}
//获取这天在的月份的最后一天凌晨 2019-03-31 00:00:00
const getMonthLastDay= function(currentDate){
  let month = currentDate.getMonth()
  //获取到下一个月，++currentMonth表示本月+1，一元运算
  let nextMonth=++month;
  //获取到下个月的第一天
  let nextMonthFirstDay=new Date(currentDate.getFullYear(),nextMonth,1);
  //一天时间的毫秒数
  let oneDay=1000*60*60*24;

  let lastDay = timeFormat(nextMonthFirstDay-oneDay);
  return lastDay;
}
//获取这天在的月份的最后一天晚上 2019-03-31 23:59:59
const getMonthLastDayLastTime= function(currentDate){
  let month = currentDate.getMonth()
  //获取到下一个月，++currentMonth表示本月+1，一元运算
  let nextMonth=++month;
  //获取到下个月的第一天
  let nextMonthFirstDay=new Date(currentDate.getFullYear(),nextMonth,1);
  //一秒的毫秒数
  let oneDay=1000;

  let lastDay = timeFormat(nextMonthFirstDay-oneDay);
  return lastDay;
}
const isNotEmpty=function(val){
  if(val){
    return true;
  }
  return false;
}
export  {
  timeFormat,
  dateFormat,
  minuteFormat,
  dateMonthFormat,
  datedifference,
  getDayLastTimeStr,
  getMonthLastDay,
  getMonthLastDayLastTime,
  isNotEmpty
}
