import axios from 'axios'
import qs from 'qs'
import LoginUtil from './LoginUtil'

/**
 * http协议工具类
 */
class RestUtil {
    constructor({store, userToken}) {
        this.$ajax = axios;
        this.$store = store;
        this.$userToken = userToken === false ? false : true;//是否添加access_token到请求中
        this.$ajax.defaults.retry = 1; //重试次数
        this.$ajax.defaults.retryDelay = 500;//重试延时
        this.$ajax.defaults.shouldRetry = (error) => {
            if (error.status === 900) {
                return true;
            }
            return false;
        };//重试条件，默认只要是错误都需要重试
        //请求拦截器
        this.$ajax.interceptors.request.use(config => {
            let access_token = LoginUtil.getToken();
            if (access_token && this.$userToken) {
                config.headers = config.headers || {};
                config.headers.Authorization = "Bearer " + access_token;
            }
            console.info({config: config, params: config.params, store: this.$store, access_token: access_token});
            return config
        }, err => {
            return Promise.reject(err);
        })
        //返回拦截器
        this.$ajax.interceptors.response.use(res => {
            console.info(res);
            res.config.__retryCount = 0;
            return res.data;
        }, err => {
            console.info(err.response);
            //判断重试
            let retry = this._retry(err);
            if (retry) return retry;
            //判断登录
            this._login(err);
            return Promise.reject(err);
        })
        this.instance = null
    }

    _retry(err) {
        let config = err.config;
        config.__retryCount = config.__retryCount || 0;
        if (err.response.status === 900 && config.__retryCount < config.retry) {
            config.__retryCount += 1;
            //延时处理
            let backoff = new Promise(function (resolve) {
                setTimeout(function () {
                    resolve();
                }, config.retryDelay || 1);
            });

            //重新发起axios请求
            return backoff.then(() => {
                return this.$ajax(config);
            });
        }
        return null;
    }

    _login(err) {
        if (err.response.status == 401) {
            LoginUtil.logout();
            console.info(err)
            location.href = location.href;
        }
    }

    static getInstance(config) {
        config = config || {}
        let store = config.store;
        let userToken = config.userToken === false ? false : true;
        if (!this.instance) {
            this.instance = new RestUtil(config);
        }
        if (store) {
            this.instance.$store = store;
        }
        this.instance.$userToken = userToken;
        return this.instance
    }

    static getAxios() {
        return getInstance().$ajax;
    }

    async post(url, data, headers) {
        console.info(headers);
        let par = data ? qs.stringify(data) : '';
        return this.$ajax.post(url, par, {headers: headers || {}}
        ).then((response) => {
            return response;
        }).catch(error => {
            console.info(error);
            return error;
        })
    }

    /**
     * @deprecated
     * 此方法过期，建议使用upload方法代替
     */
    async postFile(url, data, headers) {
        console.info(headers);
        // let par = data?qs.stringify(data):'';
        return this.$ajax.post(url, data, {headers: headers || {}}
        ).then((response) => {
            return response;
        }).catch(error => {
            console.info(error);
            return error;
        })
    }

    async put(url, data) {
        let par = data ? qs.stringify(data) : '';
        return this.$ajax.put(url, par
        ).then((response) => {
            return response;
        }).catch(error => {
            return error;
        })
    }

    async get(url, data) {
        return this.$ajax.get(url, {
            params: data,
            maxRedirects: 10,
            timeout: 50000
        }).then((response) => {
            return response;
        }).catch(error => {
            return error;
        })
    }

    async delete(url, data) {
        return this.$ajax.delete(url, {
            params: data
        }).then((response) => {
            return response;
        }).catch(error => {
            return error;
        })
    }

    async upload(url, data, headers) {
        console.info(headers);
        // let par = data?qs.stringify(data):'';
        return this.$ajax.post(url, data, {headers: headers || {}}
        ).then((response) => {
            return response;
        }).catch(error => {
            console.info(error);
            return error;
        })
    }

    download(url, data, fileName) {
        return this.$ajax.get(url, {
            params: data,
            maxRedirects: 10,
            timeout: 50000,
            responseType: 'blob'
        }).then((response) => {
            // let blob = new Blob([response]); //创建一个blob对象
            console.info({blobResponse: response})
            let a = document.createElement('a'); //创建一个<a></a>标签
            a.href = URL.createObjectURL(response); // response is a blob
            if (!fileName) {
                fileName = response.headers["content-disposition"];
                fileName = fileName.split("filename=")[1];
            }
            a.download = fileName;  //文件名称
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            a.remove();
            return response;
        }).catch(error => {
            return error;
        })
    }

}

export default RestUtil
