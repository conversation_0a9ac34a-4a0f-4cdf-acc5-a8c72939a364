import {ssoService} from "@/api";
import Cookies from 'js-cookie';

import config from '@/config'

/**
 * 授权相关帮助类
 * @type {{setToken(*=): void, getToken(): void, login(*=, *=, *=): Promise<void>}}
 */
const LoginUtil = {
  refreshTimeout : null,
  buttonsGroupMenu: {},
  /**
   * 登录方法
   * @param username 用户名
   * @param password 密码
   * @param callback 登录后的回调
   * @returns {Promise<void>}
   */
  login(params, callback, failCallback) {
     ssoService.login(params).then(data => {
       let response = data.data || {};
       debugger
       if (response.access_token) {
         let rel = response;
         console.info(rel)
         this.refreshToken(rel)
         if (callback) {
           callback(rel);
         }
       }else {
         console.error(response)
         if (failCallback) {
           failCallback(response)
         }
       }
     }).catch(error => {
       console.error(error)
       if (failCallback) {
         failCallback(error)
       }
     })
  },
  setToken(token) {
    Cookies.set(config.key.cookie.token, token);
  },
  setUsername(username) {
    Cookies.set(config.key.cookie.nickname, username);
  },
  getToken() {
    return Cookies.get(config.key.cookie.token);
  },
  getUsername() {
    return Cookies.get(config.key.cookie.nickname);
  },
  getRefreshToken() {
    return Cookies.get(config.key.cookie.refreshToken);
  },
  /**
   * 定时刷新token
   * @param token
   */
  refreshToken(token) {
    console.info({"token": token})
    this.setToken(token.access_token);
    this.setUsername(token.nickname)
    Cookies.set(config.key.cookie.refreshToken, token.refresh_token);
    Cookies.set(config.key.cookie.expires_in, token.expires_in);
    this._clearRefreshTimeout();
    console.info({"refresh_step": 1})
    let that = this;
    this.refreshTimeout = setTimeout(function () {
      console.info({"refresh_step": 2})
      that._executeRefreshToken(token.refresh_token);
    }, (token.expires_in-100)*1000);
  },
  /**
   *
   * @private
   */
  async _executeRefreshToken (refresh_token) {
    console.info({ "refresh_step": 3, refresh_token })
    let rel = await ssoService.refreshToken(refresh_token);
    console.info({ "refresh_step": 4, rel: rel })
    if (rel.success) {
      console.info({ "refresh_step": 5 })
      this.refreshToken(rel.message);
    }
  },
  _clearRefreshTimeout() {
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout)
    }
  },
  /**
   * 登出
   */
  logout() {
    this._clearRefreshTimeout();
    this.setToken("");
    this.setUsername("")
    Cookies.set(config.key.cookie.refreshToken, "");
    Cookies.set(config.key.cookie.expires_in, "");

  },
  /**
   * 获取所有权限
   * @returns {Promise<void>}
   */
  async getAuth () {
    if (!this.refreshTimeout) {
      this._executeRefreshToken(this.getRefreshToken())
    }
    let rel = await ssoService.getAuthTree();
      let routes = {};
      this._initButtonsGroupMenu(rel)
      this._eachMenu(rel, routes);
      return routes;
  },
  _eachMenu(menus, routes) {
    for (let i = 0, len = menus.length; i < len; i++) {
      let _m = menus[i];
      if (_m.menuType != 3) {
        let _mcs = _m.children;
        if (_mcs && _mcs.length > 0) {
          this._eachMenu(_mcs, routes)
        }
        delete _m.children
        routes[_m.menuNo] = _m;
      }
    }
  },
  /**
   * 按菜单分组按钮
   * 返回按菜单编码分组的按钮集合
   */
  getButtonsGroupMenu() {
    return this.buttonsGroupMenu
  },
  _initButtonsGroupMenu(menus) {
    let buttons = {};
    this._eachButtons(menus, buttons)
    this.buttonsGroupMenu = buttons;
  },
  _eachButtons(menus, buttons) {
    for (let i = 0, len = menus.length; i < len; i++) {
      let _m = menus[i];
      let _mcs = _m.children || [];
      if (_m.menuType == 1) {
        if (_mcs && _mcs.length > 0){
          let _mbuttons = {};
          for (let j = 0, jlen = _mcs.length; j < jlen; j++) {
            let _mc = _mcs[j];
            if (_mc.menuType == 2) {
              _mbuttons[_mc.menuNo] = {name: _mc.menuName}
            }
          }
          buttons[_m.menuNo] = _mbuttons
        }
      }
      this._eachButtons(_mcs, buttons)
    }
  }

}

export default LoginUtil
