import SockJ<PERSON> from 'sockjs-client';
import {Stomp} from '@stomp/stompjs';

class WebSocket {

    constructor({subscribes, url}) {
        if (!subscribes || subscribes.length == 0) {
            throw("订阅为空，创建websocket连接失败!")
        }
        if (!url) {
            throw("websocket地址为空，创建websocket连接失败!")
        }
        if (!(url.indexOf("http://") == 0 || url.indexOf("https://") == 0)) {
            url = location.protocol + "//" + location.host + (url.indexOf("/") == 0 ? url : '/' + url);
        }
        // 下面的url是本地运行的jar包的websocket地址
        this.socket = new SockJS(url);
        this.stompClient = Stomp.over(this.socket);
        this.subscribes = subscribes;
        this._connect();
    }

    _connect() {
        this._reconnect();
        this.reconectInterval = setInterval(this._reconnect, 2000)
        this.stompClient.onclose = (res) => {
            console.info('Connection closed!');
            console.info(res);
        };
    }

    _reconnect() {
        this.stompClient.connect({}, (frame) => {
            if (this.reconectInterval) {
                clearInterval(this.reconectInterval);
            }
            console.info('Connected: ' + frame);
            this._subscribe();
        }, (error) => {
            console.info("connect error: " + error);
        });
    }
    _subscribe() {
        this.subscribes.forEach(subscribe => {
            this.stompClient.subscribe(subscribe.url, subscribe.success)
        })
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.stompClient != null) {
            this.stompClient.disconnect();
        }
    }

}

export default WebSocket;
